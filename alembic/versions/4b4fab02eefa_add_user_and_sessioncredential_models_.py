"""Add User and SessionCredential models for authentication

Revision ID: 4b4fab02eefa
Revises: d969b4113a0b
Create Date: 2025-07-03 00:20:49.842397

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4b4fab02eefa"
down_revision: Union[str, None] = "d969b4113a0b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "users",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("username", sa.String(length=50), nullable=False),
        sa.Column("hashed_password", sa.String(length=255), nullable=False),
        sa.Column(
            "role", sa.<PERSON>("ADMIN", "ANNOTATOR", name="userrole"), nullable=False
        ),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("last_login_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_users_id"), "users", ["id"], unique=False)
    op.create_index(op.f("ix_users_username"), "users", ["username"], unique=True)
    op.create_table(
        "session_credentials",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("session_id", sa.Integer(), nullable=False),
        sa.Column("username", sa.String(length=50), nullable=False),
        sa.Column("hashed_password", sa.String(length=255), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["session_id"],
            ["annotation_sessions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_session_credentials_id"), "session_credentials", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_session_credentials_session_id"),
        "session_credentials",
        ["session_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_session_credentials_username"),
        "session_credentials",
        ["username"],
        unique=False,
    )
    # Use batch mode for SQLite compatibility
    with op.batch_alter_table("annotation_sessions", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("created_by_user_id", sa.Integer(), nullable=True)
        )
        batch_op.create_index(
            op.f("ix_annotation_sessions_created_by_user_id"),
            ["created_by_user_id"],
            unique=False,
        )
        batch_op.create_foreign_key(
            "fk_annotation_sessions_created_by_user_id_users",
            "users",
            ["created_by_user_id"],
            ["id"],
        )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Use batch mode for SQLite compatibility
    with op.batch_alter_table("annotation_sessions", schema=None) as batch_op:
        batch_op.drop_constraint(
            "fk_annotation_sessions_created_by_user_id_users", type_="foreignkey"
        )
        batch_op.drop_index(op.f("ix_annotation_sessions_created_by_user_id"))
        batch_op.drop_column("created_by_user_id")
    op.drop_index(
        op.f("ix_session_credentials_username"), table_name="session_credentials"
    )
    op.drop_index(
        op.f("ix_session_credentials_session_id"), table_name="session_credentials"
    )
    op.drop_index(op.f("ix_session_credentials_id"), table_name="session_credentials")
    op.drop_table("session_credentials")
    op.drop_index(op.f("ix_users_username"), table_name="users")
    op.drop_index(op.f("ix_users_id"), table_name="users")
    op.drop_table("users")
    # ### end Alembic commands ###
