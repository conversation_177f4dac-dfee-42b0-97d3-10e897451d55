"""Add AdminCredential model for dashboard authentication

Revision ID: 9e41d50baaf8
Revises: b45dbb5a05af
Create Date: 2025-07-03 18:34:37.563974

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9e41d50baaf8"
down_revision: Union[str, None] = "b45dbb5a05af"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "admin_credentials",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("subdomain", sa.String(length=255), nullable=False),
        sa.Column("hashed_password", sa.String(length=255), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("last_login_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_admin_credentials_id"), "admin_credentials", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_admin_credentials_subdomain"),
        "admin_credentials",
        ["subdomain"],
        unique=True,
    )
    op.create_index(
        op.f("ix_annotation_sessions_annotator_name"),
        "annotation_sessions",
        ["annotator_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sessions_name"),
        "annotation_sessions",
        ["name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sessions_status"),
        "annotation_sessions",
        ["status"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_sessions_status"), table_name="annotation_sessions"
    )
    op.drop_index(op.f("ix_annotation_sessions_name"), table_name="annotation_sessions")
    op.drop_index(
        op.f("ix_annotation_sessions_annotator_name"), table_name="annotation_sessions"
    )
    op.drop_index(
        op.f("ix_admin_credentials_subdomain"), table_name="admin_credentials"
    )
    op.drop_index(op.f("ix_admin_credentials_id"), table_name="admin_credentials")
    op.drop_table("admin_credentials")
    # ### end Alembic commands ###
