"""Remove users table and user-related foreign keys

Revision ID: b45dbb5a05af
Revises: 0141fd73cd8e
Create Date: 2025-07-03 12:30:44.866599

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b45dbb5a05af"
down_revision: Union[str, None] = "0141fd73cd8e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # For SQLite, we need to recreate tables without the user-related columns
    # This is the safest approach for SQLite

    # First, create new annotation_sessions table without created_by_user_id
    op.execute("""
        CREATE TABLE annotation_sessions_new (
            id INTEGER NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            annotator_name VARCHAR(255) NOT NULL,
            annotator_email VARCHAR(255),
            query_filters JSON NOT NULL,
            status VARCHAR(11) NOT NULL,
            total_samples INTEGER NOT NULL,
            completed_samples INTEGER NOT NULL,
            correct_samples INTEGER NOT NULL,
            incorrect_samples INTEGER NOT NULL,
            skipped_samples INTEGER NOT NULL,
            created_at DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
            started_at DATETIME,
            completed_at DATETIME,
            last_activity_at DATETIME,
            PRIMARY KEY (id)
        )
    """)

    # Copy data from old table to new table (excluding created_by_user_id)
    op.execute("""
        INSERT INTO annotation_sessions_new (
            id, name, description, annotator_name, annotator_email, query_filters,
            status, total_samples, completed_samples, correct_samples, incorrect_samples,
            skipped_samples, created_at, started_at, completed_at, last_activity_at
        )
        SELECT
            id, name, description, annotator_name, annotator_email, query_filters,
            status, total_samples, completed_samples, correct_samples, incorrect_samples,
            skipped_samples, created_at, started_at, completed_at, last_activity_at
        FROM annotation_sessions
    """)

    # Drop old table and rename new table
    op.execute("DROP TABLE annotation_sessions")
    op.execute("ALTER TABLE annotation_sessions_new RENAME TO annotation_sessions")

    # Create new audit_logs table without user_id
    op.execute("""
        CREATE TABLE audit_logs_new (
            id INTEGER NOT NULL,
            event_type VARCHAR(17) NOT NULL,
            session_id INTEGER,
            username VARCHAR(50),
            ip_address VARCHAR(45),
            user_agent TEXT,
            details JSON,
            success BOOLEAN NOT NULL,
            error_message TEXT,
            timestamp DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL,
            PRIMARY KEY (id),
            FOREIGN KEY(session_id) REFERENCES annotation_sessions (id)
        )
    """)

    # Copy data from old table to new table (excluding user_id)
    op.execute("""
        INSERT INTO audit_logs_new (
            id, event_type, session_id, username, ip_address, user_agent,
            details, success, error_message, timestamp
        )
        SELECT
            id, event_type, session_id, username, ip_address, user_agent,
            details, success, error_message, timestamp
        FROM audit_logs
    """)

    # Drop old table and rename new table
    op.execute("DROP TABLE audit_logs")
    op.execute("ALTER TABLE audit_logs_new RENAME TO audit_logs")

    # Create indexes
    op.execute("CREATE INDEX ix_annotation_sessions_id ON annotation_sessions (id)")
    op.execute("CREATE INDEX ix_audit_logs_id ON audit_logs (id)")
    op.execute("CREATE INDEX ix_audit_logs_event_type ON audit_logs (event_type)")
    op.execute("CREATE INDEX ix_audit_logs_session_id ON audit_logs (session_id)")
    op.execute("CREATE INDEX ix_audit_logs_username ON audit_logs (username)")
    op.execute("CREATE INDEX ix_audit_logs_timestamp ON audit_logs (timestamp)")

    # Drop the users table
    op.drop_table("users")


def downgrade() -> None:
    """Downgrade schema."""
    # Recreate users table
    op.create_table(
        "users",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("username", sa.VARCHAR(length=50), nullable=False),
        sa.Column("hashed_password", sa.VARCHAR(length=255), nullable=False),
        sa.Column(
            "role", sa.Enum("ADMIN", "ANNOTATOR", name="userrole"), nullable=False
        ),
        sa.Column("is_active", sa.BOOLEAN(), nullable=False),
        sa.Column(
            "created_at",
            sa.DATETIME(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("last_login_at", sa.DATETIME(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_users_id", "users", ["id"], unique=False)
    op.create_index("ix_users_username", "users", ["username"], unique=True)

    # Add back columns and foreign key constraints
    with op.batch_alter_table("annotation_sessions", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("created_by_user_id", sa.INTEGER(), nullable=True)
        )
        batch_op.create_index(
            "ix_annotation_sessions_created_by_user_id",
            ["created_by_user_id"],
            unique=False,
        )
        batch_op.create_foreign_key(None, "users", ["created_by_user_id"], ["id"])

    with op.batch_alter_table("audit_logs", schema=None) as batch_op:
        batch_op.add_column(sa.Column("user_id", sa.INTEGER(), nullable=True))
        batch_op.create_index("ix_audit_logs_user_id", ["user_id"], unique=False)
        batch_op.create_foreign_key(None, "users", ["user_id"], ["id"])
