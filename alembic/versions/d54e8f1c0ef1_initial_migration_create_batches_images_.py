"""Initial migration: Create batches, images, and samples tables

Revision ID: d54e8f1c0ef1
Revises:
Create Date: 2025-06-09 11:48:23.846697

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d54e8f1c0ef1"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "batches",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_batches_id"), "batches", ["id"], unique=False)
    op.create_index(op.f("ix_batches_name"), "batches", ["name"], unique=False)
    op.create_table(
        "images",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("hash_value", sa.String(length=64), nullable=False),
        sa.Column("file_path", sa.String(length=500), nullable=False),
        sa.Column("file_size", sa.Integer(), nullable=False),
        sa.Column("width", sa.Integer(), nullable=True),
        sa.Column("height", sa.Integer(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_images_hash_value"), "images", ["hash_value"], unique=True)
    op.create_index(op.f("ix_images_id"), "images", ["id"], unique=False)
    op.create_table(
        "samples",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("image_id", sa.Integer(), nullable=False),
        sa.Column("batch_id", sa.Integer(), nullable=False),
        sa.Column(
            "mode",
            sa.Enum(
                "GROUNDING",
                "DESCRIBE",
                "ENUMERATE_TEXT",
                "ENUMERATE_COORD",
                "CHECKLIST",
                "ENSURE",
                name="samplemode",
            ),
            nullable=False,
        ),
        sa.Column("sample_metadata", sa.JSON(), nullable=False),
        sa.Column("labels", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["batch_id"],
            ["batches.id"],
        ),
        sa.ForeignKeyConstraint(
            ["image_id"],
            ["images.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_samples_batch_id"), "samples", ["batch_id"], unique=False)
    op.create_index(op.f("ix_samples_id"), "samples", ["id"], unique=False)
    op.create_index(op.f("ix_samples_image_id"), "samples", ["image_id"], unique=False)
    op.create_index(op.f("ix_samples_mode"), "samples", ["mode"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_samples_mode"), table_name="samples")
    op.drop_index(op.f("ix_samples_image_id"), table_name="samples")
    op.drop_index(op.f("ix_samples_id"), table_name="samples")
    op.drop_index(op.f("ix_samples_batch_id"), table_name="samples")
    op.drop_table("samples")
    op.drop_index(op.f("ix_images_id"), table_name="images")
    op.drop_index(op.f("ix_images_hash_value"), table_name="images")
    op.drop_table("images")
    op.drop_index(op.f("ix_batches_name"), table_name="batches")
    op.drop_index(op.f("ix_batches_id"), table_name="batches")
    op.drop_table("batches")
    # ### end Alembic commands ###
