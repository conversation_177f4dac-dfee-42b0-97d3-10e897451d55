"""Add annotation apply log table

Revision ID: d969b4113a0b
Revises: ff07e000ba50
Create Date: 2025-07-01 19:51:15.661989

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d969b4113a0b"
down_revision: Union[str, None] = "ff07e000ba50"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "annotation_apply_logs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("operation_id", sa.String(length=36), nullable=False),
        sa.Column("applied_at", sa.DateTime(), nullable=False),
        sa.Column("applied_by", sa.String(length=255), nullable=False),
        sa.Column("source_file", sa.String(length=500), nullable=False),
        sa.Column("sample_id", sa.Integer(), nullable=False),
        sa.Column("original_metadata", sa.JSON(), nullable=False),
        sa.Column("new_metadata", sa.JSON(), nullable=False),
        sa.Column("operation_status", sa.String(length=20), nullable=False),
        sa.Column("rollback_at", sa.DateTime(), nullable=True),
        sa.Column("rollback_by", sa.String(length=255), nullable=True),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["sample_id"],
            ["samples.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_annotation_apply_logs_applied_at"),
        "annotation_apply_logs",
        ["applied_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_apply_logs_id"),
        "annotation_apply_logs",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_apply_logs_operation_id"),
        "annotation_apply_logs",
        ["operation_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_apply_logs_sample_id"),
        "annotation_apply_logs",
        ["sample_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_apply_logs_sample_id"), table_name="annotation_apply_logs"
    )
    op.drop_index(
        op.f("ix_annotation_apply_logs_operation_id"),
        table_name="annotation_apply_logs",
    )
    op.drop_index(
        op.f("ix_annotation_apply_logs_id"), table_name="annotation_apply_logs"
    )
    op.drop_index(
        op.f("ix_annotation_apply_logs_applied_at"), table_name="annotation_apply_logs"
    )
    op.drop_table("annotation_apply_logs")
    # ### end Alembic commands ###
