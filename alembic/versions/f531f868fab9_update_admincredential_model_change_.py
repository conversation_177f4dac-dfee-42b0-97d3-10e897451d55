"""Update AdminCredential model - change subdomain to dashboard_path

Revision ID: f531f868fab9
Revises: 9e41d50baaf8
Create Date: 2025-07-03 18:46:34.172698

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f531f868fab9"
down_revision: Union[str, None] = "9e41d50baaf8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Custom migration to handle SQLite limitations

    # Step 1: Add dashboard_path column as nullable first
    op.add_column(
        "admin_credentials",
        sa.Column("dashboard_path", sa.String(length=255), nullable=True),
    )

    # Step 2: Migrate data from subdomain to dashboard_path
    connection = op.get_bind()
    connection.execute(
        sa.text("UPDATE admin_credentials SET dashboard_path = subdomain")
    )

    # Step 3: Make dashboard_path NOT NULL and unique
    # For SQLite, we need to recreate the table
    with op.batch_alter_table("admin_credentials") as batch_op:
        batch_op.alter_column("dashboard_path", nullable=False)
        batch_op.drop_index("ix_admin_credentials_subdomain")
        batch_op.create_index(
            "ix_admin_credentials_dashboard_path", ["dashboard_path"], unique=True
        )
        batch_op.drop_column("subdomain")


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "admin_credentials",
        sa.Column("subdomain", sa.VARCHAR(length=255), nullable=False),
    )
    op.drop_index(
        op.f("ix_admin_credentials_dashboard_path"), table_name="admin_credentials"
    )
    op.create_index(
        op.f("ix_admin_credentials_subdomain"),
        "admin_credentials",
        ["subdomain"],
        unique=True,
    )
    op.drop_column("admin_credentials", "dashboard_path")
    # ### end Alembic commands ###
