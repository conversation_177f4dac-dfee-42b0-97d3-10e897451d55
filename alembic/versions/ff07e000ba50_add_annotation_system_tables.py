"""Add annotation system tables

Revision ID: ff07e000ba50
Revises: d54e8f1c0ef1
Create Date: 2025-06-26 01:06:22.065772

"""

from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ff07e000ba50"
down_revision: Union[str, None] = "d54e8f1c0ef1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "annotation_sessions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("annotator_name", sa.String(length=255), nullable=False),
        sa.Column("annotator_email", sa.String(length=255), nullable=True),
        sa.Column("query_filters", sa.JSON(), nullable=False),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING", "IN_PROGRESS", "COMPLETED", "PAUSED", name="annotationstatus"
            ),
            nullable=False,
        ),
        sa.Column("total_samples", sa.Integer(), nullable=False),
        sa.Column("completed_samples", sa.Integer(), nullable=False),
        sa.Column("correct_samples", sa.Integer(), nullable=False),
        sa.Column("incorrect_samples", sa.Integer(), nullable=False),
        sa.Column("skipped_samples", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("started_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_activity_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_annotation_sessions_annotator_name"),
        "annotation_sessions",
        ["annotator_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sessions_id"), "annotation_sessions", ["id"], unique=False
    )
    op.create_index(
        op.f("ix_annotation_sessions_name"),
        "annotation_sessions",
        ["name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sessions_status"),
        "annotation_sessions",
        ["status"],
        unique=False,
    )
    op.create_table(
        "annotation_sample_results",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("session_id", sa.Integer(), nullable=False),
        sa.Column("sample_id", sa.Integer(), nullable=False),
        sa.Column(
            "result",
            sa.Enum("CORRECT", "INCORRECT", "SKIPPED", name="annotationresult"),
            nullable=False,
        ),
        sa.Column("original_metadata", sa.JSON(), nullable=False),
        sa.Column("corrected_metadata", sa.JSON(), nullable=True),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column(
            "annotated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("annotation_duration", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["sample_id"],
            ["samples.id"],
        ),
        sa.ForeignKeyConstraint(
            ["session_id"],
            ["annotation_sessions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("session_id", "sample_id", name="uq_session_sample"),
    )
    op.create_index(
        op.f("ix_annotation_sample_results_id"),
        "annotation_sample_results",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sample_results_result"),
        "annotation_sample_results",
        ["result"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sample_results_sample_id"),
        "annotation_sample_results",
        ["sample_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_annotation_sample_results_session_id"),
        "annotation_sample_results",
        ["session_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_annotation_sample_results_session_id"),
        table_name="annotation_sample_results",
    )
    op.drop_index(
        op.f("ix_annotation_sample_results_sample_id"),
        table_name="annotation_sample_results",
    )
    op.drop_index(
        op.f("ix_annotation_sample_results_result"),
        table_name="annotation_sample_results",
    )
    op.drop_index(
        op.f("ix_annotation_sample_results_id"), table_name="annotation_sample_results"
    )
    op.drop_table("annotation_sample_results")
    op.drop_index(
        op.f("ix_annotation_sessions_status"), table_name="annotation_sessions"
    )
    op.drop_index(op.f("ix_annotation_sessions_name"), table_name="annotation_sessions")
    op.drop_index(op.f("ix_annotation_sessions_id"), table_name="annotation_sessions")
    op.drop_index(
        op.f("ix_annotation_sessions_annotator_name"), table_name="annotation_sessions"
    )
    op.drop_table("annotation_sessions")
    # ### end Alembic commands ###
