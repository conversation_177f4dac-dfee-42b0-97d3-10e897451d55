import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { adminApi } from '../services/api';

const AdminPathHandler: React.FC = () => {
  const { dashboardPath } = useParams<{ dashboardPath: string }>();
  const [isValidAdminPath, setIsValidAdminPath] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminPath = async () => {
      if (!dashboardPath) {
        setIsValidAdminPath(false);
        setLoading(false);
        return;
      }

      try {
        // Check if this path corresponds to a valid admin dashboard
        const response = await adminApi.getAdminInfo();
        const adminInfo = response.data;
        
        if (adminInfo.dashboard_path === dashboardPath) {
          setIsValidAdminPath(true);
        } else {
          setIsValidAdminPath(false);
        }
      } catch (error) {
        // If admin info endpoint fails, assume it's not an admin path
        setIsValidAdminPath(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminPath();
  }, [dashboardPath]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Checking access...</span>
        </div>
      </div>
    );
  }

  if (isValidAdminPath) {
    // Redirect to admin login
    return <Navigate to={`/${dashboardPath}/login`} replace />;
  } else {
    // Redirect to annotator login
    return <Navigate to="/login" replace />;
  }
};

export default AdminPathHandler;
