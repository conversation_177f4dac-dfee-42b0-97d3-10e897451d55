import React from 'react';
import { Navigate, useParams } from 'react-router-dom';
import { useAdminAuth } from '../contexts/AdminAuthContext';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { dashboardPath } = useParams<{ dashboardPath: string }>();
  const { isAuthenticated, adminCredentials, loading } = useAdminAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Verifying admin access...</span>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !adminCredentials) {
    return <Navigate to={`/${dashboardPath}/login`} replace />;
  }

  // Verify that the dashboard path matches the authenticated path
  if (adminCredentials.dashboardPath !== dashboardPath) {
    return <Navigate to={`/${adminCredentials.dashboardPath}/login`} replace />;
  }

  // Render protected content
  return <>{children}</>;
};

export default AdminProtectedRoute;
