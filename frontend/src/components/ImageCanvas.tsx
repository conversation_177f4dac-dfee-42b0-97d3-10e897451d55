import React, { useRef, useEffect, useState } from 'react';
import { pixelToDisplay } from '../utils/coordinates';

interface BoundingBox {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  label?: string;
  color?: string;
}

interface NormalizedBoundingBox {
  coordinate: { x1: number; y1: number; x2: number; y2: number };
  label?: string;
  color?: string;
}

interface ImageCanvasProps {
  imageUrl: string;
  boundingBoxes?: (BoundingBox | NormalizedBoundingBox)[];
  className?: string;
  onImageLoad?: (dimensions: { width: number; height: number }) => void;
}

const ImageCanvas: React.FC<ImageCanvasProps> = ({
  imageUrl,
  boundingBoxes = [],
  className = '',
  onImageLoad,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const [canvasDimensions, setCanvasDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const image = new Image();
    image.crossOrigin = 'anonymous';
    image.onload = () => {
      setImageDimensions({ width: image.width, height: image.height });
      if (onImageLoad) {
        onImageLoad({ width: image.width, height: image.height });
      }
      drawImageAndBoxes(image);
    };
    image.src = imageUrl;
    imageRef.current = image;
  }, [imageUrl]);

  useEffect(() => {
    if (imageRef.current && imageRef.current.complete) {
      drawImageAndBoxes(imageRef.current);
    }
  }, [boundingBoxes, canvasDimensions]);

  const drawImageAndBoxes = (image: HTMLImageElement) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Calculate display dimensions while maintaining aspect ratio - smaller for better layout
    const containerWidth = canvas.parentElement?.clientWidth || 600;
    const maxWidth = Math.min(containerWidth, 500); // Reduced from 800 to 500
    const aspectRatio = image.height / image.width;
    const displayWidth = maxWidth;
    const displayHeight = displayWidth * aspectRatio;

    // Set canvas size
    canvas.width = displayWidth;
    canvas.height = displayHeight;
    setCanvasDimensions({ width: displayWidth, height: displayHeight });

    // Clear canvas
    ctx.clearRect(0, 0, displayWidth, displayHeight);

    // Draw image
    ctx.drawImage(image, 0, 0, displayWidth, displayHeight);

    // Draw bounding boxes
    boundingBoxes.forEach((box, index) => {
      drawBoundingBox(ctx, box, image.width, image.height, displayWidth, displayHeight, index);
    });
  };

  const drawBoundingBox = (
    ctx: CanvasRenderingContext2D,
    box: BoundingBox | NormalizedBoundingBox,
    originalWidth: number,
    originalHeight: number,
    displayWidth: number,
    displayHeight: number,
    index: number
  ) => {
    let x1: number, y1: number, x2: number, y2: number;

    // Check if this is a pixel coordinate bounding box or legacy format
    if ('coordinate' in box) {
      // New pixel coordinate format
      const displayCoord = pixelToDisplay(
        box.coordinate,
        { width: originalWidth, height: originalHeight },
        { width: displayWidth, height: displayHeight }
      );
      x1 = displayCoord.x1;
      y1 = displayCoord.y1;
      x2 = displayCoord.x2;
      y2 = displayCoord.y2;
    } else {
      // Legacy format - assume already in display coordinates
      const scaleX = displayWidth / originalWidth;
      const scaleY = displayHeight / originalHeight;
      x1 = box.x1 * scaleX;
      y1 = box.y1 * scaleY;
      x2 = box.x2 * scaleX;
      y2 = box.y2 * scaleY;
    }

    const width = x2 - x1;
    const height = y2 - y1;

    // Set box style
    const color = box.color || getDefaultColor(index);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.fillStyle = color + '20'; // Semi-transparent fill

    // Draw rectangle
    ctx.fillRect(x1, y1, width, height);
    ctx.strokeRect(x1, y1, width, height);

    // Draw label if provided
    if (box.label) {
      ctx.fillStyle = color;
      ctx.font = '12px Arial';
      ctx.textBaseline = 'top';
      
      // Background for text
      const textMetrics = ctx.measureText(box.label);
      const textWidth = textMetrics.width;
      const textHeight = 16;
      
      ctx.fillStyle = color;
      ctx.fillRect(x1, y1 - textHeight - 2, textWidth + 8, textHeight + 4);
      
      // Text
      ctx.fillStyle = 'white';
      ctx.fillText(box.label, x1 + 4, y1 - textHeight + 2);
    }
  };

  const getDefaultColor = (index: number): string => {
    const colors = [
      '#ef4444', // red
      '#3b82f6', // blue
      '#10b981', // green
      '#f59e0b', // yellow
      '#8b5cf6', // purple
      '#06b6d4', // cyan
      '#f97316', // orange
      '#84cc16', // lime
    ];
    return colors[index % colors.length];
  };

  const handleResize = () => {
    if (imageRef.current && imageRef.current.complete) {
      drawImageAndBoxes(imageRef.current);
    }
  };

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-auto border border-gray-300 rounded-lg"
        style={{ maxWidth: '100%', height: 'auto' }}
      />
      {imageDimensions.width > 0 && (
        <div className="mt-2 text-xs text-gray-500">
          Original: {imageDimensions.width} × {imageDimensions.height}
          {canvasDimensions.width > 0 && (
            <span className="ml-2">
              Display: {Math.round(canvasDimensions.width)} × {Math.round(canvasDimensions.height)}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default ImageCanvas;
