import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  PixelCoordinate,
  ImageDimensions,
  pixelToDisplay,
  displayToPixel,
  normalizeCoordinateOrder,
  isValidCoordinate,
  clampCoordinates,
} from '../utils/coordinates';

interface BoundingBox {
  id: string;
  coordinate: PixelCoordinate;
  label?: string;
  color?: string;
  isSelected?: boolean;
}

interface InteractiveImageCanvasProps {
  imageUrl: string;
  boundingBoxes?: BoundingBox[];
  isEditable?: boolean;
  allowMultiple?: boolean;
  onBoundingBoxChange?: (boxes: BoundingBox[]) => void;
  onBoundingBoxAdd?: (box: BoundingBox) => void;
  onBoundingBoxUpdate?: (box: BoundingBox) => void;
  onBoundingBoxDelete?: (boxId: string) => void;
  className?: string;
}

const InteractiveImageCanvas: React.FC<InteractiveImageCanvasProps> = ({
  imageUrl,
  boundingBoxes = [],
  isEditable = false,
  allowMultiple = false,
  onBoundingBoxChange,
  onBoundingBoxAdd,
  onBoundingBoxDelete,
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [imageDimensions, setImageDimensions] = useState<ImageDimensions>({ width: 0, height: 0 });
  const [displayDimensions, setDisplayDimensions] = useState<ImageDimensions>({ width: 0, height: 0 });
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentBox, setCurrentBox] = useState<PixelCoordinate | null>(null);
  const [selectedBoxId, setSelectedBoxId] = useState<string | null>(null);

  // Load and setup image
  useEffect(() => {
    const image = new Image();
    image.crossOrigin = 'anonymous';
    image.onload = () => {
      setImageDimensions({ width: image.width, height: image.height });
      drawCanvas(image);
    };
    image.src = imageUrl;
    imageRef.current = image;
  }, [imageUrl]);

  // Calculate display dimensions when image loads
  useEffect(() => {
    if (imageDimensions.width > 0 && imageDimensions.height > 0) {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const containerWidth = canvas.parentElement?.clientWidth || 600;
      const maxWidth = Math.min(containerWidth, 400); // Reduced from 500 to 400 for easier annotations
      const aspectRatio = imageDimensions.height / imageDimensions.width;
      const displayWidth = maxWidth;
      const displayHeight = displayWidth * aspectRatio;

      setDisplayDimensions({ width: displayWidth, height: displayHeight });
    }
  }, [imageDimensions]);

  // Redraw when bounding boxes change
  useEffect(() => {
    if (imageRef.current && imageRef.current.complete && displayDimensions.width > 0) {
      drawCanvas(imageRef.current);
    }
  }, [boundingBoxes, displayDimensions, selectedBoxId, currentBox, isDrawing]);

  const drawCanvas = useCallback((image: HTMLImageElement) => {
    const canvas = canvasRef.current;
    if (!canvas || displayDimensions.width === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match display dimensions
    canvas.width = displayDimensions.width;
    canvas.height = displayDimensions.height;

    // Clear and draw image
    ctx.clearRect(0, 0, displayDimensions.width, displayDimensions.height);
    ctx.drawImage(image, 0, 0, displayDimensions.width, displayDimensions.height);

    // Draw existing bounding boxes
    boundingBoxes.forEach((box) => {
      drawBoundingBox(ctx, box, image);
    });

    // Draw current drawing box
    if (currentBox && isDrawing) {
      drawCurrentBox(ctx, currentBox);
    }
  }, [boundingBoxes, currentBox, isDrawing, displayDimensions]);

  const drawBoundingBox = (ctx: CanvasRenderingContext2D, box: BoundingBox, image: HTMLImageElement) => {
    const displayCoord = pixelToDisplay(
      box.coordinate,
      { width: image.width, height: image.height },
      displayDimensions
    );

    const width = displayCoord.x2 - displayCoord.x1;
    const height = displayCoord.y2 - displayCoord.y1;

    // Set style
    const color = box.color || getDefaultColor(0);
    const isSelected = box.isSelected || box.id === selectedBoxId;
    
    ctx.strokeStyle = isSelected ? '#ef4444' : color;
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.fillStyle = (isSelected ? '#ef4444' : color) + '20';

    // Draw rectangle
    ctx.fillRect(displayCoord.x1, displayCoord.y1, width, height);
    ctx.strokeRect(displayCoord.x1, displayCoord.y1, width, height);

    // Draw label
    if (box.label) {
      ctx.fillStyle = isSelected ? '#ef4444' : color;
      ctx.font = '12px Arial';
      ctx.textBaseline = 'top';
      
      const textMetrics = ctx.measureText(box.label);
      const textWidth = textMetrics.width;
      const textHeight = 16;
      
      ctx.fillRect(displayCoord.x1, displayCoord.y1 - textHeight - 2, textWidth + 8, textHeight + 4);
      ctx.fillStyle = 'white';
      ctx.fillText(box.label, displayCoord.x1 + 4, displayCoord.y1 - textHeight + 2);
    }

    // Draw resize handles if selected
    if (isSelected && isEditable) {
      drawResizeHandles(ctx, displayCoord);
    }
  };

  const drawCurrentBox = (ctx: CanvasRenderingContext2D, box: PixelCoordinate) => {
    const width = box.x2 - box.x1;
    const height = box.y2 - box.y1;

    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.fillStyle = '#3b82f620';

    ctx.fillRect(box.x1, box.y1, width, height);
    ctx.strokeRect(box.x1, box.y1, width, height);
    ctx.setLineDash([]);
  };

  const drawResizeHandles = (ctx: CanvasRenderingContext2D, coord: PixelCoordinate) => {
    const handleSize = 6;
    ctx.fillStyle = '#ef4444';
    
    // Corner handles
    const handles = [
      { x: coord.x1 - handleSize/2, y: coord.y1 - handleSize/2 }, // top-left
      { x: coord.x2 - handleSize/2, y: coord.y1 - handleSize/2 }, // top-right
      { x: coord.x1 - handleSize/2, y: coord.y2 - handleSize/2 }, // bottom-left
      { x: coord.x2 - handleSize/2, y: coord.y2 - handleSize/2 }, // bottom-right
    ];
    
    handles.forEach(handle => {
      ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
    });
  };

  const getDefaultColor = (index: number): string => {
    const colors = ['#ef4444', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'];
    return colors[index % colors.length];
  };

  const getMousePosition = (event: React.MouseEvent<HTMLCanvasElement>): { x: number; y: number } => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();

    // Calculate the scale factor between canvas display size and actual canvas size
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    return {
      x: (event.clientX - rect.left) * scaleX,
      y: (event.clientY - rect.top) * scaleY,
    };
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isEditable) return;

    const pos = getMousePosition(event);
    
    // Check if clicking on existing box
    const clickedBox = findBoxAtPosition(pos);
    if (clickedBox) {
      setSelectedBoxId(clickedBox.id);
      return;
    }

    // Start drawing new box
    // Allow drawing if: multiple boxes allowed OR no boxes exist OR single box mode (replace existing)
    if (allowMultiple || boundingBoxes.length === 0 || !allowMultiple) {
      setIsDrawing(true);
      setCurrentBox({ x1: pos.x, y1: pos.y, x2: pos.x, y2: pos.y });
      setSelectedBoxId(null);
    }
  };

  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isEditable || !isDrawing || !currentBox) return;

    const pos = getMousePosition(event);
    setCurrentBox({ ...currentBox, x2: pos.x, y2: pos.y });
    
    if (imageRef.current && imageRef.current.complete) {
      drawCanvas(imageRef.current);
    }
  };

  const handleMouseUp = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isEditable || !isDrawing || !currentBox) return;

    const pos = getMousePosition(event);
    const finalBox = { ...currentBox, x2: pos.x, y2: pos.y };

    // Convert to pixel coordinates
    const pixelCoord = displayToPixel(
      finalBox,
      imageDimensions,
      displayDimensions
    );

    const orderedCoord = normalizeCoordinateOrder(pixelCoord);
    const clampedCoord = clampCoordinates(orderedCoord, imageDimensions);

    // Only add if box is valid size
    if (isValidCoordinate(clampedCoord)) {
      const newBox: BoundingBox = {
        id: `box-${Date.now()}`,
        coordinate: clampedCoord,
        label: `Box ${boundingBoxes.length + 1}`,
      };

      if (onBoundingBoxAdd) {
        onBoundingBoxAdd(newBox);
      }

      if (onBoundingBoxChange) {
        if (allowMultiple) {
          // Multiple boxes allowed: add to existing boxes
          onBoundingBoxChange([...boundingBoxes, newBox]);
        } else {
          // Single box mode: replace existing box
          onBoundingBoxChange([newBox]);
        }
      }
    }

    setIsDrawing(false);
    setCurrentBox(null);
  };

  const findBoxAtPosition = (pos: { x: number; y: number }): BoundingBox | null => {
    for (const box of boundingBoxes) {
      const displayCoord = pixelToDisplay(
        box.coordinate,
        imageDimensions,
        displayDimensions
      );

      if (
        pos.x >= displayCoord.x1 &&
        pos.x <= displayCoord.x2 &&
        pos.y >= displayCoord.y1 &&
        pos.y <= displayCoord.y2
      ) {
        return box;
      }
    }
    return null;
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Delete' && selectedBoxId && onBoundingBoxDelete) {
      onBoundingBoxDelete(selectedBoxId);
      setSelectedBoxId(null);
    }
  };

  return (
    <div className={`relative ${className}`} tabIndex={0} onKeyDown={handleKeyDown}>
      <canvas
        ref={canvasRef}
        className="w-full h-auto border border-gray-300 rounded-lg cursor-crosshair"
        style={{ maxWidth: '100%', height: 'auto' }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      />
      
      {imageDimensions.width > 0 && (
        <div className="mt-2 text-xs text-gray-500">
          Original: {imageDimensions.width} × {imageDimensions.height}
          {displayDimensions.width > 0 && (
            <span className="ml-2">
              Display: {Math.round(displayDimensions.width)} × {Math.round(displayDimensions.height)}
            </span>
          )}
        </div>
      )}
      
      {isEditable && (
        <div className="mt-2 text-xs text-gray-600">
          {allowMultiple ? 'Click and drag to draw multiple boxes' : 'Click and drag to draw a box'}
          {selectedBoxId && ' • Press Delete to remove selected box'}
        </div>
      )}
    </div>
  );
};

export default InteractiveImageCanvas;
