import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import LanguageSelector from './LanguageSelector';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const { sessionCredentials, logout } = useAuth();
  const { t } = useTranslation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Hide navigation for annotation workspace to save space
  const isAnnotationWorkspace = location.pathname.includes('/annotate');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header - Hidden during annotation */}
      {!isAnnotationWorkspace && (
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <h1 className="text-xl font-bold text-gray-900">
                    {t('navigation.appTitle')}
                  </h1>
                </div>
                <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                  <Link
                    to="/sessions"
                    className={`${
                      isActive('/sessions')
                        ? 'border-blue-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                  >
                    {t('navigation.mySession')}
                  </Link>
                </div>
              </div>

              {/* User info and logout */}
              <div className="flex items-center space-x-4">
                <LanguageSelector />
                <span className="text-sm text-gray-700">
                  {sessionCredentials?.username}
                </span>
                <button
                  onClick={logout}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  {t('common.logout')}
                </button>
              </div>
            </div>
          </div>
        </nav>
      )}

      {/* Main Content */}
      <main className={`${isAnnotationWorkspace ? 'max-w-full' : 'max-w-7xl mx-auto'} ${isAnnotationWorkspace ? 'py-2' : 'py-6'} sm:px-6 lg:px-8`}>
        {children}
      </main>
    </div>
  );
};

export default Layout;
