import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSessionId?: number; // If specified, user must be authenticated for this specific session
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requireSessionId }) => {
  const { isAuthenticated, sessionCredentials, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If a specific session is required, check if user has access to it
  if (requireSessionId && sessionCredentials?.sessionId !== requireSessionId) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center">
          <div className="bg-red-50 border border-red-200 rounded-md p-6">
            <h3 className="text-lg font-medium text-red-800 mb-2">Access Denied</h3>
            <p className="text-sm text-red-600 mb-4">
              You don't have access to this session. You can only access session #{sessionCredentials?.sessionId}.
            </p>
            <Navigate to={`/sessions/${sessionCredentials?.sessionId}/annotate`} replace />
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
