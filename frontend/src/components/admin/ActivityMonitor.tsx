import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/api';

interface Activity {
  type: string;
  timestamp: string;
  session_id?: number;
  session_name?: string;
  annotator_name?: string;
  result?: string;
  sample_id?: number;
  duration?: number;
  has_corrections?: boolean;
  username?: string;
  ip_address?: string;
  success?: boolean;
  error_message?: string;
}

interface ActivityStats {
  total_annotations: number;
  active_sessions: number;
  active_annotators: number;
  avg_annotations_per_hour: number;
}

interface ActivityData {
  time_range: {
    hours: number;
    from: string;
    to: string;
  };
  statistics: ActivityStats;
  activities: Activity[];
}

const ActivityMonitor: React.FC = () => {
  const [data, setData] = useState<ActivityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState(24);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchActivityData = async () => {
    try {
      setError(null);
      const response = await adminApi.getRecentActivity(timeRange, 100);
      setData(response.data);
    } catch (err) {
      setError('Failed to load activity data');
      console.error('Activity data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivityData();
  }, [timeRange]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchActivityData, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, [autoRefresh, timeRange]);

  const getActivityIcon = (activity: Activity) => {
    switch (activity.type) {
      case 'annotation':
        return activity.result === 'correct' ? '✅' : activity.result === 'incorrect' ? '❌' : '⏭️';
      case 'login':
        return '🔐';
      case 'login_failed':
        return '🚫';
      case 'access':
        return '👁️';
      default:
        return '📝';
    }
  };

  const getActivityDescription = (activity: Activity) => {
    switch (activity.type) {
      case 'annotation':
        return (
          <div>
            <span className="font-medium">{activity.annotator_name}</span> annotated sample{' '}
            <span className="font-mono text-sm">#{activity.sample_id}</span> as{' '}
            <span className={`font-medium ${
              activity.result === 'correct' ? 'text-green-600' :
              activity.result === 'incorrect' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {activity.result}
            </span>
            {activity.has_corrections && <span className="text-blue-600 ml-1">(with corrections)</span>}
            {activity.duration && (
              <span className="text-gray-500 text-sm ml-2">
                ({activity.duration}s)
              </span>
            )}
          </div>
        );
      case 'login':
        return (
          <div>
            <span className="font-medium">{activity.username}</span> logged in successfully
            <span className="text-gray-500 text-sm ml-2">from {activity.ip_address}</span>
          </div>
        );
      case 'login_failed':
        return (
          <div>
            Failed login attempt for <span className="font-medium">{activity.username}</span>
            <span className="text-gray-500 text-sm ml-2">from {activity.ip_address}</span>
            {activity.error_message && (
              <div className="text-red-600 text-sm">{activity.error_message}</div>
            )}
          </div>
        );
      case 'access':
        return (
          <div>
            <span className="font-medium">{activity.username}</span> accessed session
            <span className="text-gray-500 text-sm ml-2">from {activity.ip_address}</span>
          </div>
        );
      default:
        return <div>Unknown activity type: {activity.type}</div>;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading activity...</span>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error || 'No data available'}</div>
        <button
          onClick={fetchActivityData}
          className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium text-gray-900">Activity Monitor</h2>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
          >
            <option value={1}>Last 1 hour</option>
            <option value={6}>Last 6 hours</option>
            <option value={24}>Last 24 hours</option>
            <option value={72}>Last 3 days</option>
            <option value={168}>Last week</option>
          </select>
          <label className="flex items-center text-sm">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <span className="ml-2">Auto-refresh</span>
          </label>
          <button
            onClick={fetchActivityData}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* Activity Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📝</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Annotations</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.statistics.total_annotations}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🎯</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Sessions</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.statistics.active_sessions}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Annotators</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.statistics.active_annotators}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">⚡</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Avg/Hour</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.statistics.avg_annotations_per_hour}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Activity Feed */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Recent Activity ({data.time_range.hours}h)
          </h3>
          <p className="text-sm text-gray-500">
            {new Date(data.time_range.from).toLocaleString()} - {new Date(data.time_range.to).toLocaleString()}
          </p>
        </div>
        <div className="max-h-96 overflow-y-auto">
          {data.activities.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {data.activities.map((activity, index) => (
                <div key={index} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <span className="text-lg">{getActivityIcon(activity)}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm text-gray-900">
                        {getActivityDescription(activity)}
                      </div>
                      {activity.session_name && (
                        <div className="text-xs text-gray-500 mt-1">
                          Session: {activity.session_name}
                        </div>
                      )}
                    </div>
                    <div className="flex-shrink-0 text-xs text-gray-500">
                      {formatTimestamp(activity.timestamp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500">No recent activity</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivityMonitor;
