import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/api';

interface SystemOverview {
  total_batches: number;
  total_samples: number;
  unique_images: number;
  total_sessions: number;
  active_sessions: number;
  total_annotations: number;
  annotations_today: number;
}

interface StatusDistribution {
  status: string;
  count: number;
  percentage: number;
}

interface ModeDistribution {
  mode: string;
  mode_display: string;
  count: number;
  percentage: number;
}

interface TopSession {
  session_id: number;
  session_name: string;
  annotator_name: string;
  completion_rate: number;
  completed_samples: number;
  total_samples: number;
  status: string;
  last_activity: string | null;
}

interface RecentActivity {
  annotations_last_24h: number;
  active_annotators: number;
  avg_annotations_per_hour: number;
}

interface DashboardData {
  system_overview: SystemOverview;
  session_status_distribution: StatusDistribution[];
  mode_distribution: ModeDistribution[];
  top_sessions: TopSession[];
  recent_activity: RecentActivity;
}

const DashboardOverview: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchDashboardData = async () => {
    try {
      setError(null);
      const response = await adminApi.getDashboardOverview();
      setData(response.data);
      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading overview...</span>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error || 'No data available'}</div>
        <button
          onClick={fetchDashboardData}
          className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  const { system_overview, session_status_distribution, mode_distribution, top_sessions, recent_activity } = data;

  return (
    <div className="space-y-6">
      {/* Header with refresh info */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium text-gray-900">System Overview</h2>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <span>Last updated: {lastUpdated?.toLocaleTimeString()}</span>
          <button
            onClick={fetchDashboardData}
            className="text-blue-600 hover:text-blue-800"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* System Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📦</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Batches</dt>
                  <dd className="text-lg font-medium text-gray-900">{system_overview.total_batches}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🖼️</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Samples</dt>
                  <dd className="text-lg font-medium text-gray-900">{system_overview.total_samples}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Sessions</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {system_overview.active_sessions} / {system_overview.total_sessions}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Annotations Today</dt>
                  <dd className="text-lg font-medium text-gray-900">{system_overview.annotations_today}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Status Distribution */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Status Distribution</h3>
          <div className="space-y-3">
            {session_status_distribution.map((item) => (
              <div key={item.status} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-blue-500 mr-3"></div>
                  <span className="text-sm text-gray-700 capitalize">
                    {item.status.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900">{item.count}</span>
                  <span className="text-sm text-gray-500">({item.percentage}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Mode Distribution */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Annotation Mode Distribution</h3>
          <div className="space-y-3">
            {mode_distribution.map((item) => (
              <div key={item.mode} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 mr-3"></div>
                  <span className="text-sm text-gray-700">{item.mode_display}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900">{item.count}</span>
                  <span className="text-sm text-gray-500">({item.percentage}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Performing Sessions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Top Performing Sessions</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Session
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Annotator
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Activity
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {top_sessions.slice(0, 5).map((session) => (
                <tr key={session.session_id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {session.session_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {session.annotator_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${session.completion_rate}%` }}
                        ></div>
                      </div>
                      <span>{session.completion_rate}%</span>
                    </div>
                    <div className="text-xs text-gray-400">
                      {session.completed_samples} / {session.total_samples}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      session.status === 'completed' ? 'bg-green-100 text-green-800' :
                      session.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {session.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {session.last_activity ? new Date(session.last_activity).toLocaleString() : 'Never'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Activity Summary */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity (24h)</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{recent_activity.annotations_last_24h}</div>
            <div className="text-sm text-gray-500">Annotations</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{recent_activity.active_annotators}</div>
            <div className="text-sm text-gray-500">Active Annotators</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{recent_activity.avg_annotations_per_hour}</div>
            <div className="text-sm text-gray-500">Avg/Hour</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
