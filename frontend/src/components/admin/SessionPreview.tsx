import React, { useState, useEffect } from 'react';
import { adminApi, samplesApi } from '../../services/api';
import { ModeSpecificAnnotation } from '../annotation';

interface AnnotationPreview {
  annotation: {
    id: number;
    result: string;
    annotated_at: string;
    annotation_duration: number | null;
    notes: string | null;
    has_corrections: boolean;
  };
  sample: {
    id: number;
    mode: string;
    labels: string[];
    original_metadata: any;
    corrected_metadata: any | null;
  };
  image: {
    id: number;
    width: number;
    height: number;
    file_size: number;
  };
}

interface SessionPreviewProps {
  sessionId: number;
  sessionName: string;
  isOpen: boolean;
  onClose: () => void;
}

const SessionPreview: React.FC<SessionPreviewProps> = ({
  sessionId,
  sessionName,
  isOpen,
  onClose,
}) => {
  const [annotations, setAnnotations] = useState<AnnotationPreview[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [resultFilter, setResultFilter] = useState<string>('');
  const [limit] = useState(20);

  useEffect(() => {
    if (isOpen && sessionId) {
      fetchAnnotations();
    }
  }, [isOpen, sessionId, resultFilter]);

  const fetchAnnotations = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await adminApi.getSessionPreview(
        sessionId,
        limit,
        resultFilter || undefined
      );
      setAnnotations(response.data.annotations || []);
      setCurrentIndex(0);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load annotations');
    } finally {
      setLoading(false);
    }
  };

  const currentAnnotation = annotations[currentIndex];

  const getResultBadgeColor = (result: string) => {
    switch (result) {
      case 'correct':
        return 'bg-green-100 text-green-800';
      case 'incorrect':
        return 'bg-red-100 text-red-800';
      case 'skipped':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const formatDuration = (seconds: number | null) => {
    if (!seconds) return 'N/A';
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Preview Annotations: {sessionName}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {annotations.length} annotations loaded
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center p-4 border-b bg-gray-50">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Result
              </label>
              <select
                value={resultFilter}
                onChange={(e) => setResultFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value="">All Results</option>
                <option value="correct">Correct</option>
                <option value="incorrect">Incorrect</option>
                <option value="skipped">Skipped</option>
              </select>
            </div>
          </div>

          {annotations.length > 0 && (
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {currentIndex + 1} of {annotations.length}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
                  disabled={currentIndex === 0}
                  className="px-3 py-1 bg-blue-600 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentIndex(Math.min(annotations.length - 1, currentIndex + 1))}
                  disabled={currentIndex === annotations.length - 1}
                  className="px-3 py-1 bg-blue-600 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {loading && (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">{error}</p>
              <button
                onClick={fetchAnnotations}
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          )}

          {!loading && !error && annotations.length === 0 && (
            <div className="text-center text-gray-500 py-12">
              <p>No annotations found for this session.</p>
            </div>
          )}

          {!loading && !error && currentAnnotation && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Side - Image */}
              <div className="bg-white rounded-lg border p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Sample Image</h3>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getResultBadgeColor(currentAnnotation.annotation.result)}`}>
                      {currentAnnotation.annotation.result}
                    </span>
                    {currentAnnotation.annotation.has_corrections && (
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        Corrected
                      </span>
                    )}
                  </div>
                </div>

                <ModeSpecificAnnotation
                  mode={currentAnnotation.sample.mode}
                  imageUrl={samplesApi.getImage(currentAnnotation.sample.id)}
                  metadata={currentAnnotation.annotation.has_corrections 
                    ? currentAnnotation.sample.corrected_metadata 
                    : currentAnnotation.sample.original_metadata}
                  isEditing={false}
                  imageOnly={true}
                />
              </div>

              {/* Right Side - Details */}
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Annotation Details</h3>

                <div className="space-y-4">
                  {/* Annotation Info */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Annotation Info</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Result:</span>
                        <span className="ml-2 font-medium">{currentAnnotation.annotation.result}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Duration:</span>
                        <span className="ml-2 font-medium">{formatDuration(currentAnnotation.annotation.annotation_duration)}</span>
                      </div>
                      <div className="col-span-2">
                        <span className="text-gray-500">Annotated At:</span>
                        <span className="ml-2 font-medium">
                          {new Date(currentAnnotation.annotation.annotated_at).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Sample Info */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Sample Info</h4>
                    <div className="text-sm space-y-2">
                      <div>
                        <span className="text-gray-500">Mode:</span>
                        <span className="ml-2 font-medium">{currentAnnotation.sample.mode}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Labels:</span>
                        <span className="ml-2 font-medium">
                          {currentAnnotation.sample.labels.join(', ') || 'None'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">Image Size:</span>
                        <span className="ml-2 font-medium">
                          {currentAnnotation.image.width} × {currentAnnotation.image.height}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Task Description and Metadata */}
                  <div>
                    <ModeSpecificAnnotation
                      mode={currentAnnotation.sample.mode}
                      imageUrl={samplesApi.getImage(currentAnnotation.sample.id)}
                      metadata={currentAnnotation.annotation.has_corrections 
                        ? currentAnnotation.sample.corrected_metadata 
                        : currentAnnotation.sample.original_metadata}
                      isEditing={false}
                      imageOnly={false}
                    />
                  </div>

                  {/* Notes */}
                  {currentAnnotation.annotation.notes && (
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Notes</h4>
                      <p className="text-sm text-gray-600">{currentAnnotation.annotation.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionPreview;
