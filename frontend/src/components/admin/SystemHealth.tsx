import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/api';

interface DatabaseHealth {
  total_sessions: number;
  active_sessions: number;
  recent_annotations_1h: number;
  database_size_mb: number;
}

interface SystemHealth {
  memory_usage_percent: number;
  memory_available_gb: number;
  disk_usage_percent: number;
  disk_free_gb: number;
  cpu_usage_percent: number;
}

interface StorageHealth {
  storage_size_mb: number;
  storage_path: string;
}

interface HealthData {
  status: 'healthy' | 'warning' | 'critical';
  issues: string[];
  timestamp: string;
  database: DatabaseHealth;
  system: SystemHealth;
  storage: StorageHealth;
}

const SystemHealthComponent: React.FC = () => {
  const [data, setData] = useState<HealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchHealthData = async () => {
    try {
      setError(null);
      const response = await adminApi.getSystemHealth();
      setData(response.data);
    } catch (err) {
      setError('Failed to load system health data');
      console.error('Health data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchHealthData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading system health...</span>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error || 'No data available'}</div>
        <button
          onClick={fetchHealthData}
          className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium text-gray-900">System Health</h2>
        <div className="flex items-center space-x-4">
          <label className="flex items-center text-sm">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <span className="ml-2">Auto-refresh</span>
          </label>
          <button
            onClick={fetchHealthData}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* Overall Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Overall System Status</h3>
            <p className="text-sm text-gray-500">
              Last checked: {new Date(data.timestamp).toLocaleString()}
            </p>
          </div>
          <div className="flex items-center">
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(data.status)}`}>
              {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
            </span>
          </div>
        </div>

        {data.issues.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-red-800 mb-2">Issues Detected:</h4>
            <ul className="list-disc list-inside space-y-1">
              {data.issues.map((issue, index) => (
                <li key={index} className="text-sm text-red-700">{issue}</li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* System Resources */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Memory Usage */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Memory Usage</h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Used</span>
              <span>{data.system.memory_usage_percent.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${getUsageColor(data.system.memory_usage_percent)}`}
                style={{ width: `${data.system.memory_usage_percent}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-500">
              Available: {data.system.memory_available_gb.toFixed(2)} GB
            </div>
          </div>
        </div>

        {/* Disk Usage */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Disk Usage</h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Used</span>
              <span>{data.system.disk_usage_percent.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${getUsageColor(data.system.disk_usage_percent)}`}
                style={{ width: `${data.system.disk_usage_percent}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-500">
              Free: {data.system.disk_free_gb.toFixed(2)} GB
            </div>
          </div>
        </div>

        {/* CPU Usage */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">CPU Usage</h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Current</span>
              <span>{data.system.cpu_usage_percent.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${getUsageColor(data.system.cpu_usage_percent)}`}
                style={{ width: `${data.system.cpu_usage_percent}%` }}
              ></div>
            </div>
            <div className="text-sm text-gray-500">
              Real-time usage
            </div>
          </div>
        </div>
      </div>

      {/* Database and Storage */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Database Health */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Database Health</h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Total Sessions</span>
              <span className="text-sm font-medium">{data.database.total_sessions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Active Sessions</span>
              <span className="text-sm font-medium">{data.database.active_sessions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Annotations (1h)</span>
              <span className="text-sm font-medium">{data.database.recent_annotations_1h}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Database Size</span>
              <span className="text-sm font-medium">{data.database.database_size_mb.toFixed(2)} MB</span>
            </div>
          </div>
        </div>

        {/* Storage Health */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Storage Health</h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Storage Size</span>
              <span className="text-sm font-medium">{formatBytes(data.storage.storage_size_mb * 1024 * 1024)}</span>
            </div>
            <div>
              <span className="text-sm text-gray-500">Storage Path</span>
              <div className="text-sm font-mono bg-gray-50 p-2 rounded mt-1 break-all">
                {data.storage.storage_path}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Recommendations */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Recommendations</h3>
        <div className="space-y-3">
          {data.system.memory_usage_percent > 80 && (
            <div className="flex items-start space-x-2">
              <span className="text-yellow-500">⚠️</span>
              <div className="text-sm">
                <strong>High Memory Usage:</strong> Consider restarting the application or increasing available memory.
              </div>
            </div>
          )}
          {data.system.disk_usage_percent > 85 && (
            <div className="flex items-start space-x-2">
              <span className="text-red-500">🚨</span>
              <div className="text-sm">
                <strong>Low Disk Space:</strong> Clean up old files or increase disk capacity.
              </div>
            </div>
          )}
          {data.database.recent_annotations_1h === 0 && data.database.active_sessions > 0 && (
            <div className="flex items-start space-x-2">
              <span className="text-blue-500">ℹ️</span>
              <div className="text-sm">
                <strong>No Recent Activity:</strong> Active sessions but no annotations in the last hour.
              </div>
            </div>
          )}
          {data.status === 'healthy' && (
            <div className="flex items-start space-x-2">
              <span className="text-green-500">✅</span>
              <div className="text-sm">
                <strong>System Healthy:</strong> All systems are operating normally.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemHealthComponent;
