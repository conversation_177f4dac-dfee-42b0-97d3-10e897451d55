import { t } from 'i18next';
import React from 'react';

interface ChecklistMetadata {
  descriptions: string[];
  results: boolean[];
}

interface ChecklistAnnotationProps {
  imageUrl: string;
  metadata: ChecklistMetadata;
  isEditing?: boolean;
  onMetadataChange?: (metadata: ChecklistMetadata) => void;
  imageOnly?: boolean;
}

const ChecklistAnnotation: React.FC<ChecklistAnnotationProps> = ({
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
}) => {

  const handleDescriptionChange = (index: number, description: string) => {
    if (onMetadataChange) {
      const newDescriptions = [...metadata.descriptions];
      newDescriptions[index] = description;
      onMetadataChange({ ...metadata, descriptions: newDescriptions });
    }
  };

  const handleResultChange = (index: number, result: boolean) => {
    if (onMetadataChange) {
      const newResults = [...metadata.results];
      newResults[index] = result;
      onMetadataChange({ ...metadata, results: newResults });
    }
  };

  const handleAddItem = () => {
    if (onMetadataChange) {
      onMetadataChange({
        descriptions: [...metadata.descriptions, ''],
        results: [...metadata.results, false]
      });
    }
  };

  const handleRemoveItem = (index: number) => {
    if (onMetadataChange) {
      const newDescriptions = metadata.descriptions.filter((_, i) => i !== index);
      const newResults = metadata.results.filter((_, i) => i !== index);
      onMetadataChange({ descriptions: newDescriptions, results: newResults });
    }
  };

  const trueCount = metadata.results.filter(Boolean).length;
  const totalCount = metadata.results.length;

  // If imageOnly mode, return just the image
  if (imageOnly) {
    return (
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <img
          src={imageUrl}
          alt="Sample"
          className="w-full h-auto object-contain"
        />
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Task Description */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <h4 className="text-sm font-medium text-yellow-900 mb-1">{t('modes.checklist.title')}</h4>
        <p className="text-xs text-yellow-700">
          {t('modes.checklist.description')}
        </p>
      </div>

      {/* Checklist Items */}
      <div>
        <div className="flex justify-between items-center mb-3">
          <label className="block text-sm font-medium text-gray-700">
            {t('modes.checklist.checklistItems', { correct: trueCount, total: totalCount })}
          </label>
          {isEditing && (
            <button
              onClick={handleAddItem}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200"
            >
              {t('modes.checklist.addItem')}
            </button>
          )}
        </div>

        <div className="space-y-3">
          {metadata.descriptions.map((description, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                {/* Checkbox */}
                <div className="flex-shrink-0 pt-1">
                  {isEditing ? (
                    <input
                      type="checkbox"
                      checked={metadata.results[index]}
                      onChange={(e) => handleResultChange(index, e.target.checked)}
                      className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                    />
                  ) : (
                    <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      metadata.results[index]
                        ? 'bg-green-500 border-green-500'
                        : 'bg-red-100 border-red-300'
                    }`}>
                      {metadata.results[index] ? (
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <svg className="w-3 h-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  )}
                </div>

                {/* Description */}
                <div className="flex-1">
                  {isEditing ? (
                    <input
                      type="text"
                      value={description}
                      onChange={(e) => handleDescriptionChange(index, e.target.value)}
                      className="w-full border-gray-300 rounded-md shadow-sm focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm"
                      placeholder="Enter checklist item..."
                    />
                  ) : (
                    <p className="text-sm text-gray-900">{description}</p>
                  )}
                </div>

                {/* Status Badge */}
                <div className="flex-shrink-0">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    metadata.results[index]
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {metadata.results[index] ? t('modes.checklist.present'): t('modes.checklist.absent')}
                  </span>
                </div>

                {/* Remove Button */}
                {isEditing && (
                  <div className="flex-shrink-0">
                    <button
                      onClick={() => handleRemoveItem(index)}
                      className="text-red-600 hover:text-red-800 text-xs"
                    >
                      {t('modes.checklist.removeItem')}
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}

          {metadata.descriptions.length === 0 && (
            <div className="text-center py-6 text-gray-500">
              <p className="text-sm">{t('modes.checklist.noChecklistItems')}</p>
              {isEditing && (
                <button
                  onClick={handleAddItem}
                  className="mt-2 inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  {t('modes.checklist.addFirstItem')}
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Summary Statistics */}
      {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h5 className="text-sm font-medium text-gray-700 mb-3">Checklist Summary</h5>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">{totalCount}</div>
            <div className="text-xs text-gray-500">Total Items</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-green-600">{trueCount}</div>
            <div className="text-xs text-gray-500">Present</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-red-600">{totalCount - trueCount}</div>
            <div className="text-xs text-gray-500">Absent</div>
          </div>
        </div>
        {totalCount > 0 && (
          <div className="mt-3">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${(trueCount / totalCount) * 100}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1 text-center">
              {Math.round((trueCount / totalCount) * 100)}% confirmed
            </div>
          </div>
        )}
      </div> */}
    </div>
  );
};

export default ChecklistAnnotation;
