import React from 'react';
import ImageCanvas from '../ImageCanvas';
import { arrayToCoordinate } from '../../utils/coordinates';
import { t } from 'i18next';

interface DescribeMetadata {
  coordinate: [number, number, number, number];
  description: string;
}

interface DescribeAnnotationProps {
  imageUrl: string;
  metadata: DescribeMetadata;
  isEditing?: boolean;
  onMetadataChange?: (metadata: DescribeMetadata) => void;
  imageOnly?: boolean;
}

const DescribeAnnotation: React.FC<DescribeAnnotationProps> = ({
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
}) => {

  const handleDescriptionChange = (description: string) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, description });
    }
  };

  // Note: coordinate changes are handled by the bounding box component if needed

  const boundingBoxes = [{
    coordinate: arrayToCoordinate(metadata.coordinate)!,
    label: 'Region to Describe',
    color: '#3b82f6'
  }];

  // If imageOnly mode, return just the image with bounding box
  if (imageOnly) {
    return (
      <ImageCanvas
        imageUrl={imageUrl}
        boundingBoxes={boundingBoxes}
        className="w-full"
      />
    );
  }

  return (
    <div className="space-y-3">
      {/* Task Description */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <h4 className="text-sm font-medium text-blue-900 mb-1">{t('modes.describe.title')}</h4>
        <p className="text-xs text-blue-700">
          {t('modes.describe.description')}
        </p>
      </div>

      {/* Region Coordinates */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('modes.describe.regionToDescribe')}
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
          <div className="text-sm text-gray-900">
            <span className="font-mono">
              [{metadata.coordinate[0]}, {metadata.coordinate[1]}, {metadata.coordinate[2]}, {metadata.coordinate[3]}]
            </span>
            <div className="text-xs text-gray-500 mt-1">
              {t('modes.describe.coordinate')}
            </div>
            <div className="text-xs text-gray-500">
              {t('modes.describe.region')}: ({metadata.coordinate[0]}, {metadata.coordinate[1]}) →
              ({metadata.coordinate[2]}, {metadata.coordinate[3]})
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('modes.describe.descriptionLabel')}
        </label>
        {isEditing ? (
          <textarea
            value={metadata.description}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            rows={4}
            className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder={t('modes.describe.descriptionPlaceholder')}
          />
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
            <p className="text-sm text-gray-900 whitespace-pre-wrap">
              {metadata.description}
            </p>
          </div>
        )}
      </div>

      {/* Region Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <h5 className="text-sm font-medium text-blue-700 mb-2">{t('modes.describe.region')}</h5>
        <div className="text-sm text-blue-600">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Width:</strong> {metadata.coordinate[2] - metadata.coordinate[0]} pixels</p>
              <p><strong>Height:</strong> {metadata.coordinate[3] - metadata.coordinate[1]} pixels</p>
            </div>
            <div>
              <p><strong>Top-left:</strong> ({metadata.coordinate[0]}, {metadata.coordinate[1]})</p>
              <p><strong>Bottom-right:</strong> ({metadata.coordinate[2]}, {metadata.coordinate[3]})</p>
            </div>
          </div>
          <div className="text-xs text-blue-500 mt-2">
            {t('modes.describe.coordinate')}
          </div>
        </div>
      </div>

      {/* Result Summary */}
      {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
        <h5 className="text-sm font-medium text-gray-700 mb-2">Task Summary</h5>
        <div className="text-sm text-gray-600">
          <p><strong>Task:</strong> Describe the content in the specified region</p>
          <p><strong>Region:</strong> {metadata.coordinate[2] - metadata.coordinate[0]} × {metadata.coordinate[3] - metadata.coordinate[1]} pixels</p>
          <p><strong>Description:</strong> {metadata.description.length} characters</p>
        </div>
      </div> */}
    </div>
  );
};

export default DescribeAnnotation;
