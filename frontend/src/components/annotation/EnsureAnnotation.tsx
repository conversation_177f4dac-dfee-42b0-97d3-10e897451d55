import React from 'react';
import InteractiveImageCanvas from '../InteractiveImageCanvas';
import { arrayToCoordinate, coordinateToArray } from '../../utils/coordinates';
import { t } from 'i18next';

interface EnsureMetadata {
  expected_state: string;
  coordinate: [number, number, number, number] | null;
}

interface EnsureAnnotationProps {
  imageUrl: string;
  metadata: EnsureMetadata;
  isEditing?: boolean;
  onMetadataChange?: (metadata: EnsureMetadata) => void;
  imageOnly?: boolean;
}

const EnsureAnnotation: React.FC<EnsureAnnotationProps> = ({
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
}) => {

  const handleExpectedStateChange = (expected_state: string) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, expected_state });
    }
  };

  const handleCoordinateChange = (coordinate: [number, number, number, number] | null) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, coordinate });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleBoundingBoxChange = (boxes: any[]) => {
    if (boxes.length > 0) {
      // In ensure mode, we only care about the latest/last box
      const coord = coordinateToArray(boxes[boxes.length - 1].coordinate);
      handleCoordinateChange(coord);
    } else {
      handleCoordinateChange(null);
    }
  };

  const handleBoundingBoxDelete = () => {
    // In ensure mode, any deletion should clear the coordinate
    handleCoordinateChange(null);
  };

  const boundingBoxes = metadata.coordinate ? [{
    id: 'ensure-target',
    coordinate: arrayToCoordinate(metadata.coordinate)!,
    label: 'Click Target',
    color: '#10b981'
  }] : [];

  // If imageOnly mode, return just the interactive canvas
  if (imageOnly) {
    return (
      <div>
        <InteractiveImageCanvas
          imageUrl={imageUrl}
          boundingBoxes={boundingBoxes}
          isEditable={isEditing}
          allowMultiple={false}
          onBoundingBoxChange={handleBoundingBoxChange}
          onBoundingBoxDelete={handleBoundingBoxDelete}
          className="w-full"
        />
        {isEditing && (
          <div className="mt-2 text-xs text-green-600 text-center">
            {metadata.coordinate
              ? 'Click and drag to redraw the action area, or click the existing box to select it.'
              : 'Click and drag on the image to mark where action is needed, or leave empty if no action is required.'
            }
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Task Description */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-green-900 mb-2">{t('modes.ensure.title')}</h4>
        <p className="text-sm text-green-700">
          {t('modes.ensure.description')}
        </p>
      </div>

      {/* Expected State */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('modes.ensure.expectedState')}
        </label>
        {isEditing ? (
          <input
            type="text"
            value={metadata.expected_state}
            onChange={(e) => handleExpectedStateChange(e.target.value)}
            className="w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 sm:text-sm"
            placeholder={t('modes.ensure.expectedStatePlaceholder')}
          />
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
            <p className="text-sm text-gray-900 font-medium">
              "{metadata.expected_state}"
            </p>
          </div>
        )}
      </div>

      {/* Action Information */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('modes.ensure.requiredAction')}
        </label>
        {metadata.coordinate ? (
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <div className="flex items-center mb-2">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-green-800">{t('modes.ensure.clickRequired')}</span>
            </div>
            <p className="text-sm text-green-700">
              {t('modes.ensure.clickInstructions')}
            </p>
          </div>
        ) : (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-center mb-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-blue-800">{t('modes.ensure.noActionNeeded')}</span>
            </div>
            <p className="text-sm text-blue-700">
              {t('modes.ensure.stateAlreadyAchieved')}
            </p>
          </div>
        )}
      </div>

      {/* Coordinate Information */}
      {metadata.coordinate && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('modes.ensure.clickCoordinates')}
          </label>
          {isEditing ? (
            <div className="grid grid-cols-4 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">X1</label>
                <input
                  type="number"
                  value={metadata.coordinate[0]}
                  onChange={(e) => {
                    const newCoord = [...metadata.coordinate!] as [number, number, number, number];
                    newCoord[0] = parseInt(e.target.value) || 0;
                    handleCoordinateChange(newCoord);
                  }}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Y1</label>
                <input
                  type="number"
                  value={metadata.coordinate[1]}
                  onChange={(e) => {
                    const newCoord = [...metadata.coordinate!] as [number, number, number, number];
                    newCoord[1] = parseInt(e.target.value) || 0;
                    handleCoordinateChange(newCoord);
                  }}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">X2</label>
                <input
                  type="number"
                  value={metadata.coordinate[2]}
                  onChange={(e) => {
                    const newCoord = [...metadata.coordinate!] as [number, number, number, number];
                    newCoord[2] = parseInt(e.target.value) || 0;
                    handleCoordinateChange(newCoord);
                  }}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 text-xs"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Y2</label>
                <input
                  type="number"
                  value={metadata.coordinate[3]}
                  onChange={(e) => {
                    const newCoord = [...metadata.coordinate!] as [number, number, number, number];
                    newCoord[3] = parseInt(e.target.value) || 0;
                    handleCoordinateChange(newCoord);
                  }}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 text-xs"
                />
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
              <div className="text-sm text-gray-900">
                <span className="font-mono">
                  [{metadata.coordinate[0]}, {metadata.coordinate[1]}, {metadata.coordinate[2]}, {metadata.coordinate[3]}]
                </span>
                <div className="text-xs text-gray-500 mt-1">
                  Click area: ({metadata.coordinate[0]}, {metadata.coordinate[1]}) → 
                  ({metadata.coordinate[2]}, {metadata.coordinate[3]})
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Result Summary */}
      {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
        <h5 className="text-sm font-medium text-gray-700 mb-2">Task Summary</h5>
        <div className="text-sm text-gray-600">
          <p><strong>Goal:</strong> {metadata.expected_state}</p>
          <p><strong>Action:</strong> {metadata.coordinate ? 'Click required at specified location' : 'No action needed - state already achieved'}</p>
        </div>
      </div> */}
    </div>
  );
};

export default EnsureAnnotation;
