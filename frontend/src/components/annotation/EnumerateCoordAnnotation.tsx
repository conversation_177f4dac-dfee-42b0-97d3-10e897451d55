import React from 'react';
import { useTranslation } from 'react-i18next';
import InteractiveImageCanvas from '../InteractiveImageCanvas';
import { arrayToCoordinate, coordinateToArray } from '../../utils/coordinates';

interface EnumerateCoordMetadata {
  general_description: string;
  elements: [number, number, number, number][];
}

interface EnumerateCoordAnnotationProps {
  imageUrl: string;
  metadata: EnumerateCoordMetadata;
  isEditing?: boolean;
  onMetadataChange?: (metadata: EnumerateCoordMetadata) => void;
  imageOnly?: boolean;
}

const EnumerateCoordAnnotation: React.FC<EnumerateCoordAnnotationProps> = ({
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
}) => {
  const { t } = useTranslation();
  const handleDescriptionChange = (general_description: string) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, general_description });
    }
  };

  // Note: Element changes are handled by the bounding box component if needed

  const handleRemoveElement = (index: number) => {
    if (onMetadataChange) {
      const newElements = metadata.elements.filter((_, i) => i !== index);
      onMetadataChange({ ...metadata, elements: newElements });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleBoundingBoxChange = (boxes: any[]) => {
    if (onMetadataChange) {
      const newElements = boxes.map(box => coordinateToArray(box.coordinate)!);
      onMetadataChange({ ...metadata, elements: newElements });
    }
  };

  const handleBoundingBoxDelete = (boxId: string) => {
    const index = parseInt(boxId.split('-')[1]);
    handleRemoveElement(index);
  };

  const boundingBoxes = metadata.elements.map((coord, index) => ({
    id: `element-${index}`,
    coordinate: arrayToCoordinate(coord)!,
    label: t('modes.enumerateCoord.element', { index: index + 1 }),
  }));

  // If imageOnly mode, return just the interactive canvas
  if (imageOnly) {
    return (
      <div>
        <InteractiveImageCanvas
          imageUrl={imageUrl}
          boundingBoxes={boundingBoxes}
          isEditable={isEditing}
          allowMultiple={true}
          onBoundingBoxChange={handleBoundingBoxChange}
          onBoundingBoxDelete={handleBoundingBoxDelete}
          className="w-full"
        />
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Task Description */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-purple-900 mb-2">{t('modes.enumerateCoord.title')}</h4>
        <p className="text-sm text-purple-700">
          {t('modes.enumerateCoord.description')}
        </p>
      </div>

      {/* General Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('modes.enumerateCoord.generalDescription')}
        </label>
        {isEditing ? (
          <input
            type="text"
            value={metadata.general_description}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            className="w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            placeholder={t('modes.enumerateCoord.generalDescriptionPlaceholder')}
          />
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
            <p className="text-sm text-gray-900 font-medium">
              {metadata.general_description}
            </p>
          </div>
        )}
      </div>

      {/* Elements List */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('modes.enumerateCoord.foundElements')}
        </label>

        <div className="space-y-2">
          {metadata.elements.map((element, index) => (
            <div key={index} className="bg-gray-50 border border-gray-200 rounded-md p-3">
              <div className="flex justify-between items-center">
                <h5 className="text-sm font-medium text-gray-700">
                  {t('modes.enumerateCoord.element', { index: index + 1 })}
                </h5>
                <div className="text-sm text-gray-900">
                  <span className="font-mono">
                    [{element[0]}, {element[1]}, {element[2]}, {element[3]}]
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {t('modes.enumerateCoord.pixelCoordinates', { x1: element[0], y1: element[1], x2: element[2], y2: element[3] })}
              </div>
            </div>
          ))}

          {metadata.elements.length === 0 && (
            <div className="text-center py-6 text-gray-500">
              <p className="text-sm">{t('modes.enumerateCoord.noElementsFound')}</p>
              {isEditing && (
                <p className="text-xs mt-1">{t('modes.enumerateCoord.drawInstructions')}</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Result Summary */}
      {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
        <h5 className="text-sm font-medium text-gray-700 mb-2">{t('modes.enumerateCoord.summary')}</h5>
        <div className="text-sm text-gray-600">
          <p><strong>{t('modes.enumerateCoord.task')}:</strong> {t('modes.enumerateCoord.findAllText', { description: metadata.general_description })}</p>
          <p><strong>{t('modes.enumerateCoord.result')}:</strong> {metadata.elements.length} {t('modes.enumerateCoord.elements')} {t('modes.enumerateCoord.elementsFound')}</p>
        </div>
      </div> */}
    </div>
  );
};

export default EnumerateCoordAnnotation;
