import React from 'react';
import { useTranslation } from 'react-i18next';

interface EnumerateTextMetadata {
  general_description: string;
  elements: string[];
}

interface EnumerateTextAnnotationProps {
  imageUrl: string;
  metadata: EnumerateTextMetadata;
  isEditing?: boolean;
  onMetadataChange?: (metadata: EnumerateTextMetadata) => void;
  imageOnly?: boolean;
}

const EnumerateTextAnnotation: React.FC<EnumerateTextAnnotationProps> = ({
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
}) => {
  const { t } = useTranslation();
  const handleDescriptionChange = (general_description: string) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, general_description });
    }
  };

  const handleElementChange = (index: number, element: string) => {
    if (onMetadataChange) {
      const newElements = [...metadata.elements];
      newElements[index] = element;
      onMetadataChange({ ...metadata, elements: newElements });
    }
  };

  const handleAddElement = () => {
    if (onMetadataChange) {
      onMetadataChange({
        ...metadata,
        elements: [...metadata.elements, '']
      });
    }
  };

  const handleRemoveElement = (index: number) => {
    if (onMetadataChange) {
      const newElements = metadata.elements.filter((_, i) => i !== index);
      onMetadataChange({ ...metadata, elements: newElements });
    }
  };

  // If imageOnly mode, return just the image
  if (imageOnly) {
    return (
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <img
          src={imageUrl}
          alt="Sample"
          className="w-full h-auto object-contain"
        />
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Task Description */}
      <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-3">
        <h4 className="text-sm font-medium text-indigo-900 mb-1">{t('modes.enumerateText.title')}</h4>
        <p className="text-xs text-indigo-700">
          {t('modes.enumerateText.description')}
        </p>
      </div>

      {/* General Description */}
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          {t('modes.enumerateText.generalDescription')}
        </label>
        {isEditing ? (
          <input
            type="text"
            value={metadata.general_description}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            className="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"
            placeholder={t('modes.enumerateText.generalDescriptionPlaceholder')}
          />
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-2">
            <p className="text-sm text-gray-900 font-medium">
              {metadata.general_description}
            </p>
          </div>
        )}
      </div>

      {/* Text Elements List */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700">
            {t('modes.enumerateText.elementsFound', {count: metadata.elements.length})}
          </label>
          {isEditing && (
            <button
              onClick={handleAddElement}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
            >
              + {t('modes.enumerateText.addElement')}
            </button>
          )}
        </div>

        <div className="space-y-2">
          {metadata.elements.map((element, index) => (
            <div key={index} className="flex items-center space-x-3 bg-gray-50 border border-gray-200 rounded-md p-3">
              <div className="flex-shrink-0">
                <span className="inline-flex items-center justify-center w-6 h-6 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-full">
                  {index + 1}
                </span>
              </div>
              
              <div className="flex-1">
                {isEditing ? (
                  <input
                    type="text"
                    value={element}
                    onChange={(e) => handleElementChange(index, e.target.value)}
                    className="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder={t('modes.enumerateText.elementPlaceholder')}
                  />
                ) : (
                  <span className="text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded border">
                    "{element}"
                  </span>
                )}
              </div>

              {isEditing && (
                <div className="flex-shrink-0">
                  <button
                    onClick={() => handleRemoveElement(index)}
                    className="text-red-600 hover:text-red-800 text-xs"
                  >
                    {t('modes.enumerateText.removeElement')}
                  </button>
                </div>
              )}
            </div>
          ))}

          {metadata.elements.length === 0 && (
            <div className="text-center py-6 text-gray-500">
              <p className="text-sm">{t('modes.enumerateText.noElementsFound')}</p>
              {isEditing && (
                <button
                  onClick={handleAddElement}
                  className="mt-2 inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  {t('modes.enumerateText.addFirstElement')}
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Text Elements Preview */}
      {metadata.elements.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('modes.enumerateText.textElements')}
          </label>
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
            <div className="flex flex-wrap gap-2">
              {metadata.elements.map((element, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                >
                  {element}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Result Summary */}
      {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
        <h5 className="text-sm font-medium text-gray-700 mb-2">Task Summary</h5>
        <div className="text-sm text-gray-600">
          <p><strong>Task:</strong> Find all {metadata.general_description}</p>
          <p><strong>Result:</strong> {metadata.elements.length} text element(s) found</p>
          {metadata.elements.length > 0 && (
            <div className="mt-2">
              <p><strong>Elements:</strong></p>
              <p className="font-mono text-xs bg-white p-2 rounded border mt-1">
                {metadata.elements.map(el => `"${el}"`).join(', ')}
              </p>
            </div>
          )}
        </div>
      </div> */}
    </div>
  );
};

export default EnumerateTextAnnotation;
