import React from 'react';
import { useTranslation } from 'react-i18next';
import InteractiveImageCanvas from '../InteractiveImageCanvas';
import { arrayToCoordinate, coordinateToArray} from '../../utils/coordinates';

interface GroundingMetadata {
  description: string;
  coordinate: [number, number, number, number] | null;
}

interface GroundingAnnotationProps {
  imageUrl: string;
  metadata: GroundingMetadata;
  isEditing?: boolean;
  onMetadataChange?: (metadata: GroundingMetadata) => void;
  imageOnly?: boolean; // New prop to render only image
}

const GroundingAnnotation: React.FC<GroundingAnnotationProps> = ({
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
}) => {
  const { t } = useTranslation();
  const handleDescriptionChange = (description: string) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, description });
    }
  };

  const handleCoordinateChange = (coordinate: [number, number, number, number] | null) => {
    if (onMetadataChange) {
      onMetadataChange({ ...metadata, coordinate });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleBoundingBoxChange = (boxes: any[]) => {
    if (boxes.length > 0) {
      // In grounding mode, we only care about the latest/last box
      const coord = coordinateToArray(boxes[boxes.length - 1].coordinate);
      handleCoordinateChange(coord);
    } else {
      handleCoordinateChange(null);
    }
  };

  const handleBoundingBoxDelete = () => {
    // In grounding mode, any deletion should clear the coordinate
    handleCoordinateChange(null);
  };

  const boundingBoxes = metadata.coordinate ? [{
    id: 'grounding-target',
    coordinate: arrayToCoordinate(metadata.coordinate)!,
    label: metadata.description || 'Target',
    color: '#ef4444'
  }] : [];

  // If imageOnly mode, return just the interactive canvas
  if (imageOnly) {
    return (
      <div>
        <InteractiveImageCanvas
          imageUrl={imageUrl}
          boundingBoxes={boundingBoxes}
          isEditable={isEditing}
          allowMultiple={false}
          onBoundingBoxChange={handleBoundingBoxChange}
          onBoundingBoxDelete={handleBoundingBoxDelete}
          className="w-full"
        />
      </div>
    );
  }

  // Full metadata view for right panel
  return (
    <div className="space-y-3">
      {/* Task Description */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <h4 className="text-sm font-medium text-blue-900 mb-1">{t('modes.grounding.title')}</h4>
        <p className="text-xs text-blue-700">
          {t('modes.grounding.description')}
        </p>
      </div>

      {/* Description */}
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          {t('modes.grounding.targetDescription')}
        </label>
        {isEditing ? (
          <input
            type="text"
            value={metadata.description}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm"
            placeholder={t('modes.grounding.targetDescriptionPlaceholder')}
          />
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-2">
            <p className="text-sm text-gray-900 font-medium">
              "{metadata.description}"
            </p>
          </div>
        )}
      </div>

      {/* Coordinate Information */}
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          {t('modes.grounding.coordinates')}
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-md p-2">
          {metadata.coordinate ? (
            <div className="text-xs text-gray-900">
              <span className="font-mono">
                [{metadata.coordinate[0]}, {metadata.coordinate[1]}, {metadata.coordinate[2]}, {metadata.coordinate[3]}]
              </span>
            </div>
          ) : (
            <div className="text-xs text-gray-500 italic">
              {t('modes.grounding.targetNotFound')}
            </div>
          )}
        </div>
        {isEditing && metadata.coordinate && (
          <div className="mt-1">
            <button
              onClick={() => handleCoordinateChange(null)}
              className="text-xs text-red-600 hover:text-red-800"
            >
              {t('modes.grounding.clearCoordinate')}
            </button>
          </div>
        )}
      </div>

      {/* Result Summary */}
      {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-2">
        <h5 className="text-xs font-medium text-gray-700 mb-1">Summary</h5>
        <div className="text-xs text-gray-600">
          <p><strong>Task:</strong> Find "{metadata.description}"</p>
          <p><strong>Result:</strong> {metadata.coordinate ? 'Target located' : 'Target not found'}</p>
        </div>
      </div> */}
    </div>
  );
};

export default GroundingAnnotation;
