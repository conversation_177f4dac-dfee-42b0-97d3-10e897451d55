import React from 'react';
import GroundingAnnotation from './GroundingAnnotation';
import DescribeAnnotation from './DescribeAnnotation';
import EnumerateTextAnnotation from './EnumerateTextAnnotation';
import EnumerateCoordAnnotation from './EnumerateCoordAnnotation';
import ChecklistAnnotation from './ChecklistAnnotation';
import EnsureAnnotation from './EnsureAnnotation';

interface ModeSpecificAnnotationProps {
  mode: string;
  imageUrl: string;
  metadata: any;
  isEditing?: boolean;
  onMetadataChange?: (metadata: any) => void;
  imageOnly?: boolean;
  compact?: boolean; // For admin preview - smaller image size
}

const ModeSpecificAnnotation: React.FC<ModeSpecificAnnotationProps> = ({
  mode,
  imageUrl,
  metadata,
  isEditing = false,
  onMetadataChange,
  imageOnly = false,
  compact = false,
}) => {
  // Validate metadata structure based on mode
  const validateAndNormalizeMetadata = (mode: string, metadata: any) => {
    switch (mode) {
      case 'grounding':
        return {
          description: metadata?.description || '',
          coordinate: metadata?.coordinate || null,
        };
      
      case 'describe':
        return {
          coordinate: metadata?.coordinate || [0, 0, 0, 0],
          description: metadata?.description || '',
        };
      
      case 'enumerate_text':
        return {
          general_description: metadata?.general_description || '',
          elements: metadata?.elements || [],
        };
      
      case 'enumerate_coord':
        return {
          general_description: metadata?.general_description || '',
          elements: metadata?.elements || [],
        };
      
      case 'checklist':
        return {
          descriptions: metadata?.descriptions || [],
          results: metadata?.results || [],
        };
      
      case 'ensure':
        return {
          expected_state: metadata?.expected_state || '',
          coordinate: metadata?.coordinate || null,
        };
      
      default:
        return metadata;
    }
  };

  const normalizedMetadata = validateAndNormalizeMetadata(mode, metadata);

  const renderModeComponent = () => {
    // For imageOnly mode and non-interactive modes, show simple image
    if (imageOnly && !['grounding', 'ensure', 'enumerate_coord'].includes(mode)) {
      const maxWidth = compact ? '250px' : '400px';
      return (
        <div className="border border-gray-300 rounded-lg overflow-hidden" style={{ maxWidth }}>
          <img
            src={imageUrl}
            alt="Sample"
            className="w-full h-auto object-contain"
          />
        </div>
      );
    }

    switch (mode) {
      case 'grounding':
        return (
          <GroundingAnnotation
            imageUrl={imageUrl}
            metadata={normalizedMetadata}
            isEditing={isEditing}
            onMetadataChange={onMetadataChange}
            imageOnly={imageOnly}
          />
        );
      
      case 'describe':
        return (
          <DescribeAnnotation
            imageUrl={imageUrl}
            metadata={normalizedMetadata}
            isEditing={isEditing}
            onMetadataChange={onMetadataChange}
            imageOnly={imageOnly}
          />
        );
      
      case 'enumerate_text':
        return (
          <EnumerateTextAnnotation
            imageUrl={imageUrl}
            metadata={normalizedMetadata}
            isEditing={isEditing}
            onMetadataChange={onMetadataChange}
            imageOnly={imageOnly}
          />
        );
      
      case 'enumerate_coord':
        return (
          <EnumerateCoordAnnotation
            imageUrl={imageUrl}
            metadata={normalizedMetadata}
            isEditing={isEditing}
            onMetadataChange={onMetadataChange}
            imageOnly={imageOnly}
          />
        );
      
      case 'checklist':
        return (
          <ChecklistAnnotation
            imageUrl={imageUrl}
            metadata={normalizedMetadata}
            isEditing={isEditing}
            onMetadataChange={onMetadataChange}
            imageOnly={imageOnly}
          />
        );
      
      case 'ensure':
        return (
          <EnsureAnnotation
            imageUrl={imageUrl}
            metadata={normalizedMetadata}
            isEditing={isEditing}
            onMetadataChange={onMetadataChange}
            imageOnly={imageOnly}
          />
        );
      
      default:
        return (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-yellow-900 mb-2">Unknown Mode</h4>
            <p className="text-sm text-yellow-700 mb-3">
              Mode "{mode}" is not supported. Showing raw metadata:
            </p>
            <pre className="bg-gray-50 p-3 rounded text-xs overflow-auto">
              {JSON.stringify(metadata, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {renderModeComponent()}
    </div>
  );
};

export default ModeSpecificAnnotation;
