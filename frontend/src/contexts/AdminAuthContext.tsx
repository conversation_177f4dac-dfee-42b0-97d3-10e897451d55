import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { adminApi, setAdminAuth } from '../services/api';

interface AdminCredentials {
  dashboardPath: string;
  password: string;
  loginTime: number;
  lastActivity: number;
}

interface AdminAuthContextType {
  isAuthenticated: boolean;
  adminCredentials: AdminCredentials | null;
  login: (dashboardPath: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
  updateActivity: () => void;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

interface AdminAuthProviderProps {
  children: ReactNode;
}

// Admin session timeout configuration (in milliseconds)
const ADMIN_SESSION_TIMEOUT = 4 * 60 * 60 * 1000; // 4 hours
const ADMIN_IDLE_TIMEOUT = 2 * 60 * 60 * 1000; // 2 hours
const ADMIN_ACTIVITY_CHECK_INTERVAL = 5 * 60 * 1000; // Check every 5 minutes

export const AdminAuthProvider: React.FC<AdminAuthProviderProps> = ({ children }) => {
  const [adminCredentials, setAdminCredentials] = useState<AdminCredentials | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for admin session timeout
  const checkAdminSessionTimeout = (credentials: AdminCredentials): boolean => {
    const now = Date.now();
    const sessionAge = now - credentials.loginTime;
    const idleTime = now - credentials.lastActivity;

    if (sessionAge > ADMIN_SESSION_TIMEOUT) {
      console.log('Admin session expired due to total time limit');
      return true;
    }

    if (idleTime > ADMIN_IDLE_TIMEOUT) {
      console.log('Admin session expired due to inactivity');
      return true;
    }

    return false;
  };

  // Update last activity time
  const updateActivity = () => {
    if (adminCredentials) {
      const updatedCredentials = {
        ...adminCredentials,
        lastActivity: Date.now(),
      };
      setAdminCredentials(updatedCredentials);
      localStorage.setItem('adminCredentials', JSON.stringify(updatedCredentials));
    }
  };

  // Initialize admin authentication state from localStorage on mount
  useEffect(() => {
    const savedCredentials = localStorage.getItem('adminCredentials');
    if (savedCredentials) {
      try {
        const credentials = JSON.parse(savedCredentials) as AdminCredentials;

        // Check if admin session has expired
        if (checkAdminSessionTimeout(credentials)) {
          localStorage.removeItem('adminCredentials');
          setAdminCredentials(null);
        } else {
          // Update activity time and restore session
          const updatedCredentials = {
            ...credentials,
            lastActivity: Date.now(),
          };
          setAdminCredentials(updatedCredentials);
          localStorage.setItem('adminCredentials', JSON.stringify(updatedCredentials));
          setAdminAuth({
            dashboardPath: credentials.dashboardPath,
            password: credentials.password,
          });
        }
      } catch (error) {
        console.error('Failed to parse saved admin credentials:', error);
        localStorage.removeItem('adminCredentials');
      }
    }
    setLoading(false);
  }, []);

  // Set up periodic admin session timeout check
  useEffect(() => {
    if (!adminCredentials) return;

    const timeoutCheck = setInterval(() => {
      // Get fresh credentials from localStorage to check timeout
      const savedCredentials = localStorage.getItem('adminCredentials');
      if (savedCredentials) {
        try {
          const credentials = JSON.parse(savedCredentials) as AdminCredentials;
          if (checkAdminSessionTimeout(credentials)) {
            logout();
            alert('Your admin session has expired due to inactivity. Please log in again.');
          }
        } catch (error) {
          console.error('Failed to parse saved admin credentials for timeout check:', error);
        }
      }
    }, ADMIN_ACTIVITY_CHECK_INTERVAL);

    return () => clearInterval(timeoutCheck);
  }, [adminCredentials?.dashboardPath]);

  const login = async (dashboardPath: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      // Verify admin credentials with the server
      await adminApi.verifyCredentials(dashboardPath, password);
      
      // If verification succeeds, save credentials
      const now = Date.now();
      const credentials: AdminCredentials = {
        dashboardPath,
        password,
        loginTime: now,
        lastActivity: now,
      };
      
      setAdminCredentials(credentials);
      setAdminAuth({
        dashboardPath,
        password,
      });
      
      // Persist credentials to localStorage
      localStorage.setItem('adminCredentials', JSON.stringify(credentials));
      
      return true;
    } catch (error) {
      console.error('Admin login failed:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setAdminCredentials(null);
    setAdminAuth(null);
    localStorage.removeItem('adminCredentials');
  };

  const value: AdminAuthContextType = {
    isAuthenticated: !!adminCredentials,
    adminCredentials,
    login,
    logout,
    loading,
    updateActivity,
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

export const useAdminAuth = (): AdminAuthContextType => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};
