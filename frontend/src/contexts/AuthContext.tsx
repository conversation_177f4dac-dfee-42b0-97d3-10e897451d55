import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authApi, setSessionAuth, setActivityCallback } from '../services/api';

interface SessionCredentials {
  sessionId: number;
  username: string;
  password: string;
  loginTime: number;
  lastActivity: number;
}

interface AuthContextType {
  isAuthenticated: boolean;
  sessionCredentials: SessionCredentials | null;
  login: (sessionId: number, username: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
  updateActivity: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Session timeout configuration (in milliseconds)
const SESSION_TIMEOUT = 60 * 60 * 1000; // 60 minutes
const IDLE_TIMEOUT = 30 * 60 * 1000; // 30 minutes
const ACTIVITY_CHECK_INTERVAL = 60 * 1000; // Check every minute

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [sessionCredentials, setSessionCredentials] = useState<SessionCredentials | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for session timeout
  const checkSessionTimeout = (credentials: SessionCredentials): boolean => {
    const now = Date.now();
    const sessionAge = now - credentials.loginTime;
    const idleTime = now - credentials.lastActivity;

    if (sessionAge > SESSION_TIMEOUT) {
      console.log('Session expired due to total time limit');
      return true;
    }

    if (idleTime > IDLE_TIMEOUT) {
      console.log('Session expired due to inactivity');
      return true;
    }

    return false;
  };

  // Update last activity time
  const updateActivity = () => {
    if (sessionCredentials) {
      const updatedCredentials = {
        ...sessionCredentials,
        lastActivity: Date.now(),
      };
      setSessionCredentials(updatedCredentials);
      localStorage.setItem('sessionCredentials', JSON.stringify(updatedCredentials));
    }
  };

  // Initialize authentication state from localStorage on mount
  useEffect(() => {
    const savedCredentials = localStorage.getItem('sessionCredentials');
    if (savedCredentials) {
      try {
        const credentials = JSON.parse(savedCredentials) as SessionCredentials;

        // Check if session has expired
        if (checkSessionTimeout(credentials)) {
          localStorage.removeItem('sessionCredentials');
          setSessionCredentials(null);
        } else {
          // Update activity time and restore session
          const updatedCredentials = {
            ...credentials,
            lastActivity: Date.now(),
          };
          setSessionCredentials(updatedCredentials);
          localStorage.setItem('sessionCredentials', JSON.stringify(updatedCredentials));
          setSessionAuth({
            sessionId: credentials.sessionId,
            username: credentials.username,
            password: credentials.password,
          });
        }
      } catch (error) {
        console.error('Failed to parse saved credentials:', error);
        localStorage.removeItem('sessionCredentials');
      }
    }
    setLoading(false);
  }, []);

  // Set up activity tracking callback
  useEffect(() => {
    setActivityCallback(updateActivity);
    return () => setActivityCallback(null);
  }, [sessionCredentials?.sessionId, sessionCredentials?.username]); // Only depend on session identity, not activity time

  // Set up periodic session timeout check
  useEffect(() => {
    if (!sessionCredentials) return;

    const timeoutCheck = setInterval(() => {
      // Get fresh credentials from localStorage to check timeout
      const savedCredentials = localStorage.getItem('sessionCredentials');
      if (savedCredentials) {
        try {
          const credentials = JSON.parse(savedCredentials) as SessionCredentials;
          if (checkSessionTimeout(credentials)) {
            logout();
            alert('Your session has expired due to inactivity. Please log in again.');
          }
        } catch (error) {
          console.error('Failed to parse saved credentials for timeout check:', error);
        }
      }
    }, ACTIVITY_CHECK_INTERVAL);

    return () => clearInterval(timeoutCheck);
  }, [sessionCredentials?.sessionId]); // Only depend on session identity

  const login = async (sessionId: number, username: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      // Verify credentials with the server
      await authApi.verifySession(sessionId, username, password);
      
      // If verification succeeds, save credentials
      const now = Date.now();
      const credentials: SessionCredentials = {
        sessionId,
        username,
        password,
        loginTime: now,
        lastActivity: now,
      };
      
      setSessionCredentials(credentials);
      setSessionAuth({
        sessionId,
        username,
        password,
      });
      
      // Persist credentials to localStorage
      localStorage.setItem('sessionCredentials', JSON.stringify(credentials));
      
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setSessionCredentials(null);
    setSessionAuth(null);
    localStorage.removeItem('sessionCredentials');
  };

  const value: AuthContextType = {
    isAuthenticated: !!sessionCredentials,
    sessionCredentials,
    login,
    logout,
    loading,
    updateActivity,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
