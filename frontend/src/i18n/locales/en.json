{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "logout": "Logout", "login": "<PERSON><PERSON>", "refresh": "Refresh"}, "auth": {"signInTitle": "Sign in to your annotation session", "signInSubtitle": "Enter your session credentials provided by the administrator", "sessionId": "Session ID", "username": "Username", "password": "Password", "signInButton": "Sign in", "invalidCredentials": "Invalid session credentials. Please check your session ID, username, and password.", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "sessionIdPlaceholder": "Enter your session ID", "usernamePlaceholder": "Enter your username", "passwordPlaceholder": "Enter your password", "unknownSessionCredentials": "Don't have session credentials? Contact your administrator"}, "navigation": {"appTitle": "Ruyi Annotation System", "mySession": "My Session"}, "sessions": {"title": "Your Annotation Session", "loggedInAs": "Logged in as:", "sessionInfo": "Session Information", "annotator": "Annotator", "email": "Email", "status": "Status", "progress": "Progress", "samplesCompleted": "{{completed}} of {{total}} samples completed", "completionRate": "{{rate}}% complete", "startAnnotation": "Start Annotation", "continueAnnotation": "Continue Annotation", "sessionCompleted": "Session Completed", "noSessionData": "No session data available"}, "annotation": {"backToSession": "Back to My Session", "annotationSession": "Annotation Session", "unknownAnnotator": "Unknown Annotator", "sampleImage": "Sample Image", "drawEditAnnotation": "Draw/Edit Annotation", "annotationDetails": "Annotation Details", "correctAnnotation": "Correct Annotation", "taskInstructions": "Task Instructions", "currentProgress": "Current Progress", "sample": "Sample {{current}} of {{total}}", "mode": "Mode", "cancelCorrection": "Cancel Correction", "loadingError": "Failed to load annotation data.", "noSamplesAvailable": "No samples available for annotation.", "submitAnnotation": "Submit Annotation", "skipSample": "<PERSON><PERSON>", "correctThisAnnotation": "Correct This Annotation", "saveCorrection": "Save Correction", "annotationSubmitted": "Annotation submitted successfully!", "correctionSaved": "Correction saved successfully!", "submissionError": "Failed to submit annotation. Please try again."}, "modes": {"grounding": {"title": "Grounding Task", "description": "Find and mark the specified object or region in the image.", "targetDescription": "Target Description", "targetDescriptionPlaceholder": "Enter object description...", "instructions": "Click and drag on the image to draw a bounding box around the target object. If the object is not found, leave the area unmarked.", "summary": "Summary", "task": "Task", "result": "Result", "findTarget": "Find \"{{description}}\"", "targetLocated": "Target located", "targetNotFound": "Target not found"}, "describe": {"title": "Describe Task", "description": "Describe the content within the specified region of the image.", "regionToDescribe": "Region to Describe", "descriptionLabel": "Description of the Region", "descriptionPlaceholder": "Describe what you see in the highlighted region...", "task": "Task", "taskDescription": "Describe the content in the specified region", "region": "Region", "regionSize": "{{width}} × {{height}} pixels", "descriptionLength": "{{length}} characters", "summary": "Task Summary"}, "enumerateText": {"title": "Enumerate Text Task", "description": "List all text elements visible in the image.", "generalDescription": "General Description", "generalDescriptionPlaceholder": "Describe the type of text elements...", "textElements": "Text Elements", "addElement": "Add Element", "removeElement": "Remove", "elementPlaceholder": "Enter text element...", "allTextElements": "All Text Elements", "task": "Task", "findAllText": "Find all {{description}}", "result": "Result", "elementsFound": "{{count}} text element(s) found", "elements": "Elements", "summary": "Task Summary"}, "enumerateCoord": {"title": "Enumerate Coordinates Task", "description": "Find and mark all instances of the specified objects in the image.", "generalDescription": "General Description", "generalDescriptionPlaceholder": "Describe what to find...", "instructions": "Use the interactive canvas above to draw bounding boxes around each instance of the target objects.", "foundElements": "Found Elements", "element": "Element {{index}}", "pixelCoordinates": "Pixel coordinates: ({{x1}}, {{y1}}) → ({{x2}}, {{y2}})", "noElementsFound": "No elements found", "drawInstructions": "Use the interactive canvas above to draw bounding boxes", "task": "Task", "findAllObjects": "Find all {{description}}", "result": "Result", "elementsFoundCount": "{{count}} element(s) found", "summary": "Task Summary"}, "checklist": {"title": "Checklist Task", "description": "Verify the presence or absence of multiple items in the image.", "checklistItems": "Checklist Items", "itemPlaceholder": "Enter checklist item...", "present": "Present", "absent": "Absent", "addItem": "Add Item", "removeItem": "Remove", "results": "Results", "itemsPresent": "{{count}} of {{total}} items present", "summary": "Task Summary"}, "ensure": {"title": "Ensure Task", "description": "Identify the action needed to achieve the desired state. If the state is already achieved, no action is needed.", "expectedState": "Expected State", "expectedStatePlaceholder": "Describe the desired state...", "requiredAction": "Required Action", "clickRequired": "<PERSON><PERSON> Required", "clickInstructions": "Click at the highlighted area to achieve the expected state.", "noActionNeeded": "No Action Needed", "stateAlreadyAchieved": "The expected state is already achieved. No action is required.", "clickCoordinates": "Click Coordinates", "clickArea": "Click area: ({{x1}}, {{y1}}) → ({{x2}}, {{y3}})", "goal": "Goal", "action": "Action", "clickRequiredAtLocation": "Click required at specified location", "noActionNeededState": "No action needed - state already achieved", "drawInstructions": "Click and drag on the image to mark where action is needed, or leave empty if no action is required.", "redrawInstructions": "Click and drag to redraw the action area, or click the existing box to select it.", "summary": "Task Summary"}}}