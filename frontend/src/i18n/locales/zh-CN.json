{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "save": "保存", "delete": "删除", "edit": "编辑", "back": "返回", "next": "下一个", "previous": "上一个", "logout": "退出登录", "login": "登录", "refresh": "刷新", "mode": "模式", "sampleID": "样本号", "batch": "批次", "imageSize": "图像尺寸", "correct": "正确", "incorrect": "错误", "skipSample": "跳过", "createdAt": "创建于"}, "auth": {"signInTitle": "登录到您的标注会话", "signInSubtitle": "请输入管理员提供的会话凭据", "sessionId": "会话ID", "username": "用户名", "password": "密码", "signInButton": "登录", "invalidCredentials": "会话凭据无效。请检查您的会话ID、用户名和密码。", "loginFailed": "登录失败。请重试。", "sessionIdPlaceholder": "请输入会话ID", "usernamePlaceholder": "请输入用户名", "passwordPlaceholder": "请输入密码", "unknownSessionCredentials": "不知道您的会话凭据？请与管理员联系。"}, "navigation": {"appTitle": "如意标注系统", "mySession": "我的会话", "firstUnAnnotated": "第一个没有标注的样本"}, "sessions": {"title": "您的标注会话", "loggedInAs": "登录身份：", "sessionInfo": "会话信息", "annotator": "标注员", "email": "邮箱", "status": "状态", "progress": "进度", "samplesCompleted": "已完成 {{completed}} / {{total}} 个样本", "completionRate": "完成率 {{rate}}%", "startAnnotation": "开始标注", "continueAnnotation": "继续标注", "sessionCompleted": "会话已完成", "noSessionData": "无会话数据"}, "annotation": {"backToSession": "返回我的会话", "annotationSession": "标注会话", "unknownAnnotator": "未知标注员", "sampleImage": "样本图像", "drawEditAnnotation": "绘制/编辑标注", "annotationDetails": "标注详情", "correctAnnotation": "修正标注", "taskInstructions": "任务说明", "currentProgress": "当前进度", "sample": "样本 {{current}} / {{total}}", "mode": "模式", "correctionMode": "修正模式", "correctionHint": "当前处于修正模式。你可以编辑你的标注结果，编辑完成后点击提交修正按钮。", "cancelCorrection": "取消修正", "loadingError": "加载标注数据失败。", "noSamplesAvailable": "没有可用的标注样本。", "submitAnnotation": "提交标注", "cancelSubmit": "取消修改", "skipSample": "跳过样本", "correctThisAnnotation": "修正此标注", "saveCorrection": "保存修正", "annotationSubmitted": "标注提交成功！", "correctionSaved": "修正保存成功！", "submissionError": "提交标注失败。请重试。", "correct": "已标注为正确", "incorrect": "已标注为错误", "skipped": "已跳过", "information": "样本信息", "notes": "备注(可以不写)", "notesPlaceholder": "记录关于这个样本的一些备注...", "prevResult": "上次标注的结果", "update": "更新标注结果"}, "modes": {"grounding": {"title": "定位任务", "description": "在图像中使用框标注符合描述的唯一对象。", "targetDescription": "目标描述", "targetDescriptionPlaceholder": "输入对象描述...", "instructions": "在图像上点击并拖拽以绘制目标对象的边界框。如果未找到对象，请保持区域未标记。", "summary": "摘要", "task": "任务", "result": "结果", "findTarget": "查找 \"{{description}}\"", "targetLocated": "目标已定位", "targetNotFound": "未找到目标", "coordinates": "坐标信息", "clearCoordinate": "清除当前标注的框（标记为元素未找到）"}, "describe": {"title": "描述任务", "description": "描述图像指定区域内的内容。", "regionToDescribe": "待描述区域", "descriptionLabel": "区域描述", "descriptionPlaceholder": "描述您在高亮区域中看到的内容...", "task": "任务", "taskDescription": "描述指定区域中的内容", "region": "区域", "regionSize": "{{width}} × {{height}} 像素", "descriptionLength": "{{length}} 个字符", "summary": "任务摘要", "coordinate": "坐标"}, "enumerateText": {"title": "枚举任务(文本)", "description": "列出图像中可见的所有文本元素。", "generalDescription": "总体描述", "generalDescriptionPlaceholder": "描述文本元素的类型...", "textElements": "文本元素", "addElement": "添加元素", "removeElement": "移除", "elementPlaceholder": "输入文本元素...", "allTextElements": "所有文本元素", "task": "任务", "findAllText": "查找所有 {{description}}", "result": "结果", "elementsFound": "找到 {{count}} 个文本元素", "elements": "元素", "summary": "任务摘要", "noElementsFound": "未找到元素", "addFirstElement": "添加第一个元素"}, "enumerateCoord": {"title": "枚举任务(坐标)", "description": "在图像中找到并使用方框框出符合描述的所有实例。", "generalDescription": "总体描述", "generalDescriptionPlaceholder": "描述要查找的内容...", "instructions": "使用上方的交互画布为每个目标对象实例绘制边界框。", "foundElements": "符合条件的元素", "element": "元素 {{index}}", "pixelCoordinates": "像素坐标：({{x1}}, {{y1}}) → ({{x2}}, {{y2}})", "noElementsFound": "未找到元素", "drawInstructions": "使用上方的交互画布绘制边界框", "task": "任务", "findAllObjects": "查找所有 {{description}}", "result": "结果", "elementsFoundCount": "找到 {{count}} 个元素", "summary": "任务摘要"}, "checklist": {"title": "正误判断任务", "description": "根据图像判断各个描述是否正确。", "checklistItems": "描述列表 ({{correct}}/{{total}} 正确)", "itemPlaceholder": "输入检查清单项目...", "present": "正确", "absent": "错误", "addItem": "添加描述", "removeItem": "移除", "results": "结果", "itemsPresent": "{{total}} 个项目中有 {{count}} 个存在", "summary": "任务摘要", "noChecklistItems": "没有描述", "addFirstItem": "添加第一个描述"}, "ensure": {"title": "确保目标状态符合当前界面状态任务", "description": "识别实现期望状态所需的操作。如果状态已经实现，则无需操作。", "expectedState": "期望状态", "expectedStatePlaceholder": "描述期望的状态...", "requiredAction": "所需操作", "clickRequired": "需要点击", "clickInstructions": "在高亮区域点击以实现期望状态。", "noActionNeeded": "无需操作", "stateAlreadyAchieved": "期望状态已经实现。无需操作。", "clickCoordinates": "点击坐标", "clickArea": "点击区域：({{x1}}, {{y1}}) → ({{x2}}, {{y3}})", "goal": "目标", "action": "操作", "clickRequiredAtLocation": "需要在指定位置点击", "noActionNeededState": "无需操作 - 状态已实现", "drawInstructions": "在图像上点击并拖拽以标记需要操作的位置，如果无需操作请保持空白。", "redrawInstructions": "点击并拖拽重新绘制操作区域，或点击现有框选择它。", "summary": "任务摘要"}}}