import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAdminAuth } from '../contexts/AdminAuthContext';
import { adminApi } from '../services/api';
import AdminLayout from '../components/AdminLayout';
import DashboardOverview from '../components/admin/DashboardOverview';
import SessionsManagement from '../components/admin/SessionsManagement';
import ActivityMonitor from '../components/admin/ActivityMonitor';
import SystemHealth from '../components/admin/SystemHealth';

type TabType = 'overview' | 'sessions' | 'activity' | 'health';

const AdminDashboard: React.FC = () => {
  const { dashboardPath } = useParams<{ dashboardPath: string }>();
  const { isAuthenticated, updateActivity } = useAdminAuth();
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Update activity on user interaction
  useEffect(() => {
    const handleActivity = () => updateActivity();
    
    // Track various user activities
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [updateActivity]);

  // Verify admin access on mount
  useEffect(() => {
    const verifyAccess = async () => {
      if (!isAuthenticated || !dashboardPath) {
        setError('Admin authentication required');
        setLoading(false);
        return;
      }

      try {
        // Verify admin info to ensure access is still valid
        await adminApi.getAdminInfo();
        setLoading(false);
      } catch (err) {
        setError('Failed to verify admin access');
        setLoading(false);
      }
    };

    verifyAccess();
  }, [isAuthenticated, dashboardPath]);

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading dashboard...</span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      </AdminLayout>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'sessions', name: 'Sessions', icon: '👥' },
    { id: 'activity', name: 'Activity', icon: '📈' },
    { id: 'health', name: 'System Health', icon: '🔧' },
  ] as const;

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview />;
      case 'sessions':
        return <SessionsManagement />;
      case 'activity':
        return <ActivityMonitor />;
      case 'health':
        return <SystemHealth />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-sm text-gray-500">
                Real-time monitoring and management for Ruyi Dataverse
              </p>
            </div>
            <div className="text-sm text-gray-500">
              Dashboard: /{dashboardPath}
            </div>
          </div>

          {/* Tab Navigation */}
          <nav className="mt-4 -mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1">
          {renderTabContent()}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
