import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { adminApi, samplesApi } from '../services/api';
import { ModeSpecificAnnotation } from '../components/annotation';
import AdminLayout from '../components/AdminLayout';

interface SessionDetail {
  id: number;
  name: string;
  description: string;
  annotator_name: string;
  annotator_email: string;
  status: string;
  created_at: string;
  started_at: string | null;
  completed_at: string | null;
  last_activity_at: string | null;
  progress: {
    session_id: number;
    session_name: string;
    status: string;
    total_samples: number;
    completed_samples: number;
    correct_samples: number;
    incorrect_samples: number;
    skipped_samples: number;
    progress_percentage: number;
    created_at: string;
    started_at: string | null;
    completed_at: string | null;
    last_activity_at: string | null;
  };
  query_filters: any;
}

interface AnnotationPreview {
  annotation: {
    id: number;
    result: string;
    annotated_at: string;
    annotation_duration: number | null;
    notes: string | null;
    has_corrections: boolean;
  };
  sample: {
    id: number;
    mode: string;
    labels: string[];
    original_metadata: any;
    corrected_metadata: any | null;
  };
  image: {
    id: number;
    width: number;
    height: number;
    file_size: number;
  };
}

const AdminSessionDetail: React.FC = () => {
  const { dashboardPath, sessionId } = useParams<{ dashboardPath: string; sessionId: string }>();
  const navigate = useNavigate();
  const [session, setSession] = useState<SessionDetail | null>(null);
  const [annotations, setAnnotations] = useState<AnnotationPreview[]>([]);
  const [loading, setLoading] = useState(true);
  const [annotationsLoading, setAnnotationsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [resultFilter, setResultFilter] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'details' | 'annotations'>('details');

  useEffect(() => {
    if (sessionId) {
      fetchSessionDetails();
    }
  }, [sessionId]);

  useEffect(() => {
    if (sessionId && activeTab === 'annotations') {
      fetchAnnotations();
    }
  }, [sessionId, activeTab, resultFilter]);

  const fetchSessionDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await adminApi.getSessionDetails(parseInt(sessionId!));
      // The API returns session and progress as separate objects, so we need to merge them
      const sessionData = {
        ...response.data.session,
        progress: response.data.progress
      };
      setSession(sessionData);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to load session details');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnnotations = async () => {
    setAnnotationsLoading(true);
    try {
      const response = await adminApi.getSessionPreview(
        parseInt(sessionId!),
        20,
        resultFilter || undefined
      );
      setAnnotations(response.data.annotations || []);
      setCurrentIndex(0);
    } catch (err: any) {
      console.error('Failed to load annotations:', err);
    } finally {
      setAnnotationsLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const getResultBadgeColor = (result: string) => {
    switch (result) {
      case 'correct':
        return 'bg-green-100 text-green-800';
      case 'incorrect':
        return 'bg-red-100 text-red-800';
      case 'skipped':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const formatDuration = (seconds: number | null) => {
    if (!seconds) return 'N/A';
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const currentAnnotation = annotations[currentIndex];

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={() => navigate(`/${dashboardPath}/dashboard`)}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </AdminLayout>
    );
  }

  if (!session) {
    return (
      <AdminLayout>
        <div className="text-center text-gray-500 py-12">
          <p>Session not found.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header with Breadcrumb */}
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <button
                onClick={() => navigate(`/${dashboardPath}/dashboard`)}
                className="hover:text-gray-700"
              >
                Dashboard
              </button>
              <span>›</span>
              <span className="text-gray-900">Session Details</span>
            </nav>
            <h1 className="text-2xl font-bold text-gray-900">{session.name}</h1>
            <p className="text-sm text-gray-600 mt-1">
              Annotator: {session.annotator_name}
              {session.annotator_email && ` (${session.annotator_email})`}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
              session.status === 'completed' ? 'bg-green-100 text-green-800' :
              session.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {session.status.replace('_', ' ')}
            </span>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('details')}
              className={`${
                activeTab === 'details'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
            >
              Session Details
            </button>
            {session.progress && session.progress.completed_samples > 0 && (
              <button
                onClick={() => setActiveTab('annotations')}
                className={`${
                  activeTab === 'annotations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
              >
                Annotation Preview ({session.progress.completed_samples})
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1">
          {activeTab === 'details' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Session Information */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Session Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <div className="text-sm text-gray-900">{session.description || 'No description'}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Created</label>
                      <div className="text-sm text-gray-900">{formatDate(session.created_at)}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Started</label>
                      <div className="text-sm text-gray-900">{formatDate(session.started_at)}</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Completed</label>
                      <div className="text-sm text-gray-900">{formatDate(session.completed_at)}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Last Activity</label>
                      <div className="text-sm text-gray-900">{formatDate(session.last_activity_at)}</div>
                    </div>
                  </div>
                  {session.query_filters && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Query Filters</label>
                      <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded mt-1">
                        {JSON.stringify(session.query_filters, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>

              {/* Progress Statistics */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Progress Statistics</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Overall Progress</span>
                      <span>{session.progress?.progress_percentage || 0}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${session.progress?.progress_percentage || 0}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {session.progress?.completed_samples || 0} / {session.progress?.total_samples || 0} samples
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{session.progress?.correct_samples || 0}</div>
                      <div className="text-xs text-gray-500">Correct</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{session.progress?.incorrect_samples || 0}</div>
                      <div className="text-xs text-gray-500">Incorrect</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-600">{session.progress?.skipped_samples || 0}</div>
                      <div className="text-xs text-gray-500">Skipped</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'annotations' && (
            <div className="space-y-6">
              {/* Annotation Controls */}
              <div className="bg-white shadow rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Filter by Result
                      </label>
                      <select
                        value={resultFilter}
                        onChange={(e) => setResultFilter(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                      >
                        <option value="">All Results</option>
                        <option value="correct">Correct</option>
                        <option value="incorrect">Incorrect</option>
                        <option value="skipped">Skipped</option>
                      </select>
                    </div>
                  </div>

                  {annotations.length > 0 && (
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">
                        {currentIndex + 1} of {annotations.length}
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
                          disabled={currentIndex === 0}
                          className="px-3 py-1 bg-blue-600 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed text-sm"
                        >
                          Previous
                        </button>
                        <button
                          onClick={() => setCurrentIndex(Math.min(annotations.length - 1, currentIndex + 1))}
                          disabled={currentIndex === annotations.length - 1}
                          className="px-3 py-1 bg-blue-600 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed text-sm"
                        >
                          Next
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Annotation Content */}
              {annotationsLoading && (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}

              {!annotationsLoading && annotations.length === 0 && (
                <div className="bg-white shadow rounded-lg p-12 text-center text-gray-500">
                  <p>No annotations found for this session.</p>
                </div>
              )}

              {!annotationsLoading && currentAnnotation && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Left Side - Compact Image */}
                  <div className="bg-white shadow rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-sm font-medium text-gray-900">Sample Image</h3>
                      <div className="flex items-center space-x-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getResultBadgeColor(currentAnnotation.annotation.result)}`}>
                          {currentAnnotation.annotation.result}
                        </span>
                        {currentAnnotation.annotation.has_corrections && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Corrected
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="max-w-xs mx-auto">
                      <ModeSpecificAnnotation
                        mode={currentAnnotation.sample.mode}
                        imageUrl={samplesApi.getImage(currentAnnotation.sample.id)}
                        metadata={currentAnnotation.annotation.has_corrections
                          ? currentAnnotation.sample.corrected_metadata
                          : currentAnnotation.sample.original_metadata}
                        isEditing={false}
                        imageOnly={true}
                        compact={true}
                      />
                    </div>
                  </div>

                  {/* Right Side - Details (2 columns) */}
                  <div className="lg:col-span-2 space-y-4">
                    {/* Annotation Info */}
                    <div className="bg-white shadow rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Annotation Info</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Result:</span>
                          <span className="ml-2 font-medium">{currentAnnotation.annotation.result}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Duration:</span>
                          <span className="ml-2 font-medium">{formatDuration(currentAnnotation.annotation.annotation_duration)}</span>
                        </div>
                        <div className="col-span-2">
                          <span className="text-gray-500">Annotated At:</span>
                          <span className="ml-2 font-medium">
                            {new Date(currentAnnotation.annotation.annotated_at).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Sample Info */}
                    <div className="bg-white shadow rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Sample Info</h4>
                      <div className="text-sm space-y-2">
                        <div>
                          <span className="text-gray-500">Mode:</span>
                          <span className="ml-2 font-medium">{currentAnnotation.sample.mode}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Labels:</span>
                          <span className="ml-2 font-medium">
                            {currentAnnotation.sample.labels.join(', ') || 'None'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Image Size:</span>
                          <span className="ml-2 font-medium">
                            {currentAnnotation.image.width} × {currentAnnotation.image.height}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Task Description and Metadata */}
                    <div className="bg-white shadow rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Task Details</h4>
                      <ModeSpecificAnnotation
                        mode={currentAnnotation.sample.mode}
                        imageUrl={samplesApi.getImage(currentAnnotation.sample.id)}
                        metadata={currentAnnotation.annotation.has_corrections 
                          ? currentAnnotation.sample.corrected_metadata 
                          : currentAnnotation.sample.original_metadata}
                        isEditing={false}
                        imageOnly={false}
                      />
                    </div>

                    {/* Notes */}
                    {currentAnnotation.annotation.notes && (
                      <div className="bg-white shadow rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-3">Notes</h4>
                        <p className="text-sm text-gray-600">{currentAnnotation.annotation.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSessionDetail;
