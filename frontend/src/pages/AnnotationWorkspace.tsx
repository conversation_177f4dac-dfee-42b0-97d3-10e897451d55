import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { annotationApi, samplesApi, AnnotationSession, Sample, AnnotationResult } from '../services/api';
import ModeSpecificAnnotation from '../components/annotation/ModeSpecificAnnotation';

const AnnotationWorkspace: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { sessionCredentials } = useAuth();
  const { t } = useTranslation();

  // Check if user has access to this session
  useEffect(() => {
    if (sessionId && sessionCredentials) {
      const requestedSessionId = Number(sessionId);
      if (sessionCredentials.sessionId !== requestedSessionId) {
        // Redirect to their own session
        navigate(`/sessions/${sessionCredentials.sessionId}/annotate`, { replace: true });
        return;
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId, sessionCredentials?.sessionId, navigate]); // Only depend on session identity
  
  const [session, setSession] = useState<AnnotationSession | null>(null);
  const [currentSample, setCurrentSample] = useState<Sample | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notes, setNotes] = useState('');
  const [startTime, setStartTime] = useState<number>(Date.now());
  const [isCorrectingMode, setIsCorrectingMode] = useState(false);
  const [correctedMetadata, setCorrectedMetadata] = useState<unknown>(null);
  const [allSamples, setAllSamples] = useState<Sample[]>([]);
  const [currentSampleIndex, setCurrentSampleIndex] = useState(0);
  const [correctedSampleIds, setCorrectedSampleIds] = useState<Set<number>>(new Set());
  const [currentAnnotationResult, setCurrentAnnotationResult] = useState<AnnotationResult | null>(null);
  const [isAutoNavigating, setIsAutoNavigating] = useState(false);

  useEffect(() => {
    if (sessionId) {
      loadSession();
      loadAllSamples();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  const loadSession = async () => {
    try {
      const response = await annotationApi.getSession(Number(sessionId));
      setSession(response.data);
    } catch (err) {
      setError('Failed to load session');
      console.error('Error loading session:', err);
    }
  };

  const loadCurrentAnnotationResult = async (sampleId: number) => {
    if (!sessionId) return;

    try {
      const response = await annotationApi.getResultBySample(Number(sessionId), sampleId);
      setCurrentAnnotationResult(response.data);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (err) {
      // It's okay if there's no annotation result yet
      setCurrentAnnotationResult(null);
      console.log('No annotation result found for sample:', sampleId);
    }
  };

  const loadAllSamples = async () => {
    try {
      setLoading(true);
      const response = await annotationApi.getSessionSamples(Number(sessionId), 1000, 0);
      setAllSamples(response.data.items);
      if (response.data.items.length > 0) {
        const firstSample = response.data.items[0];
        setCurrentSample(firstSample);
        setCurrentSampleIndex(0);
        // Load annotation result for the first sample
        await loadCurrentAnnotationResult(firstSample.id);

        // Automatically navigate to the first unannotated sample when entering the session
        setTimeout(async () => {
          await goToFirstUnannotatedSample(false);
        }, 100);
      }
      setStartTime(Date.now());
      setNotes('');
      setError(null);
      setIsCorrectingMode(false);
      setCorrectedMetadata(null);
      setCorrectedSampleIds(new Set()); // Reset corrected samples tracking for new session
    } catch (err) {
      setError('Failed to load samples');
      console.error('Error loading samples:', err);
    } finally {
      setLoading(false);
    }
  };

  const navigateToSample = async (index: number) => {
    if (index >= 0 && index < allSamples.length) {
      const sample = allSamples[index];
      setCurrentSample(sample);
      setCurrentSampleIndex(index);
      setStartTime(Date.now());
      setNotes('');
      setIsCorrectingMode(false);
      setCorrectedMetadata(null);
      // Don't reset correctedSampleIds when navigating - keep track of all corrections in this session

      // Load annotation result for this sample
      await loadCurrentAnnotationResult(sample.id);
    }
  };

  const goToPreviousSample = () => {
    if (currentSampleIndex > 0) {
      navigateToSample(currentSampleIndex - 1);
    }
  };

  const goToNextSample = () => {
    if (currentSampleIndex < allSamples.length - 1) {
      navigateToSample(currentSampleIndex + 1);
    }
  };

  const goToFirstUnannotatedSample = async (showMessage = true) => {
    if (!sessionId) return;

    // Find the first sample that doesn't have an annotation result
    for (let i = 0; i < allSamples.length; i++) {
      try {
        const response = await annotationApi.getResultBySample(Number(sessionId), allSamples[i].id);
        if (!response.data) {
          // This sample is not annotated, navigate to it
          await navigateToSample(i);
          return true; // Successfully found and navigated to unannotated sample
        }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (err) {
        // If there's an error (likely 404), the sample is not annotated
        await navigateToSample(i);
        return true; // Successfully found and navigated to unannotated sample
      }
    }

    // If all samples are annotated, show a message only if requested
    if (showMessage) {
      setError('🎉 Congratulations! All samples in this session have been annotated.');
      setTimeout(() => setError(null), 4000);
    }
    return false; // No unannotated samples found
  };

  const submitAnnotation = async (result: 'correct' | 'incorrect' | 'skipped', correctedMetadata?: unknown) => {
    if (!currentSample || !sessionId) return;

    setSubmitting(true);
    try {
      const duration = Math.floor((Date.now() - startTime) / 1000);

      await annotationApi.submitResult(Number(sessionId), {
        session_id: Number(sessionId),
        sample_id: currentSample.id,
        result,
        corrected_metadata: correctedMetadata,
        notes: notes || undefined,
        annotation_duration: duration,
      }, currentAnnotationResult !== null); // Allow update if sample is already annotated

      // If we submitted a correction, update the current sample to show the corrected metadata
      if (result === 'incorrect' && correctedMetadata) {
        const updatedSample = { ...currentSample, sample_metadata: correctedMetadata };
        setCurrentSample(updatedSample);

        // Also update the sample in the allSamples array
        const updatedAllSamples = allSamples.map(sample =>
          sample.id === currentSample.id
            ? { ...sample, sample_metadata: correctedMetadata }
            : sample
        );
        setAllSamples(updatedAllSamples);

        // Track that this sample has been corrected
        setCorrectedSampleIds(prev => new Set(prev).add(currentSample.id));
      }

      // Reset correction mode and notes
      setIsCorrectingMode(false);
      setCorrectedMetadata(null);
      setNotes('');
      setStartTime(Date.now());

      // Refresh session stats and current annotation result
      await loadSession();
      await loadCurrentAnnotationResult(currentSample.id);

      // Automatically navigate to the first unannotated sample
      setTimeout(async () => {
        setIsAutoNavigating(true);
        try {
          const foundUnannotated = await goToFirstUnannotatedSample(false);
          if (!foundUnannotated) {
            // If no unannotated samples found, show completion message
            setError('🎉 Congratulations! All samples in this session have been annotated.');
            setTimeout(() => setError(null), 4000);
          }
        } finally {
          setIsAutoNavigating(false);
        }
      }, 500); // Small delay to ensure the annotation is processed
    } catch (err) {
      setError('Failed to submit annotation');
      console.error('Error submitting annotation:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCorrect = () => {
    submitAnnotation('correct');
  };

  const handleIncorrect = () => {
    if (!isCorrectingMode) {
      // Enter correction mode
      setIsCorrectingMode(true);
      setCorrectedMetadata(currentSample?.sample_metadata);
    } else {
      // Submit with corrections
      submitAnnotation('incorrect', correctedMetadata);
    }
  };

  const handleCancelCorrection = () => {
    setIsCorrectingMode(false);
    setCorrectedMetadata(null);
  };

  const handleSkip = () => {
    submitAnnotation('skipped');
  };

  if (loading && !currentSample) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!currentSample) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Session Complete!</h2>
        <p className="text-gray-600 mb-6">
          All samples in this session have been annotated.
        </p>
        <button
          onClick={() => navigate('/sessions')}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Back to Sessions
        </button>
      </div>
    );
  }

  return (
    <div className="px-4 py-6 sm:px-0">
      {/* Compact Header with Integrated Navigation */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <div>
              <div className="flex items-center space-x-4 mb-2">
                <button
                  onClick={() => navigate('/sessions')}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                  {t('annotation.backToSession')}
                </button>
                <h1 className="text-2xl font-bold text-gray-900">
                  {session?.name || t('annotation.annotationSession')}
                </h1>
              </div>
              <p className="text-sm text-gray-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
                <span>{session?.annotator_name || t('annotation.unknownAnnotator')}</span>
                {session?.annotator_email && (
                  <span className="ml-2 text-gray-500">({session.annotator_email})</span>
                )}
                <span className="mx-2">•</span>
                <span>Mode: <span className="font-medium">{currentSample.mode}</span></span>
                <span className="mx-2">•</span>
                <span>Sample <span className="font-medium">{currentSampleIndex + 1}</span> of{' '}
                <span className="font-medium">{allSamples.length}</span></span>
              </p>
            </div>

            {/* Navigation Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={goToPreviousSample}
                disabled={currentSampleIndex === 0}
                className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                ← {t('common.previous')}
              </button>

              <button
                onClick={goToNextSample}
                disabled={currentSampleIndex === allSamples.length - 1}
                className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {t('common.next')} →
              </button>

              <button
                onClick={() => goToFirstUnannotatedSample(true)}
                className="inline-flex items-center px-2 py-1 border border-blue-300 shadow-sm text-xs font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                {t('navigation.firstUnAnnotated')}
              </button>
            </div>
          </div>

          <div className="text-right">
            <div className="text-sm text-gray-600">
              {t('sessions.samplesCompleted', { completed: session?.completed_samples || 0, total: session?.total_samples || 0 })}
            </div>
            <div className="w-48 bg-gray-200 rounded-full h-2 mt-1">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{
                  width: `${
                    session && session.total_samples > 0
                      ? (session.completed_samples / session.total_samples) * 100
                      : 0
                  }%`,
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className={`mb-6 rounded-md p-4 ${
          error.includes('🎉')
            ? 'bg-green-50 border border-green-200'
            : 'bg-red-50 border border-red-200'
        }`}>
          <div className={`text-sm ${
            error.includes('🎉') ? 'text-green-600' : 'text-red-600'
          }`}>{error}</div>
        </div>
      )}

      {isAutoNavigating && (
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
            <div className="text-sm text-blue-600">
              Navigating to next unannotated sample...
            </div>
          </div>
        </div>
      )}

      {isCorrectingMode && (
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">{t('annotation.correctionMode')}</h3>
              <div className="mt-1 text-sm text-blue-700">
                {t('annotation.correctionHint')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sample Display - Side by Side Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Side - Image Only */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {isCorrectingMode ? t('annotation.drawEditAnnotation') : t('annotation.sampleImage')}
            </h3>
            {isCorrectingMode && (
              <button
                onClick={handleCancelCorrection}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                {t('common.cancel')}
              </button>
            )}
          </div>

          <ModeSpecificAnnotation
            mode={currentSample.mode}
            imageUrl={samplesApi.getImage(currentSample.id)}
            metadata={isCorrectingMode ? correctedMetadata : currentSample.sample_metadata}
            isEditing={isCorrectingMode}
            onMetadataChange={setCorrectedMetadata}
            imageOnly={true}
          />
        </div>

        {/* Right Side - Metadata and Controls */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {isCorrectingMode ? t('annotation.correctAnnotation') : t('annotation.annotationDetails')}
          </h3>

          {/* Task Description and Metadata */}
          <div className="mb-6">
            <ModeSpecificAnnotation
              mode={currentSample.mode}
              imageUrl={samplesApi.getImage(currentSample.id)}
              metadata={isCorrectingMode ? correctedMetadata : currentSample.sample_metadata}
              isEditing={isCorrectingMode}
              onMetadataChange={setCorrectedMetadata}
              imageOnly={false}
            />
          </div>

          {/* Sample Details */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-medium text-gray-700">{t('annotation.information')}</h4>
              <div className="flex items-center space-x-2">
                {currentAnnotationResult && (
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    currentAnnotationResult.result === 'correct'
                      ? 'bg-green-100 text-green-800'
                      : currentAnnotationResult.result === 'incorrect'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      {currentAnnotationResult.result === 'correct' ? (
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      ) : currentAnnotationResult.result === 'incorrect' ? (
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      ) : (
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      )}
                    </svg>
                    {currentAnnotationResult.result === 'correct' ? t('annotation.correct') :
                     currentAnnotationResult.result === 'incorrect' ? t('annotation.incorrect') : t('annotation.skipped')}
                  </span>
                )}
                {correctedSampleIds.has(currentSample.id) && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Updated
                  </span>
                )}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">{t('common.mode')}:</span>
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                  {currentSample.mode}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">{t('common.sampleID')}:</span>
                <span className="ml-2 text-gray-600">{currentSample.id}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">{t('common.batch')}:</span>
                <span className="ml-2 text-gray-600">{currentSample.batch.name}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">{t('common.imageSize')}:</span>
                <span className="ml-2 text-gray-600">
                  {currentSample.image.width} × {currentSample.image.height}
                </span>
              </div>
            </div>
          </div>

          {/* Labels */}
          {/* {currentSample.labels && currentSample.labels.length > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Labels</h4>
              <div className="flex flex-wrap gap-2">
                {currentSample.labels.map((label, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {label}
                  </span>
                ))}
              </div>
            </div>
          )} */}

          {/* Notes */}
          <div className="mb-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
              {t('annotation.notes')}
            </label>
            <textarea
              id="notes"
              rows={3}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder={t('annotation.notesPlaceholder')}
            />
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            {currentAnnotationResult && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="text-sm text-blue-800">
                  <strong>{t('annotation.prevResult')}:</strong> {currentAnnotationResult.result}
                  {currentAnnotationResult.annotated_at && (
                    <div className="text-xs text-blue-600 mt-1">
                      {new Date(currentAnnotationResult.annotated_at).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="space-y-3">
              {!isCorrectingMode ? (
                <div className="grid grid-cols-3 gap-3">
                  <button
                    onClick={handleCorrect}
                    disabled={submitting}
                    className={`inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
                      currentAnnotationResult?.result === 'correct'
                        ? 'bg-green-700 hover:bg-green-800 focus:ring-green-500'
                        : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                    }`}
                  >
                    ✓ {currentAnnotationResult ? t('annotation.update') + ':' + t('common.correct') : t('common.correct')}
                  </button>
                  <button
                    onClick={handleIncorrect}
                    disabled={submitting}
                    className={`inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
                      currentAnnotationResult?.result === 'incorrect'
                        ? 'bg-red-700 hover:bg-red-800 focus:ring-red-500'
                        : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                    }`}
                  >
                    ✗ {currentAnnotationResult ? t('annotation.update') + ':' + t('common.incorrect') : t('common.incorrect')}
                  </button>
                  <button
                    onClick={handleSkip}
                    disabled={submitting}
                    className={`inline-flex justify-center items-center px-3 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
                      currentAnnotationResult?.result === 'skipped'
                        ? 'border-yellow-300 text-yellow-800 bg-yellow-50 hover:bg-yellow-100 focus:ring-yellow-500'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500'
                    }`}
                  >
                    {currentAnnotationResult ? t('annotation.update') + ':' + t('common.skipSample') : t('common.skipSample')}
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={handleCancelCorrection}
                    disabled={submitting}
                    className="inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"
                  >
                    {t('annotation.cancelSubmit')}
                  </button>
                  <button
                    onClick={handleIncorrect}
                    disabled={submitting}
                    className="inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    ✓ {t('annotation.submitAnnotation')}
                  </button>
                </div>
              )}
            </div>

            {submitting && (
              <div className="text-center">
                <div className="inline-flex items-center text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                  Submitting...
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnotationWorkspace;
