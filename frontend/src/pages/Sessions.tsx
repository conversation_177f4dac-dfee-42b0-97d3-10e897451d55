import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { annotationApi, AnnotationSession } from '../services/api';

const Sessions: React.FC = () => {
  const { sessionCredentials } = useAuth();
  const { t } = useTranslation();
  const [session, setSession] = useState<AnnotationSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSession();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionCredentials?.sessionId, sessionCredentials?.username]); // Only reload when session identity changes

  const loadSession = async () => {
    if (!sessionCredentials) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await annotationApi.getSession(sessionCredentials.sessionId);
      setSession(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to load session');
      console.error('Error loading session:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">{t('sessions.title')}</h1>
          <p className="mt-2 text-sm text-gray-700">
            {t('sessions.loggedInAs')} <span className="font-medium">{sessionCredentials?.username}</span>
          </p>
        </div>
      </div>

      {error && (
        <div className="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Session Card */}
      {session && (
        <div className="mt-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="sm:flex sm:items-center sm:justify-between">
                <div className="sm:flex sm:items-center">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {session.name}
                    </h3>
                    {session.description && (
                      <p className="mt-1 text-sm text-gray-500">
                        {session.description}
                      </p>
                    )}
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          session.status
                        )}`}
                      >
                        {session.status.replace('_', ' ')}
                      </span>
                      <span className="ml-4">
                        {t('common.createdAt')}: {formatDate(session.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:flex sm:items-center">
                  <Link
                    to={`/sessions/${session.id}/annotate`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {t('sessions.startAnnotation')}
                  </Link>
                </div>
              </div>

              {/* Progress */}
              <div className="mt-6">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                  <span>{t('sessions.progress')}</span>
                  <span>{t('sessions.samplesCompleted', { completed: session.completed_samples, total: session.total_samples })}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{
                      width: `${
                        session.total_samples > 0
                          ? (session.completed_samples / session.total_samples) * 100
                          : 0
                      }%`,
                    }}
                  ></div>
                </div>

                {/* Stats */}
                <div className="mt-4 grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-semibold text-green-600">{session.correct_samples}</div>
                    <div className="text-xs text-gray-500">{t('common.correct')}</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-red-600">{session.incorrect_samples}</div>
                    <div className="text-xs text-gray-500">{t('common.incorrect')}</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-gray-600">{session.skipped_samples}</div>
                    <div className="text-xs text-gray-500">{t('common.skipSample')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {!session && !loading && (
        <div className="mt-8 text-center py-12">
          <div className="text-sm text-gray-500">
            {t('sessions.noSessionData')}
          </div>
        </div>
      )}
    </div>
  );
};

export default Sessions;
