import axios from 'axios';

// API Configuration
const API_BASE_URL = 'http://localhost:8001/api/v1';

// Create separate axios instances for annotator and admin APIs
const annotatorApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

const adminApiInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Session authentication state
interface SessionAuth {
  sessionId: number;
  username: string;
  password: string;
}

let currentSessionAuth: SessionAuth | null = null;

// Set session authentication credentials
export const setSessionAuth = (auth: SessionAuth | null) => {
  currentSessionAuth = auth;

  if (auth) {
    // Add session authentication headers to annotator API only
    annotatorApi.defaults.headers['X-Session-Username'] = auth.username;
    annotatorApi.defaults.headers['X-Session-Password'] = auth.password;
  } else {
    // Remove session authentication headers
    delete annotatorApi.defaults.headers['X-Session-Username'];
    delete annotatorApi.defaults.headers['X-Session-Password'];
  }
};

// Get current session authentication
export const getCurrentSessionAuth = (): SessionAuth | null => {
  return currentSessionAuth;
};

// Admin authentication state
interface AdminAuth {
  dashboardPath: string;
  password: string;
}

let currentAdminAuth: AdminAuth | null = null;

// Set admin authentication credentials
export const setAdminAuth = (auth: AdminAuth | null) => {
  currentAdminAuth = auth;

  if (auth) {
    // Add admin authentication headers to admin API only
    adminApiInstance.defaults.headers['X-Admin-Path'] = auth.dashboardPath;
    adminApiInstance.defaults.headers['X-Admin-Password'] = auth.password;
  } else {
    // Remove admin authentication headers
    delete adminApiInstance.defaults.headers['X-Admin-Path'];
    delete adminApiInstance.defaults.headers['X-Admin-Password'];
  }
};

// Get current admin authentication
export const getCurrentAdminAuth = (): AdminAuth | null => {
  return currentAdminAuth;
};

// Activity tracking callback
let activityCallback: (() => void) | null = null;

// Set activity tracking callback
export const setActivityCallback = (callback: (() => void) | null) => {
  activityCallback = callback;
};

// Add request interceptor to track activity for both APIs
annotatorApi.interceptors.request.use((config) => {
  // Update activity on any API request
  if (activityCallback) {
    activityCallback();
  }
  return config;
});

adminApiInstance.interceptors.request.use((config) => {
  // Update activity on any API request
  if (activityCallback) {
    activityCallback();
  }
  return config;
});

// Types
export interface Batch {
  id: number;
  name: string;
  description: string;
  created_at: string;
}

export interface Image {
  id: number;
  hash_value: string;
  file_path: string;
  file_size: number;
  width: number;
  height: number;
  created_at: string;
}

export interface Sample {
  id: number;
  mode: string;
  sample_metadata: unknown;
  labels: string[];
  image_id: number;
  batch_id: number;
  created_at: string;
  image: Image;
  batch: Batch;
}

export interface AnnotationSession {
  id: number;
  name: string;
  description?: string;
  annotator_name: string;
  annotator_email?: string;
  query_filters: {
    batch_ids?: number[];
    modes?: string[];
    labels?: string[];
  };
  status: 'pending' | 'in_progress' | 'completed' | 'paused';
  total_samples: number;
  completed_samples: number;
  correct_samples: number;
  incorrect_samples: number;
  skipped_samples: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  last_activity_at?: string;
}

export interface AnnotationResult {
  id: number;
  session_id: number;
  sample_id: number;
  result: 'correct' | 'incorrect' | 'skipped';
  original_metadata: unknown;
  corrected_metadata?: unknown;
  notes?: string;
  annotated_at: string;
  annotation_duration?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  limit: number;
  offset: number;
}

// API Functions - using annotatorApi for session-based endpoints
export const batchesApi = {
  list: () => annotatorApi.get<Batch[]>('/batches'),
  getById: (id: number) => annotatorApi.get<Batch>(`/batches/${id}`),
  getStatistics: (id: number) => annotatorApi.get(`/batches/${id}/statistics`),
  getSamples: (id: number, limit = 100, offset = 0) =>
    annotatorApi.get<PaginatedResponse<Sample>>(`/batches/${id}/samples?limit=${limit}&offset=${offset}`),
};

export const samplesApi = {
  query: (params: {
    batch_ids?: string;
    modes?: string;
    labels?: string;
    limit?: number;
    offset?: number;
  }) => {
    const searchParams = new URLSearchParams();
    if (params.batch_ids) searchParams.set('batch_ids', params.batch_ids);
    if (params.modes) searchParams.set('modes', params.modes);
    if (params.labels) searchParams.set('labels', params.labels);
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());

    return annotatorApi.get<PaginatedResponse<Sample>>(`/samples?${searchParams.toString()}`);
  },
  getById: (id: number) => annotatorApi.get<Sample>(`/samples/${id}`),
  getImage: (id: number) => `${API_BASE_URL}/samples/${id}/image`,
};

// Authentication API - using annotatorApi for session authentication
export const authApi = {
  // Verify session credentials
  verifySession: (sessionId: number, username: string, password: string) =>
    annotatorApi.post('/auth/verify-session', {
      session_id: sessionId,
      username,
      password,
    }),
};

export const annotationApi = {
  // Session management - removed createSession as it's CLI-only

  listSessions: (annotator_name?: string, limit = 100, offset = 0) => {
    const params = new URLSearchParams();
    if (annotator_name) params.set('annotator_name', annotator_name);
    params.set('limit', limit.toString());
    params.set('offset', offset.toString());
    return annotatorApi.get<AnnotationSession[]>(`/sessions?${params.toString()}`);
  },

  getSession: (id: number) => annotatorApi.get<AnnotationSession>(`/sessions/${id}`),

  updateSession: (id: number, data: {
    name?: string;
    description?: string;
    status?: string;
  }) => annotatorApi.put<AnnotationSession>(`/sessions/${id}`, data),

  startSession: (id: number) => annotatorApi.post<AnnotationSession>(`/sessions/${id}/start`),

  completeSession: (id: number) => annotatorApi.post<AnnotationSession>(`/sessions/${id}/complete`),

  getProgress: (id: number) => annotatorApi.get(`/sessions/${id}/progress`),

  // Sample management
  getSessionSamples: (id: number, limit = 100, offset = 0) =>
    annotatorApi.get<PaginatedResponse<Sample>>(`/sessions/${id}/samples?limit=${limit}&offset=${offset}`),

  getNextSample: (id: number) => annotatorApi.get<Sample | null>(`/sessions/${id}/next-sample`),
  
  // Annotation results
  submitResult: (sessionId: number, data: {
    session_id: number;
    sample_id: number;
    result: 'correct' | 'incorrect' | 'skipped';
    corrected_metadata?: unknown;
    notes?: string;
    annotation_duration?: number;
  }, allowUpdate = false) => {
    const params = new URLSearchParams();
    if (allowUpdate) params.set('allow_update', 'true');
    return annotatorApi.post<AnnotationResult>(`/sessions/${sessionId}/results?${params.toString()}`, data);
  },

  getResults: (sessionId: number, limit = 100, offset = 0) =>
    annotatorApi.get<PaginatedResponse<AnnotationResult>>(`/sessions/${sessionId}/results?limit=${limit}&offset=${offset}`),

  getResultBySample: (sessionId: number, sampleId: number) =>
    annotatorApi.get<AnnotationResult | null>(`/sessions/${sessionId}/results/${sampleId}`),
};

// Admin API endpoints - using adminApiInstance for admin authentication
export const adminApiEndpoints = {
  // Admin authentication
  verifyCredentials: (dashboardPath: string, password: string) =>
    adminApiInstance.post('/admin/auth/verify', { dashboard_path: dashboardPath, password }),

  getAdminInfo: () => adminApiInstance.get('/admin/info'),

  // Dashboard overview
  getDashboardOverview: () => adminApiInstance.get('/admin/dashboard/overview'),

  // Session management
  getAllSessions: (status?: string, limit = 50, offset = 0) => {
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    return adminApiInstance.get(`/admin/dashboard/sessions?${params.toString()}`);
  },

  getSessionDetails: (sessionId: number) =>
    adminApiInstance.get(`/admin/dashboard/sessions/${sessionId}/details`),

  getSessionPreview: (sessionId: number, limit = 20, resultFilter?: string) => {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    if (resultFilter) params.append('result_filter', resultFilter);
    return adminApiInstance.get(`/admin/dashboard/sessions/${sessionId}/preview?${params.toString()}`);
  },

  // Activity monitoring
  getRecentActivity: (hours = 24, limit = 100) => {
    const params = new URLSearchParams();
    params.append('hours', hours.toString());
    params.append('limit', limit.toString());
    return adminApiInstance.get(`/admin/dashboard/activity?${params.toString()}`);
  },

  // System health
  getSystemHealth: () => adminApiInstance.get('/admin/dashboard/health'),
};

// Export the admin API endpoints with a different name to avoid conflict
export const adminApi = adminApiEndpoints;

// Export annotator API as default for backward compatibility
export default annotatorApi;
