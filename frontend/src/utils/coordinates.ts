/**
 * Coordinate conversion utilities for handling pixel coordinates
 * and display coordinates for canvas rendering
 */

export interface PixelCoordinate {
  x1: number; // actual pixels
  y1: number; // actual pixels
  x2: number; // actual pixels
  y2: number; // actual pixels
}

export interface ImageDimensions {
  width: number;
  height: number;
}

/**
 * Convert pixel coordinates to display coordinates (scaled for canvas)
 */
export function pixelToDisplay(
  pixel: PixelCoordinate,
  imageDimensions: ImageDimensions,
  displayDimensions: ImageDimensions
): PixelCoordinate {
  const scaleX = displayDimensions.width / imageDimensions.width;
  const scaleY = displayDimensions.height / imageDimensions.height;

  return {
    x1: pixel.x1 * scaleX,
    y1: pixel.y1 * scaleY,
    x2: pixel.x2 * scaleX,
    y2: pixel.y2 * scaleY,
  };
}

/**
 * Convert display coordinates back to pixel coordinates
 */
export function displayToPixel(
  display: PixelCoordinate,
  imageDimensions: ImageDimensions,
  displayDimensions: ImageDimensions
): PixelCoordinate {
  const scaleX = imageDimensions.width / displayDimensions.width;
  const scaleY = imageDimensions.height / displayDimensions.height;

  return {
    x1: Math.round(display.x1 * scaleX),
    y1: Math.round(display.y1 * scaleY),
    x2: Math.round(display.x2 * scaleX),
    y2: Math.round(display.y2 * scaleY),
  };
}

/**
 * Convert array format [x1, y1, x2, y2] to coordinate object
 */
export function arrayToCoordinate(arr: [number, number, number, number] | null): PixelCoordinate | null {
  if (!arr) return null;
  return {
    x1: arr[0],
    y1: arr[1],
    x2: arr[2],
    y2: arr[3],
  };
}

/**
 * Convert coordinate object to array format [x1, y1, x2, y2]
 */
export function coordinateToArray(coord: PixelCoordinate | null): [number, number, number, number] | null {
  if (!coord) return null;
  return [Math.round(coord.x1), Math.round(coord.y1), Math.round(coord.x2), Math.round(coord.y2)];
}

/**
 * Ensure coordinates are in correct order (x1 < x2, y1 < y2)
 */
export function normalizeCoordinateOrder(coord: PixelCoordinate): PixelCoordinate {
  return {
    x1: Math.round(Math.min(coord.x1, coord.x2)),
    y1: Math.round(Math.min(coord.y1, coord.y2)),
    x2: Math.round(Math.max(coord.x1, coord.x2)),
    y2: Math.round(Math.max(coord.y1, coord.y2)),
  };
}

/**
 * Check if coordinates are valid (x1 < x2 and y1 < y2)
 */
export function isValidCoordinate(coord: PixelCoordinate | null): boolean {
  if (!coord) return false;
  return coord.x1 < coord.x2 && coord.y1 < coord.y2;
}

/**
 * Clamp coordinates to valid bounds (0 to image dimensions)
 */
export function clampCoordinates(coord: PixelCoordinate, imageDimensions: ImageDimensions): PixelCoordinate {
  return {
    x1: Math.round(Math.max(0, Math.min(imageDimensions.width, coord.x1))),
    y1: Math.round(Math.max(0, Math.min(imageDimensions.height, coord.y1))),
    x2: Math.round(Math.max(0, Math.min(imageDimensions.width, coord.x2))),
    y2: Math.round(Math.max(0, Math.min(imageDimensions.height, coord.y2))),
  };
}
