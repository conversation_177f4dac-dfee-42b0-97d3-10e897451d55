"""
Ruyi Dataverse - Training data management for Ruyi Grounding Model
"""

import importlib.metadata

__version__ = importlib.metadata.version("ruyidv")
__author__ = "Ruyi Team"
__description__ = "Training data management for Ruyi Grounding Model"

from .config import settings
from .database import Base, get_db
from .models import (
    AdminCredential,
    AnnotationResult,
    AnnotationSampleResult,
    AnnotationSession,
    AnnotationStatus,
    Batch,
    Image,
    Sample,
    SampleMode,
    SessionCredential,
)

__all__ = [
    "settings",
    "Base",
    "get_db",
    "Batch",
    "Image",
    "Sample",
    "SampleMode",
    "AnnotationSession",
    "AnnotationSampleResult",
    "AnnotationStatus",
    "AnnotationResult",
    "SessionCredential",
    "AdminCredential",
]
