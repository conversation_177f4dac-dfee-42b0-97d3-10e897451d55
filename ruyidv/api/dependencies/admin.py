"""
Admin authentication dependencies for FastAPI
"""

from typing import Optional

from fastapi import Depends, <PERSON><PERSON>, HTTPException, Request, status
from sqlalchemy.orm import Session

from ...database import get_db
from ...services import AuthService


def get_admin_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """Get authentication service instance for admin operations"""
    return AuthService(db)


async def verify_admin_access(
    request: Request,
    x_admin_path: Optional[str] = Header(None, alias="X-Admin-Path"),
    x_admin_password: Optional[str] = Header(None, alias="X-Admin-Password"),
    auth_service: AuthService = Depends(get_admin_auth_service),
) -> bool:
    """
    Verify admin dashboard access using admin credentials

    Args:
        request: FastAPI request object
        x_admin_path: Admin dashboard path from header
        x_admin_password: Admin password from header
        auth_service: Authentication service instance

    Returns:
        True if access is granted

    Raises:
        HTTPException: If admin authentication fails
    """
    if not x_admin_path or not x_admin_password:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Admin credentials required",
            headers={"WWW-Authenticate": "Admin"},
        )

    # Get client information for audit logging
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("User-Agent", "")

    # Verify admin credentials
    is_authenticated = auth_service.authenticate_admin(
        x_admin_path, x_admin_password, client_ip, user_agent
    )

    if not is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials",
            headers={"WWW-Authenticate": "Admin"},
        )

    return True


async def verify_admin_path_access(
    dashboard_path: str,
    request: Request,
    x_admin_password: Optional[str] = Header(None, alias="X-Admin-Password"),
    auth_service: AuthService = Depends(get_admin_auth_service),
) -> bool:
    """
    Verify admin access for a specific dashboard path

    Args:
        dashboard_path: Dashboard path from URL
        request: FastAPI request object
        x_admin_password: Admin password from header
        auth_service: Authentication service instance

    Returns:
        True if access is granted

    Raises:
        HTTPException: If admin authentication fails
    """
    if not x_admin_password:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Admin password required",
            headers={"WWW-Authenticate": "Admin"},
        )

    # Get client information for audit logging
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("User-Agent", "")

    # Verify admin credentials with the specific path
    is_authenticated = auth_service.authenticate_admin(
        dashboard_path, x_admin_password, client_ip, user_agent
    )

    if not is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials for this dashboard",
            headers={"WWW-Authenticate": "Admin"},
        )

    return True


class AdminAccessDependency:
    """
    Dependency class for admin dashboard access control
    """

    def __init__(self, require_path: bool = False):
        self.require_path = require_path

    async def __call__(
        self,
        request: Request,
        x_admin_path: Optional[str] = Header(None, alias="X-Admin-Path"),
        x_admin_password: Optional[str] = Header(None, alias="X-Admin-Password"),
        auth_service: AuthService = Depends(get_admin_auth_service),
    ) -> bool:
        """
        Verify admin access
        """
        if self.require_path:
            return await verify_admin_access(
                request, x_admin_path, x_admin_password, auth_service
            )
        else:
            # For endpoints that don't require path verification
            if not x_admin_password:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Admin password required",
                    headers={"WWW-Authenticate": "Admin"},
                )

            # Get the admin credential to verify password
            admin_cred = auth_service.get_admin_credential()
            if not admin_cred:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No admin dashboard configured",
                )

            # Verify password only
            if not auth_service.verify_password(
                x_admin_password, admin_cred.hashed_password
            ):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid admin password",
                    headers={"WWW-Authenticate": "Admin"},
                )

            return True


# Create reusable dependency instances
require_admin_access = AdminAccessDependency(require_path=True)
require_admin_password = AdminAccessDependency(require_path=False)
