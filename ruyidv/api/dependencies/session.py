"""
Session authentication dependencies for FastAPI
"""

from typing import Optional

from fastapi import Depends, <PERSON><PERSON>, HTTPException, status
from sqlalchemy.orm import Session

from ...database import get_db
from ...services import AuthService


def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    """Get authentication service instance"""
    return AuthService(db)


async def verify_session_access(
    session_id: int,
    x_session_username: Optional[str] = Header(None),
    x_session_password: Optional[str] = Header(None),
    auth_service: AuthService = Depends(get_auth_service),
) -> bool:
    """
    Verify access to a specific annotation session using session credentials

    Args:
        session_id: Session ID to verify access for
        x_session_username: Session username from header
        x_session_password: Session password from header
        auth_service: Authentication service instance

    Returns:
        True if access is granted

    Raises:
        HTTPException: If session authentication fails
    """
    if not x_session_username or not x_session_password:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session credentials required",
            headers={"WWW-Authenticate": "Session"},
        )

    # Verify session credentials
    is_authenticated = auth_service.authenticate_session(
        session_id, x_session_username, x_session_password
    )

    if not is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid session credentials",
            headers={"WWW-Authenticate": "Session"},
        )

    return True


class SessionAccessDependency:
    """
    Dependency class for session-specific access control
    """

    def __init__(self, session_id_param: str = "session_id"):
        self.session_id_param = session_id_param

    async def __call__(
        self,
        session_id: int,
        x_session_username: Optional[str] = Header(None),
        x_session_password: Optional[str] = Header(None),
        auth_service: AuthService = Depends(get_auth_service),
    ) -> bool:
        """
        Verify session access for the given session ID
        """
        return await verify_session_access(
            session_id, x_session_username, x_session_password, auth_service
        )


# Create reusable dependency instances
require_session_access = SessionAccessDependency()
