"""
Security middleware for Ruyi Dataverse
"""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = (
            "geolocation=(), microphone=(), camera=()"
        )

        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: blob:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        response.headers["Content-Security-Policy"] = csp

        # Remove server information
        if "server" in response.headers:
            del response.headers["server"]

        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log requests for security monitoring"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # Get client information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")

        response = await call_next(request)

        process_time = time.time() - start_time

        # Log security-relevant requests
        if self._is_security_relevant(request):
            print(
                f"SECURITY LOG: {request.method} {request.url.path} - "
                f"IP: {client_ip} - UA: {user_agent[:100]} - "
                f"Status: {response.status_code} - Time: {process_time:.3f}s"
            )

        return response

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()

        if (
            hasattr(request, "client")
            and request.client
            and hasattr(request.client, "host")
        ):
            return request.client.host

        return "unknown"

    def _is_security_relevant(self, request: Request) -> bool:
        """Check if request is security-relevant and should be logged"""
        security_paths = [
            "/auth/",
            "/sessions/",
            "/admin/",
        ]

        return any(request.url.path.startswith(path) for path in security_paths)


class InputValidationMiddleware(BaseHTTPMiddleware):
    """Middleware for basic input validation and sanitization"""

    MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.MAX_REQUEST_SIZE:
            from fastapi import HTTPException, status

            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Request too large",
            )

        # Check for suspicious patterns in headers
        self._validate_headers(request)

        response = await call_next(request)
        return response

    def _validate_headers(self, request: Request):
        """Validate request headers for suspicious patterns"""
        suspicious_patterns = [
            "<script",
            "javascript:",
            "vbscript:",
            "onload=",
            "onerror=",
        ]

        for header_name, header_value in request.headers.items():
            if isinstance(header_value, str):
                header_lower = header_value.lower()
                for pattern in suspicious_patterns:
                    if pattern in header_lower:
                        from fastapi import HTTPException, status

                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Invalid request headers",
                        )
