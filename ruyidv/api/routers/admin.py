"""
Admin Dashboard API endpoints
"""

from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from ...database import get_db
from ...schemas import AdminAuthRequest, AdminCredentialResponse
from ...services import AuthService
from ..dependencies.admin import require_admin_password

router = APIRouter(tags=["admin"])


@router.post("/admin/auth/verify", status_code=status.HTTP_200_OK)
async def verify_admin_credentials(
    request: AdminAuthRequest,
    http_request: Request,
    db: Session = Depends(get_db),
) -> dict:
    """
    Verify admin dashboard credentials

    Args:
        request: Admin authentication request
        http_request: HTTP request object for IP and user agent
        db: Database session

    Returns:
        Success message if credentials are valid

    Raises:
        HTTPException: If authentication fails
    """
    # Get client information for audit logging
    client_ip = http_request.client.host if http_request.client else "unknown"
    user_agent = http_request.headers.get("User-Agent", "")

    # Initialize auth service
    auth_service = AuthService(db)

    is_authenticated = auth_service.authenticate_admin(
        request.dashboard_path, request.password, client_ip, user_agent
    )

    if not is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials",
        )

    return {"message": "Admin credentials verified successfully"}


@router.get("/admin/info", response_model=AdminCredentialResponse)
async def get_admin_info(
    db: Session = Depends(get_db),
) -> AdminCredentialResponse:
    """
    Get admin dashboard information (public endpoint for checking if dashboard exists)

    Args:
        db: Database session

    Returns:
        Admin credential information (without sensitive data)

    Raises:
        HTTPException: If no admin dashboard is configured
    """
    auth_service = AuthService(db)
    credential = auth_service.get_admin_credential()

    if not credential:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No admin dashboard configured",
        )

    return AdminCredentialResponse.model_validate(credential)


@router.get("/admin/dashboard/overview")
async def get_dashboard_overview(
    db: Session = Depends(get_db),
    _: bool = Depends(require_admin_password),
) -> Dict[str, Any]:
    """
    Get comprehensive dashboard overview with real-time statistics

    Args:
        db: Database session

    Returns:
        Dashboard overview data including system stats and recent activity
    """
    from collections import Counter
    from datetime import datetime, timedelta

    from ...models import AnnotationSampleResult, AnnotationSession, AnnotationStatus
    from ...services import BatchService, SampleService

    try:
        # Initialize services
        batch_service = BatchService(db)
        sample_service = SampleService(db)

        # Get basic system statistics using efficient database aggregation
        total_batches = batch_service.count_batches()
        system_stats = sample_service.get_system_statistics()
        total_samples = system_stats["total_samples"]
        unique_images = system_stats["unique_images"]

        # Get annotation sessions statistics
        all_sessions = db.query(AnnotationSession).all()
        total_sessions = len(all_sessions)

        # Session status distribution
        session_status_counts = Counter(
            session.status.value for session in all_sessions
        )

        # Active sessions (in progress or pending)
        active_sessions = [
            session
            for session in all_sessions
            if session.status
            in [AnnotationStatus.IN_PROGRESS, AnnotationStatus.PENDING]
        ]

        # Recent activity (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_results = (
            db.query(AnnotationSampleResult)
            .filter(AnnotationSampleResult.annotated_at >= yesterday)
            .all()
        )

        # Calculate annotation progress
        total_annotations = db.query(AnnotationSampleResult).count()
        annotations_today = len(recent_results)

        # Mode distribution using efficient database aggregation
        mode_counts = Counter(system_stats["mode_distribution"])

        # Top performing sessions (by completion rate)
        session_performance = []
        for session in all_sessions:
            if session.total_samples > 0:
                completion_rate = (
                    session.completed_samples / session.total_samples
                ) * 100
                session_performance.append({
                    "session_id": session.id,
                    "session_name": session.name,
                    "annotator_name": session.annotator_name,
                    "completion_rate": round(completion_rate, 1),
                    "completed_samples": session.completed_samples,
                    "total_samples": session.total_samples,
                    "status": session.status.value,
                    "last_activity": session.last_activity_at.isoformat()
                    if session.last_activity_at
                    else None,
                })

        # Sort by completion rate
        session_performance.sort(key=lambda x: x["completion_rate"], reverse=True)

        return {
            "system_overview": {
                "total_batches": total_batches,
                "total_samples": total_samples,
                "unique_images": unique_images,
                "total_sessions": total_sessions,
                "active_sessions": len(active_sessions),
                "total_annotations": total_annotations,
                "annotations_today": annotations_today,
            },
            "session_status_distribution": [
                {
                    "status": status,
                    "count": count,
                    "percentage": round((count / total_sessions) * 100, 1)
                    if total_sessions > 0
                    else 0,
                }
                for status, count in session_status_counts.most_common()
            ],
            "mode_distribution": [
                {
                    "mode": mode,
                    "mode_display": mode.replace("_", " ").title(),
                    "count": count,
                    "percentage": round((count / total_samples) * 100, 1)
                    if total_samples > 0
                    else 0,
                }
                for mode, count in mode_counts.most_common()
            ],
            "top_sessions": session_performance[:10],  # Top 10 sessions
            "recent_activity": {
                "annotations_last_24h": annotations_today,
                "active_annotators": len(
                    set(session.annotator_name for session in active_sessions)
                ),
                "avg_annotations_per_hour": round(annotations_today / 24, 1)
                if annotations_today > 0
                else 0,
            },
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get dashboard overview: {str(e)}"
        )


@router.get("/admin/dashboard/sessions")
async def get_all_sessions(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    _: bool = Depends(require_admin_password),
) -> Dict[str, Any]:
    """
    Get all annotation sessions with detailed progress information

    Args:
        status: Filter by session status (optional)
        limit: Maximum number of sessions to return
        offset: Number of sessions to skip
        db: Database session

    Returns:
        List of sessions with progress details
    """
    from ...models import AnnotationSession, AnnotationStatus
    from ...services import AnnotationService

    try:
        annotation_service = AnnotationService(db)

        # Build query
        query = db.query(AnnotationSession)

        # Apply status filter if provided
        if status:
            try:
                status_enum = AnnotationStatus(status)
                query = query.filter(AnnotationSession.status == status_enum)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        # Apply pagination
        total_count = query.count()
        sessions = query.offset(offset).limit(limit).all()

        # Build detailed session information
        session_details = []
        for session in sessions:
            progress_info = annotation_service.get_session_progress(session.id)

            session_details.append({
                "id": session.id,
                "name": session.name,
                "description": session.description,
                "annotator_name": session.annotator_name,
                "annotator_email": session.annotator_email,
                "status": session.status.value,
                "created_at": session.created_at.isoformat(),
                "started_at": session.started_at.isoformat()
                if session.started_at
                else None,
                "completed_at": session.completed_at.isoformat()
                if session.completed_at
                else None,
                "last_activity_at": session.last_activity_at.isoformat()
                if session.last_activity_at
                else None,
                "progress": progress_info,
                "query_filters": session.query_filters,
            })

        return {
            "sessions": session_details,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count,
            },
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get sessions: {str(e)}")


@router.get("/admin/dashboard/sessions/{session_id}/details")
async def get_session_details(
    session_id: int,
    db: Session = Depends(get_db),
    _: bool = Depends(require_admin_password),
) -> Dict[str, Any]:
    """
    Get detailed information about a specific annotation session

    Args:
        session_id: ID of the annotation session
        db: Database session

    Returns:
        Detailed session information including progress and recent annotations
    """
    from datetime import datetime

    from ...models import AnnotationSampleResult
    from ...services import AnnotationService

    try:
        annotation_service = AnnotationService(db)

        # Get session
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            raise HTTPException(
                status_code=404, detail=f"Session {session_id} not found"
            )

        # Get progress information
        progress_info = annotation_service.get_session_progress(session_id)

        # Get recent annotations (last 10)
        recent_annotations = (
            db.query(AnnotationSampleResult)
            .filter(AnnotationSampleResult.session_id == session_id)
            .order_by(AnnotationSampleResult.annotated_at.desc())
            .limit(10)
            .all()
        )

        # Get annotation statistics by result type
        from collections import Counter

        all_results = (
            db.query(AnnotationSampleResult)
            .filter(AnnotationSampleResult.session_id == session_id)
            .all()
        )

        result_counts = Counter(result.result.value for result in all_results)

        # Calculate annotation rate (annotations per hour)
        if session.started_at:
            hours_active = (
                datetime.utcnow() - session.started_at
            ).total_seconds() / 3600
            annotation_rate = len(all_results) / hours_active if hours_active > 0 else 0
        else:
            annotation_rate = 0

        # Format recent annotations
        recent_annotations_data = []
        for result in recent_annotations:
            recent_annotations_data.append({
                "id": result.id,
                "sample_id": result.sample_id,
                "result": result.result.value,
                "annotated_at": result.annotated_at.isoformat(),
                "annotation_duration": result.annotation_duration,
                "notes": result.notes,
                "has_corrections": result.corrected_metadata is not None,
            })

        return {
            "session": {
                "id": session.id,
                "name": session.name,
                "description": session.description,
                "annotator_name": session.annotator_name,
                "annotator_email": session.annotator_email,
                "status": session.status.value,
                "created_at": session.created_at.isoformat(),
                "started_at": session.started_at.isoformat()
                if session.started_at
                else None,
                "completed_at": session.completed_at.isoformat()
                if session.completed_at
                else None,
                "last_activity_at": session.last_activity_at.isoformat()
                if session.last_activity_at
                else None,
                "query_filters": session.query_filters,
            },
            "progress": progress_info,
            "statistics": {
                "total_annotations": len(all_results),
                "result_distribution": [
                    {"result": result, "count": count}
                    for result, count in result_counts.most_common()
                ],
                "annotation_rate_per_hour": round(annotation_rate, 2),
                "avg_duration_seconds": round(
                    sum(
                        r.annotation_duration
                        for r in all_results
                        if r.annotation_duration
                    )
                    / len([r for r in all_results if r.annotation_duration])
                    if any(r.annotation_duration for r in all_results)
                    else 0,
                    1,
                ),
            },
            "recent_annotations": recent_annotations_data,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get session details: {str(e)}"
        )


@router.get("/admin/dashboard/sessions/{session_id}/preview")
async def get_session_annotation_preview(
    session_id: int,
    limit: int = 20,
    result_filter: Optional[str] = None,
    db: Session = Depends(get_db),
    _: bool = Depends(require_admin_password),
) -> Dict[str, Any]:
    """
    Get annotation preview for a specific session

    Args:
        session_id: ID of the annotation session
        limit: Maximum number of annotations to return
        result_filter: Filter by annotation result (correct, incorrect, skipped)
        db: Database session

    Returns:
        List of annotations with sample data for preview
    """
    from ...models import AnnotationResult, AnnotationSampleResult, Image, Sample
    from ...services import AnnotationService

    try:
        annotation_service = AnnotationService(db)

        # Verify session exists
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            raise HTTPException(
                status_code=404, detail=f"Session {session_id} not found"
            )

        # Build query for annotations with sample and image data
        query = (
            db.query(AnnotationSampleResult)
            .join(Sample, AnnotationSampleResult.sample_id == Sample.id)
            .join(Image, Sample.image_id == Image.id)
            .filter(AnnotationSampleResult.session_id == session_id)
        )

        # Apply result filter if provided
        if result_filter:
            try:
                result_enum = AnnotationResult(result_filter)
                query = query.filter(AnnotationSampleResult.result == result_enum)
            except ValueError:
                raise HTTPException(
                    status_code=400, detail=f"Invalid result filter: {result_filter}"
                )

        # Get annotations with related data
        annotations = (
            query.order_by(AnnotationSampleResult.annotated_at.desc())
            .limit(limit)
            .all()
        )

        # Format annotation preview data
        preview_data = []
        for annotation in annotations:
            sample = annotation.sample
            image = sample.image

            preview_data.append({
                "annotation": {
                    "id": annotation.id,
                    "result": annotation.result.value,
                    "annotated_at": annotation.annotated_at.isoformat(),
                    "annotation_duration": annotation.annotation_duration,
                    "notes": annotation.notes,
                    "has_corrections": annotation.corrected_metadata is not None,
                },
                "sample": {
                    "id": sample.id,
                    "mode": sample.mode.value,
                    "labels": sample.labels,
                    "original_metadata": annotation.original_metadata,
                    "corrected_metadata": annotation.corrected_metadata,
                },
                "image": {
                    "id": image.id,
                    "width": image.width,
                    "height": image.height,
                    "file_size": image.file_size,
                },
            })

        return {
            "session_id": session_id,
            "session_name": session.name,
            "annotations": preview_data,
            "total_shown": len(preview_data),
            "filter_applied": result_filter,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get annotation preview: {str(e)}"
        )


@router.get("/admin/dashboard/activity")
async def get_recent_activity(
    hours: int = 24,
    limit: int = 100,
    db: Session = Depends(get_db),
    _: bool = Depends(require_admin_password),
) -> Dict[str, Any]:
    """
    Get recent annotation activity across all sessions

    Args:
        hours: Number of hours to look back for activity (default: 24)
        limit: Maximum number of activities to return
        db: Database session

    Returns:
        Recent annotation activity feed
    """
    from datetime import datetime, timedelta

    from ...models import (
        AnnotationSampleResult,
        AnnotationSession,
        AuditEventType,
        AuditLog,
    )

    try:
        # Calculate time threshold
        time_threshold = datetime.utcnow() - timedelta(hours=hours)

        # Get recent annotations
        recent_annotations = (
            db.query(AnnotationSampleResult)
            .join(
                AnnotationSession,
                AnnotationSampleResult.session_id == AnnotationSession.id,
            )
            .filter(AnnotationSampleResult.annotated_at >= time_threshold)
            .order_by(AnnotationSampleResult.annotated_at.desc())
            .limit(limit)
            .all()
        )

        # Get recent audit events (login/logout activities)
        recent_audit_events = (
            db.query(AuditLog)
            .filter(
                AuditLog.timestamp >= time_threshold,
                AuditLog.event_type.in_([
                    AuditEventType.LOGIN_SUCCESS,
                    AuditEventType.LOGIN_FAILURE,
                    AuditEventType.SESSION_ACCESS,
                ]),
            )
            .order_by(AuditLog.timestamp.desc())
            .limit(50)
            .all()
        )

        # Combine and format activities
        activities = []

        # Add annotation activities
        for annotation in recent_annotations:
            activities.append({
                "type": "annotation",
                "timestamp": annotation.annotated_at.isoformat(),
                "session_id": annotation.session_id,
                "session_name": annotation.session.name,
                "annotator_name": annotation.session.annotator_name,
                "result": annotation.result.value,
                "sample_id": annotation.sample_id,
                "duration": annotation.annotation_duration,
                "has_corrections": annotation.corrected_metadata is not None,
            })

        # Add audit activities
        for audit in recent_audit_events:
            activity_type = (
                "login"
                if audit.event_type == AuditEventType.LOGIN_SUCCESS
                else "access"
            )
            if audit.event_type == AuditEventType.LOGIN_FAILURE:
                activity_type = "login_failed"

            activities.append({
                "type": activity_type,
                "timestamp": audit.timestamp.isoformat(),
                "session_id": audit.session_id,
                "username": audit.username,
                "ip_address": audit.ip_address,
                "success": audit.success,
                "error_message": audit.error_message,
            })

        # Sort all activities by timestamp
        activities.sort(key=lambda x: x["timestamp"], reverse=True)

        # Calculate activity statistics
        annotation_count = len(recent_annotations)
        unique_sessions = len(set(a.session_id for a in recent_annotations))
        unique_annotators = len(
            set(a.session.annotator_name for a in recent_annotations)
        )

        return {
            "time_range": {
                "hours": hours,
                "from": time_threshold.isoformat(),
                "to": datetime.utcnow().isoformat(),
            },
            "statistics": {
                "total_annotations": annotation_count,
                "active_sessions": unique_sessions,
                "active_annotators": unique_annotators,
                "avg_annotations_per_hour": round(annotation_count / hours, 1)
                if hours > 0
                else 0,
            },
            "activities": activities[:limit],
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get recent activity: {str(e)}"
        )


@router.get("/admin/dashboard/health")
async def get_system_health(
    db: Session = Depends(get_db),
    _: bool = Depends(require_admin_password),
) -> Dict[str, Any]:
    """
    Get system health and performance metrics

    Args:
        db: Database session

    Returns:
        System health information
    """
    import os
    from datetime import datetime, timedelta

    import psutil

    from ...models import AnnotationSampleResult, AnnotationSession, AnnotationStatus

    try:
        # Database health checks
        total_sessions = db.query(AnnotationSession).count()
        active_sessions = (
            db.query(AnnotationSession)
            .filter(
                AnnotationSession.status.in_([
                    AnnotationStatus.IN_PROGRESS,
                    AnnotationStatus.PENDING,
                ])
            )
            .count()
        )

        # Recent activity check (last hour)
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        recent_annotations = (
            db.query(AnnotationSampleResult)
            .filter(AnnotationSampleResult.annotated_at >= one_hour_ago)
            .count()
        )

        # System resource usage
        memory_usage = psutil.virtual_memory()
        disk_usage = psutil.disk_usage("/")
        cpu_percent = psutil.cpu_percent(interval=1)

        # Database file size (for SQLite)
        from ...config import settings

        db_size = 0
        if "sqlite" in settings.DATABASE_URL:
            db_path = settings.DATABASE_URL.replace("sqlite:///", "").replace(
                "sqlite://", ""
            )
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path)

        # Storage directory size
        storage_size = 0
        try:
            if settings.STORAGE_ROOT.exists():
                for dirpath, dirnames, filenames in os.walk(settings.STORAGE_ROOT):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            storage_size += os.path.getsize(filepath)
                        except (OSError, FileNotFoundError):
                            # Skip files that can't be accessed
                            continue
        except Exception:
            # If storage directory can't be accessed, set size to 0
            storage_size = 0

        # Health status determination
        health_status = "healthy"
        issues = []

        if memory_usage.percent > 90:
            health_status = "warning"
            issues.append("High memory usage")

        if disk_usage.percent > 90:
            health_status = "critical"
            issues.append("Low disk space")

        if cpu_percent > 90:
            health_status = "warning"
            issues.append("High CPU usage")

        if recent_annotations == 0 and active_sessions > 0:
            health_status = "warning"
            issues.append("No recent annotation activity despite active sessions")

        return {
            "status": health_status,
            "issues": issues,
            "timestamp": datetime.utcnow().isoformat(),
            "database": {
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "recent_annotations_1h": recent_annotations,
                "database_size_mb": round(db_size / (1024 * 1024), 2),
            },
            "system": {
                "memory_usage_percent": memory_usage.percent,
                "memory_available_gb": round(memory_usage.available / (1024**3), 2),
                "disk_usage_percent": disk_usage.percent,
                "disk_free_gb": round(disk_usage.free / (1024**3), 2),
                "cpu_usage_percent": cpu_percent,
            },
            "storage": {
                "storage_size_mb": round(storage_size / (1024 * 1024), 2),
                "storage_path": str(settings.STORAGE_ROOT),
            },
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get system health: {str(e)}"
        )
