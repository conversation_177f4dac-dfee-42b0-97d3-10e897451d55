"""
Annotation API endpoints
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...database import get_db
from ...schemas import (
    AnnotationSampleResultCreate,
    AnnotationSampleResultResponse,
    AnnotationSessionResponse,
    AnnotationSessionUpdate,
    PaginatedResponse,
    SampleWithRelations,
)
from ...services import AnnotationService
from ..dependencies import require_session_access

router = APIRouter(tags=["annotation"])


# Session creation is now admin-only via CLI
# @router.post("/sessions", response_model=AnnotationSessionResponse)
# async def create_annotation_session(
#     session_data: AnnotationSessionCreate,
#     db: Session = Depends(get_db),
#     current_admin: User = Depends(get_current_admin_user),
# ) -> AnnotationSessionResponse:
#     """
#     创建新的标注会话 - DEPRECATED: Use CLI admin commands instead
#     """
#     raise HTTPException(
#         status_code=410,
#         detail="Session creation via API is deprecated. Use CLI admin commands: 'ruyidv admin create-session'"
#     )


@router.get("/sessions", response_model=List[AnnotationSessionResponse])
async def list_annotation_sessions(
    annotator_name: Optional[str] = Query(None, description="按标注者姓名过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> List[AnnotationSessionResponse]:
    """
    获取标注会话列表

    Args:
        annotator_name: 可选的标注者姓名过滤
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        标注会话列表
    """
    try:
        annotation_service = AnnotationService(db)

        if annotator_name:
            sessions = annotation_service.get_annotation_sessions_by_annotator(
                annotator_name, limit=limit, offset=offset
            )
        else:
            sessions = annotation_service.get_all_annotation_sessions(
                limit=limit, offset=offset
            )

        return [
            AnnotationSessionResponse.model_validate(session) for session in sessions
        ]
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to list annotation sessions: {str(e)}"
        )


@router.get("/sessions/{session_id}", response_model=AnnotationSessionResponse)
async def get_annotation_session(
    session_id: int,
    db: Session = Depends(get_db),
    _: bool = Depends(require_session_access),
) -> AnnotationSessionResponse:
    """
    获取指定标注会话的详细信息

    Args:
        session_id: 会话ID

    Returns:
        标注会话详细信息
    """
    try:
        annotation_service = AnnotationService(db)
        session = annotation_service.get_annotation_session_by_id(session_id)

        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        return AnnotationSessionResponse.model_validate(session)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get annotation session: {str(e)}"
        )


@router.put("/sessions/{session_id}", response_model=AnnotationSessionResponse)
async def update_annotation_session(
    session_id: int, update_data: AnnotationSessionUpdate, db: Session = Depends(get_db)
) -> AnnotationSessionResponse:
    """
    更新标注会话信息

    Args:
        session_id: 会话ID
        update_data: 更新数据

    Returns:
        更新后的标注会话信息
    """
    try:
        annotation_service = AnnotationService(db)
        session = annotation_service.update_annotation_session(session_id, update_data)

        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        return AnnotationSessionResponse.model_validate(session)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to update annotation session: {str(e)}"
        )


@router.post("/sessions/{session_id}/start", response_model=AnnotationSessionResponse)
async def start_annotation_session(
    session_id: int, db: Session = Depends(get_db)
) -> AnnotationSessionResponse:
    """
    开始标注会话

    Args:
        session_id: 会话ID

    Returns:
        更新后的标注会话信息
    """
    try:
        annotation_service = AnnotationService(db)
        session = annotation_service.start_annotation_session(session_id)

        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        return AnnotationSessionResponse.model_validate(session)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start annotation session: {str(e)}"
        )


@router.post(
    "/sessions/{session_id}/complete", response_model=AnnotationSessionResponse
)
async def complete_annotation_session(
    session_id: int, db: Session = Depends(get_db)
) -> AnnotationSessionResponse:
    """
    完成标注会话

    Args:
        session_id: 会话ID

    Returns:
        更新后的标注会话信息
    """
    try:
        annotation_service = AnnotationService(db)
        session = annotation_service.complete_annotation_session(session_id)

        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        return AnnotationSessionResponse.model_validate(session)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to complete annotation session: {str(e)}"
        )


@router.get("/sessions/{session_id}/progress")
async def get_session_progress(session_id: int, db: Session = Depends(get_db)):
    """
    获取标注会话进度信息

    Args:
        session_id: 会话ID

    Returns:
        会话进度统计信息
    """
    try:
        annotation_service = AnnotationService(db)
        progress = annotation_service.get_session_progress(session_id)

        if not progress:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        return progress
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get session progress: {str(e)}"
        )


@router.get("/sessions/{session_id}/samples", response_model=PaginatedResponse)
async def get_session_samples(
    session_id: int,
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> PaginatedResponse:
    """
    获取标注会话的样本列表

    Args:
        session_id: 会话ID
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        分页的样本列表
    """
    try:
        annotation_service = AnnotationService(db)

        # 验证会话是否存在
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        # 获取样本列表
        samples = annotation_service.get_session_samples(
            session_id, limit=limit, offset=offset
        )

        # 转换为响应格式
        sample_responses = [
            SampleWithRelations.model_validate(sample) for sample in samples
        ]

        return PaginatedResponse(
            items=sample_responses,
            total=session.total_samples,
            limit=limit,
            offset=offset,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get session samples: {str(e)}"
        )


@router.get(
    "/sessions/{session_id}/next-sample", response_model=Optional[SampleWithRelations]
)
async def get_next_unannotated_sample(
    session_id: int, db: Session = Depends(get_db)
) -> Optional[SampleWithRelations]:
    """
    获取下一个待标注的样本

    Args:
        session_id: 会话ID

    Returns:
        下一个待标注的样本，如果没有则返回null
    """
    try:
        annotation_service = AnnotationService(db)

        # 验证会话是否存在
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        # 获取下一个样本
        sample = annotation_service.get_next_unannotated_sample(session_id)

        if sample:
            return SampleWithRelations.model_validate(sample)
        else:
            return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get next sample: {str(e)}"
        )


@router.post(
    "/sessions/{session_id}/results", response_model=AnnotationSampleResultResponse
)
async def create_annotation_result(
    session_id: int,
    result_data: AnnotationSampleResultCreate,
    allow_update: bool = Query(False, description="是否允许更新已存在的标注结果"),
    db: Session = Depends(get_db),
) -> AnnotationSampleResultResponse:
    """
    提交标注结果

    Args:
        session_id: 会话ID
        result_data: 标注结果数据
        allow_update: 是否允许更新已存在的标注结果

    Returns:
        创建的标注结果信息
    """
    try:
        # 验证session_id匹配
        if result_data.session_id != session_id:
            raise HTTPException(
                status_code=400,
                detail="Session ID in URL does not match session ID in request body",
            )

        annotation_service = AnnotationService(db)
        result = annotation_service.create_annotation_result(
            result_data, allow_update=allow_update
        )

        return AnnotationSampleResultResponse.model_validate(result)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create annotation result: {str(e)}"
        )


@router.get("/sessions/{session_id}/results", response_model=PaginatedResponse)
async def get_annotation_results(
    session_id: int,
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> PaginatedResponse:
    """
    获取标注会话的结果列表

    Args:
        session_id: 会话ID
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        分页的标注结果列表
    """
    try:
        annotation_service = AnnotationService(db)

        # 验证会话是否存在
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        # 获取标注结果列表
        results = annotation_service.get_annotation_results_by_session(
            session_id, limit=limit, offset=offset
        )

        # 转换为响应格式
        result_responses = [
            AnnotationSampleResultResponse.model_validate(result) for result in results
        ]

        return PaginatedResponse(
            items=result_responses,
            total=session.completed_samples,
            limit=limit,
            offset=offset,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get annotation results: {str(e)}"
        )


@router.get(
    "/sessions/{session_id}/results/{sample_id}",
    response_model=Optional[AnnotationSampleResultResponse],
)
async def get_annotation_result_by_sample(
    session_id: int,
    sample_id: int,
    db: Session = Depends(get_db),
) -> Optional[AnnotationSampleResultResponse]:
    """
    获取指定样本的标注结果

    Args:
        session_id: 会话ID
        sample_id: 样本ID

    Returns:
        标注结果信息，如果未标注则返回null
    """
    try:
        annotation_service = AnnotationService(db)

        # 验证会话是否存在
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            raise HTTPException(
                status_code=404,
                detail=f"Annotation session with ID {session_id} not found",
            )

        # 获取标注结果
        result = annotation_service.get_annotation_result_by_sample(
            session_id, sample_id
        )

        if result:
            return AnnotationSampleResultResponse.model_validate(result)
        else:
            return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get annotation result: {str(e)}"
        )
