"""
Authentication API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from ...database import get_db
from ...schemas import (
    SessionAuthRequest,
)
from ...services import AuthService

router = APIRouter(tags=["authentication"])


@router.post("/auth/verify-session", status_code=status.HTTP_200_OK)
async def verify_session_credentials(
    request: SessionAuthRequest,
    http_request: Request,
    db: Session = Depends(get_db),
) -> dict:
    """
    Verify session credentials for annotation access

    Args:
        request: Session authentication request
        http_request: HTTP request object for IP and user agent
        db: Database session

    Returns:
        Success message if credentials are valid

    Raises:
        HTTPException: If session authentication fails or rate limited
    """
    import time

    from ...services.rate_limiter import get_client_ip, rate_limiter

    # Get client information
    client_ip = get_client_ip(http_request)
    user_agent = http_request.headers.get("User-Agent")

    # Check rate limiting
    if rate_limiter.is_rate_limited(client_ip):
        remaining_time = rate_limiter.get_reset_time(client_ip)
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many login attempts. Try again later.",
            headers={
                "Retry-After": str(
                    int(remaining_time - time.time()) if remaining_time else 900
                )
            },
        )

    # Initialize auth service with db session
    auth_service = AuthService(db)

    is_authenticated = auth_service.authenticate_session(
        request.session_id, request.username, request.password, client_ip, user_agent
    )

    if not is_authenticated:
        # Record failed attempt for rate limiting
        rate_limiter.record_attempt(client_ip)

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid session credentials",
        )

    # Reset rate limiting on successful authentication
    rate_limiter.reset_attempts(client_ip)

    return {"message": "Session credentials verified successfully"}
