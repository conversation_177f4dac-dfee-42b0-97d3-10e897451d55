"""
Batch API endpoints
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...database import get_db
from ...schemas import BatchResponse, PaginatedResponse, SampleWithRelations
from ...services import BatchService, SampleService

router = APIRouter(tags=["batches"])


@router.get("/batches", response_model=List[BatchResponse])
async def list_batches(
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> List[BatchResponse]:
    """
    获取批次列表

    Args:
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        批次列表
    """
    try:
        batch_service = BatchService(db)
        batches = batch_service.get_all_batches(limit=limit, offset=offset)

        return [BatchResponse.model_validate(batch) for batch in batches]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list batches: {str(e)}")


@router.get("/batches/{batch_id}", response_model=BatchResponse)
async def get_batch_details(
    batch_id: int, db: Session = Depends(get_db)
) -> BatchResponse:
    """
    获取指定批次的详细信息

    Args:
        batch_id: 批次ID

    Returns:
        批次详细信息
    """
    try:
        batch_service = BatchService(db)
        batch = batch_service.get_batch_by_id(batch_id)

        if not batch:
            raise HTTPException(
                status_code=404, detail=f"Batch with ID {batch_id} not found"
            )

        return BatchResponse.model_validate(batch)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get batch details: {str(e)}"
        )


@router.get("/batches/{batch_id}/statistics")
async def get_batch_statistics(batch_id: int, db: Session = Depends(get_db)):
    """
    获取批次统计信息

    Args:
        batch_id: 批次ID

    Returns:
        批次统计信息
    """
    try:
        batch_service = BatchService(db)

        # 验证批次是否存在
        batch = batch_service.get_batch_by_id(batch_id)
        if not batch:
            raise HTTPException(
                status_code=404, detail=f"Batch with ID {batch_id} not found"
            )

        # 获取统计信息
        stats = batch_service.get_batch_statistics(batch_id)

        return stats
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get batch statistics: {str(e)}"
        )


@router.get("/batches/{batch_id}/samples", response_model=PaginatedResponse)
async def get_batch_samples(
    batch_id: int,
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> PaginatedResponse:
    """
    获取批次的样本列表

    Args:
        batch_id: 批次ID
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        分页的样本列表
    """
    try:
        batch_service = BatchService(db)
        sample_service = SampleService(db)

        # 验证批次是否存在
        batch = batch_service.get_batch_by_id(batch_id)
        if not batch:
            raise HTTPException(
                status_code=404, detail=f"Batch with ID {batch_id} not found"
            )

        # 获取样本总数
        total_count = sample_service.count_samples_with_filters(batch_ids=[batch_id])

        # 获取样本列表
        samples = sample_service.get_samples_with_filters(
            batch_ids=[batch_id], limit=limit, offset=offset
        )

        # 转换为响应格式
        sample_responses = [
            SampleWithRelations.model_validate(sample) for sample in samples
        ]

        return PaginatedResponse(
            items=sample_responses, total=total_count, limit=limit, offset=offset
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get batch samples: {str(e)}"
        )


@router.get("/batches/{batch_id}/summary")
async def get_batch_summary(
    batch_id: int,
    limit: int = Query(10, ge=1, le=100, description="样本摘要数量限制"),
    db: Session = Depends(get_db),
):
    """
    获取批次摘要信息

    Args:
        batch_id: 批次ID
        limit: 样本摘要数量限制

    Returns:
        批次摘要信息
    """
    try:
        batch_service = BatchService(db)

        # 验证批次是否存在
        batch = batch_service.get_batch_by_id(batch_id)
        if not batch:
            raise HTTPException(
                status_code=404, detail=f"Batch with ID {batch_id} not found"
            )

        # 获取摘要信息
        summary = batch_service.get_batch_samples_summary(batch_id, limit=limit)

        return summary
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get batch summary: {str(e)}"
        )
