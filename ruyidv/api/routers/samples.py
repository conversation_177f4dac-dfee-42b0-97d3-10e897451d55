"""
Sample API endpoints
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from ...database import get_db
from ...models import SampleMode
from ...schemas import PaginatedResponse, SampleWithRelations
from ...services import ImageService, SampleService

router = APIRouter(tags=["samples"])


@router.get("/samples", response_model=PaginatedResponse)
async def query_samples(
    batch_ids: Optional[str] = Query(None, description="批次ID列表，逗号分隔"),
    modes: Optional[str] = Query(None, description="模式列表，逗号分隔"),
    labels: Optional[str] = Query(None, description="标签列表，逗号分隔"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> PaginatedResponse:
    """
    查询样本列表

    Args:
        batch_ids: 批次ID列表，逗号分隔，例如 "1,2,3"
        modes: 模式列表，逗号分隔，例如 "grounding,describe"
        labels: 标签列表，逗号分隔，例如 "vehicle,outdoor"
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        分页的样本列表
    """
    try:
        sample_service = SampleService(db)

        # 解析查询参数
        parsed_batch_ids = None
        if batch_ids:
            try:
                parsed_batch_ids = [
                    int(bid.strip()) for bid in batch_ids.split(",") if bid.strip()
                ]
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid batch_ids format. Expected comma-separated integers.",
                )

        parsed_modes = None
        if modes:
            try:
                parsed_modes = [
                    SampleMode(mode.strip())
                    for mode in modes.split(",")
                    if mode.strip()
                ]
            except ValueError:
                valid_modes = [mode.value for mode in SampleMode]
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid mode. Valid modes are: {', '.join(valid_modes)}",
                )

        parsed_labels = None
        if labels:
            parsed_labels = [
                label.strip() for label in labels.split(",") if label.strip()
            ]

        # 获取样本总数
        total_count = sample_service.count_samples_with_filters(
            batch_ids=parsed_batch_ids, modes=parsed_modes, labels=parsed_labels
        )

        # 获取样本列表
        samples = sample_service.get_samples_with_filters(
            batch_ids=parsed_batch_ids,
            modes=parsed_modes,
            labels=parsed_labels,
            limit=limit,
            offset=offset,
        )

        # 转换为响应格式
        sample_responses = [
            SampleWithRelations.model_validate(sample) for sample in samples
        ]

        return PaginatedResponse(
            items=sample_responses, total=total_count, limit=limit, offset=offset
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to query samples: {str(e)}"
        )


@router.get("/samples/{sample_id}", response_model=SampleWithRelations)
async def get_sample_details(
    sample_id: int, db: Session = Depends(get_db)
) -> SampleWithRelations:
    """
    获取指定样本的详细信息

    Args:
        sample_id: 样本ID

    Returns:
        样本详细信息
    """
    try:
        sample_service = SampleService(db)
        sample = sample_service.get_sample_by_id(sample_id)

        if not sample:
            raise HTTPException(
                status_code=404, detail=f"Sample with ID {sample_id} not found"
            )

        return SampleWithRelations.model_validate(sample)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get sample details: {str(e)}"
        )


@router.get("/samples/{sample_id}/image")
async def get_sample_image(sample_id: int, db: Session = Depends(get_db)):
    """
    获取样本对应的图片文件

    Args:
        sample_id: 样本ID

    Returns:
        图片文件响应
    """
    try:
        sample_service = SampleService(db)
        image_service = ImageService(db)

        # 获取样本
        sample = sample_service.get_sample_by_id(sample_id)
        if not sample:
            raise HTTPException(
                status_code=404, detail=f"Sample with ID {sample_id} not found"
            )

        # 获取图片绝对路径
        image_path = image_service.get_absolute_path(sample.image)

        if not image_path.exists():
            raise HTTPException(
                status_code=404, detail=f"Image file not found: {image_path}"
            )

        # 返回图片文件
        return FileResponse(
            path=str(image_path),
            media_type="image/jpeg",  # 可以根据文件扩展名动态设置
            filename=f"sample_{sample_id}_image{image_path.suffix}",
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get sample image: {str(e)}"
        )


@router.get("/samples/by-image/{image_id}", response_model=List[SampleWithRelations])
async def get_samples_by_image(
    image_id: int, db: Session = Depends(get_db)
) -> List[SampleWithRelations]:
    """
    获取指定图片的所有样本

    Args:
        image_id: 图片ID

    Returns:
        样本列表
    """
    try:
        sample_service = SampleService(db)
        samples = sample_service.get_samples_by_image(image_id)

        return [SampleWithRelations.model_validate(sample) for sample in samples]
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get samples by image: {str(e)}"
        )


@router.get("/samples/by-batch/{batch_id}", response_model=PaginatedResponse)
async def get_samples_by_batch(
    batch_id: int,
    limit: int = Query(100, ge=1, le=1000, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db),
) -> PaginatedResponse:
    """
    获取指定批次的样本列表

    Args:
        batch_id: 批次ID
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        分页的样本列表
    """
    try:
        sample_service = SampleService(db)

        # 获取样本总数
        total_count = sample_service.count_samples_with_filters(batch_ids=[batch_id])

        # 获取样本列表
        samples = sample_service.get_samples_with_filters(
            batch_ids=[batch_id], limit=limit, offset=offset
        )

        # 转换为响应格式
        sample_responses = [
            SampleWithRelations.model_validate(sample) for sample in samples
        ]

        return PaginatedResponse(
            items=sample_responses, total=total_count, limit=limit, offset=offset
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get samples by batch: {str(e)}"
        )
