"""
Statistics API endpoints
"""

from collections import Counter
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ...database import get_db
from ...services import BatchService, SampleService

router = APIRouter(tags=["statistics"])


@router.get("/stats/overview")
async def get_overview_stats(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取系统概览统计信息

    Returns:
        包含系统关键指标的字典
    """
    try:
        batch_service = BatchService(db)
        sample_service = SampleService(db)

        # 基础统计 - 使用高效的数据库聚合方法
        total_batches = batch_service.count_batches()

        # 使用高效的统计方法，避免获取所有样本记录
        system_stats = sample_service.get_system_statistics()
        total_samples = system_stats["total_samples"]
        unique_images = system_stats["unique_images"]
        mode_counts = Counter(system_stats["mode_distribution"])

        # 高效获取所有标签
        all_labels = sample_service.get_all_labels_efficiently()
        unique_labels = len(set(all_labels))

        # 计算比率
        samples_per_image = total_samples / unique_images if unique_images > 0 else 0
        samples_per_batch = total_samples / total_batches if total_batches > 0 else 0

        # 获取最近的批次
        recent_batches = batch_service.get_all_batches(limit=5)
        recent_batches_data = []
        for batch in recent_batches:
            stats = batch_service.get_batch_statistics(batch.id)
            sample_count = stats["total_samples"] if stats else 0
            recent_batches_data.append({
                "id": batch.id,
                "name": batch.name,
                "description": batch.description,
                "sample_count": sample_count,
                "created_at": batch.created_at.isoformat(),
            })

        # 标签使用统计 (前10个)
        label_counts = Counter(all_labels) if all_labels else Counter()
        top_labels = (
            [
                {
                    "label": label,
                    "count": count,
                    "percentage": count / len(all_labels) * 100,
                }
                for label, count in label_counts.most_common(10)
            ]
            if all_labels
            else []
        )

        # 模式分布详情
        mode_descriptions = {
            "grounding": "Description → Coordinate mapping",
            "describe": "Coordinate → Description mapping",
            "enumerate_text": "Element text enumeration",
            "enumerate_coord": "Element coordinate enumeration",
            "checklist": "Boolean checklist validation",
            "ensure": "Target state achievement",
        }

        mode_distribution = [
            {
                "mode": mode,
                "mode_display": mode.replace("_", " ").title(),
                "count": count,
                "percentage": count / total_samples * 100 if total_samples > 0 else 0,
                "description": mode_descriptions.get(mode, ""),
            }
            for mode, count in mode_counts.most_common()
        ]

        return {
            "overview": {
                "total_batches": total_batches,
                "total_samples": total_samples,
                "unique_images": unique_images,
                "unique_labels": unique_labels,
                "samples_per_image": round(samples_per_image, 1),
                "samples_per_batch": round(samples_per_batch, 1),
            },
            "mode_distribution": mode_distribution,
            "top_labels": top_labels,
            "recent_batches": recent_batches_data,
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get overview stats: {str(e)}"
        )


@router.get("/stats/modes")
async def get_mode_stats(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取训练模式分布统计

    Returns:
        模式分布统计数据
    """
    try:
        sample_service = SampleService(db)

        # 获取所有样本
        all_samples = sample_service.get_samples_with_filters(limit=10000)
        total_samples = len(all_samples)

        # 统计模式分布
        mode_counts = Counter(sample.mode.value for sample in all_samples)

        mode_descriptions = {
            "grounding": "Description → Coordinate mapping",
            "describe": "Coordinate → Description mapping",
            "enumerate_text": "Element text enumeration",
            "enumerate_coord": "Element coordinate enumeration",
            "checklist": "Boolean checklist validation",
            "ensure": "Target state achievement",
        }

        mode_stats = [
            {
                "mode": mode,
                "mode_display": mode.replace("_", " ").title(),
                "count": count,
                "percentage": count / total_samples * 100 if total_samples > 0 else 0,
                "description": mode_descriptions.get(mode, ""),
            }
            for mode, count in mode_counts.most_common()
        ]

        return {"total_samples": total_samples, "mode_distribution": mode_stats}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get mode stats: {str(e)}"
        )


@router.get("/stats/labels")
async def get_label_stats(
    top: int = 20, min_usage: int = 1, db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取标签使用统计

    Args:
        top: 返回前N个热门标签 (默认20)
        min_usage: 最小使用次数 (默认1)

    Returns:
        标签使用统计数据
    """
    try:
        sample_service = SampleService(db)

        # 获取所有样本
        all_samples = sample_service.get_samples_with_filters(limit=10000)

        # 统计所有标签
        all_labels = []
        for sample in all_samples:
            if sample.labels:
                all_labels.extend(sample.labels)

        total_label_usages = len(all_labels)
        unique_labels = len(set(all_labels))

        # 标签使用统计
        label_counts = Counter(all_labels)

        # 过滤低使用次数的标签并取前N个
        filtered_labels = [
            (label, count)
            for label, count in label_counts.items()
            if count >= min_usage
        ]

        top_labels = [
            {
                "label": label,
                "count": count,
                "percentage": count / total_label_usages * 100
                if total_label_usages > 0
                else 0,
            }
            for label, count in Counter(dict(filtered_labels)).most_common(top)
        ]

        return {
            "total_samples": len(all_samples),
            "total_label_usages": total_label_usages,
            "unique_labels": unique_labels,
            "top_labels": top_labels,
            "filters": {"top": top, "min_usage": min_usage},
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get label stats: {str(e)}"
        )
