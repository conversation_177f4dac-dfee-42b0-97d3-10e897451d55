"""
Ruyi Dataverse CLI Module
"""

import importlib.metadata

import click

__version__ = importlib.metadata.version("ruyidv")


from .commands.admin import admin
from .commands.annotation import annotation
from .commands.batch import batch
from .commands.check import check
from .commands.config import config
from .commands.db import db
from .commands.export import export
from .commands.import_cmd import import_cmd
from .commands.query import query
from .commands.serve import serve
from .commands.stats import stats
from .commands.system import init


@click.group()
@click.version_option(version=__version__, prog_name="ruyidv")
def main():
    """Ruyi Dataverse - Training data management for Ruyi Grounding Model"""
    pass


# 添加命令组
main.add_command(admin)
main.add_command(annotation)
main.add_command(batch)
main.add_command(check)
main.add_command(init)
main.add_command(import_cmd)
main.add_command(export)
main.add_command(query)
main.add_command(stats)
main.add_command(db)
main.add_command(config)
main.add_command(serve)

__all__ = ["main", "annotation", "admin"]
