"""
Admin CLI Commands
管理员相关的命令行工具
"""

from typing import Optional

import click
from rich.console import <PERSON>sole
from rich.panel import Panel
from rich.table import Table
from sqlalchemy.orm import Session

from ...database import get_db
from ...services import AnnotationService, AuthService

console = Console()


@click.group()
def admin():
    """Admin management commands"""
    pass


# Admin access is CLI-only - no need for admin user accounts
# The person with CLI access IS the admin


# User management removed - CLI access = admin access


@admin.command("create-session")
@click.argument("name")
@click.option("--description", help="Session description")
@click.option("--annotator", required=True, help="Annotator name for the session")
@click.option("--annotator-email", help="Annotator email")
@click.option(
    "--batch-ids",
    help="Comma-separated list of batch IDs to include (e.g., '1,2,3')",
)
@click.option(
    "--modes",
    help="Comma-separated list of modes to include (e.g., 'grounding,describe')",
)
@click.option(
    "--labels",
    help="Comma-separated list of labels to include (e.g., 'vehicle,outdoor')",
)
@click.option(
    "--username",
    help="Session username (if not provided, will be generated)",
)
@click.option(
    "--password",
    help="Session password (if not provided, will be generated)",
)
def create_session(
    name: str,
    description: Optional[str],
    annotator: str,
    annotator_email: Optional[str],
    batch_ids: Optional[str],
    modes: Optional[str],
    labels: Optional[str],
    username: Optional[str],
    password: Optional[str],
):
    """
    Create a new annotation session with credentials

    NAME: Name for the annotation session
    """
    console.print(f"[blue]Creating annotation session '{name}'...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        auth_service = AuthService(db)
        annotation_service = AnnotationService(db)

        # Parse query filters
        query_filters = {}

        if batch_ids:
            try:
                query_filters["batch_ids"] = [
                    int(bid.strip()) for bid in batch_ids.split(",")
                ]
            except ValueError:
                console.print(
                    "[red]✗ Invalid batch IDs format. Use comma-separated integers.[/red]"
                )
                raise click.Abort()

        if modes:
            query_filters["modes"] = [mode.strip() for mode in modes.split(",")]

        if labels:
            query_filters["labels"] = [label.strip() for label in labels.split(",")]

        # Create session data
        from ...schemas import AnnotationSessionCreate

        session_data = AnnotationSessionCreate(
            name=name,
            description=description,
            annotator_name=annotator,
            annotator_email=annotator_email,
            query_filters=query_filters,
        )

        # Create the session
        session = annotation_service.create_annotation_session(session_data)

        # Generate credentials if not provided
        if not username:
            username = f"annotator_{session.id}"

        if not password:
            password = auth_service.generate_secure_password(12)
            generated_password = True
        else:
            generated_password = False

        # Create session credentials
        auth_service.create_session_credential(
            session_id=session.id,
            username=username,
            password=password,
        )

        # Display success message
        console.print(
            f"[green]✓ Annotation session '{name}' created successfully![/green]"
        )
        console.print(f"[dim]Session ID: {session.id}[/dim]")
        console.print(f"[dim]Total samples: {session.total_samples}[/dim]")

        # Display credentials
        console.print(
            Panel.fit(
                f"[bold cyan]Session Credentials:[/bold cyan]\n"
                f"[bold]Username:[/bold] {username}\n"
                f"[bold]Password:[/bold] {password}\n"
                f"[bold]Session ID:[/bold] {session.id}\n\n"
                "[yellow]⚠️  Please save these credentials securely![/yellow]\n"
                "[dim]Share these with the annotator to access the session.[/dim]",
                title="🔑 Session Access",
                border_style="cyan",
            )
        )

        if generated_password:
            console.print("[dim]Note: Password was automatically generated[/dim]")

    except ValueError as e:
        console.print(f"[red]✗ Error creating session: {str(e)}[/red]")
        raise click.Abort()
    except Exception as e:
        console.print(f"[red]✗ Unexpected error: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("set-session-password")
@click.argument("session_id", type=int)
@click.option(
    "--password",
    help="New password (if not provided, will be generated)",
)
@click.option("--confirm", is_flag=True, help="Skip confirmation prompt")
def set_session_password(session_id: int, password: Optional[str], confirm: bool):
    """
    Update password for session credentials

    SESSION_ID: ID of the session to update
    """
    if not confirm:
        if not click.confirm(
            f"Are you sure you want to update password for session {session_id}?"
        ):
            console.print("[yellow]Operation cancelled[/yellow]")
            return

    console.print(f"[blue]Updating password for session {session_id}...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        auth_service = AuthService(db)

        # Generate password if not provided
        if not password:
            password = auth_service.generate_secure_password(12)
            generated_password = True
        else:
            generated_password = False

        # Update session password
        success = auth_service.update_session_credential_password(session_id, password)

        if success:
            console.print(f"[green]✓ Password updated for session {session_id}[/green]")

            # Display new password
            console.print(
                Panel.fit(
                    f"[bold yellow]New Password:[/bold yellow] {password}\n\n"
                    "[red]⚠️  Please save this password securely![/red]\n"
                    "[dim]Share this with the annotator to access the session.[/dim]",
                    title="🔑 Updated Credentials",
                    border_style="yellow",
                )
            )

            if generated_password:
                console.print("[dim]Note: Password was automatically generated[/dim]")
        else:
            console.print(
                f"[red]✗ Session {session_id} not found or has no credentials[/red]"
            )
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error updating session password: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("setup-dashboard")
@click.option(
    "--path",
    help="Custom dashboard path (if not provided, will be generated)",
)
@click.option(
    "--password",
    help="Dashboard password (if not provided, will be generated)",
)
@click.option("--confirm", is_flag=True, help="Skip confirmation prompt")
def setup_dashboard(path: Optional[str], password: Optional[str], confirm: bool):
    """
    Setup admin dashboard with authentication (single admin system)
    """
    db: Session | None = None
    try:
        db = next(get_db())
        auth_service = AuthService(db)

        # Check if admin already exists
        existing_cred = auth_service.get_admin_credential()
        if existing_cred and not confirm:
            if not click.confirm(
                f"Admin dashboard already exists at '/{existing_cred.dashboard_path}'. "
                "Do you want to update it?"
            ):
                console.print("[yellow]Operation cancelled[/yellow]")
                return

        if not confirm and not existing_cred:
            if not click.confirm("Are you sure you want to setup the admin dashboard?"):
                console.print("[yellow]Operation cancelled[/yellow]")
                return

        action = "Updating" if existing_cred else "Setting up"
        console.print(f"[blue]{action} admin dashboard...[/blue]")

        # Generate dashboard path if not provided
        if not path:
            path = auth_service.generate_secure_dashboard_path(16)
            generated_path = True
        else:
            generated_path = False

        # Generate password if not provided
        if not password:
            password = auth_service.generate_secure_password(16)
            generated_password = True
        else:
            generated_password = False

        # Create/update admin credential
        credential = auth_service.create_admin_credential(path, password)

        # Display results
        console.print()
        console.print(
            Panel.fit(
                f"[green]✅ Admin dashboard {action.lower()} successful![/green]\n\n"
                f"[bold]Dashboard URL:[/bold] http://{{host}}:{{port}}/{path}\n"
                f"[bold]Dashboard Path:[/bold] /{path}"
                + (" [dim](generated)[/dim]" if generated_path else "")
                + f"\n[bold]Password:[/bold] {password}"
                + (" [dim](generated)[/dim]" if generated_password else "")
                + "\n\n[yellow]⚠️  Please save these credentials securely![/yellow]"
                + f"\n[dim]{action} at: {credential.updated_at if existing_cred else credential.created_at}[/dim]",
                title="Admin Dashboard Setup",
                border_style="green",
            )
        )

        console.print(
            f"\n[green]Dashboard {action.lower()} completed successfully[/green]"
        )

    except Exception as e:
        console.print(f"[red]Error setting up dashboard: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("show-dashboard")
def show_dashboard():
    """
    Show admin dashboard configuration
    """
    console.print("[blue]Checking admin dashboard configuration...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        auth_service = AuthService(db)

        # Get admin credential
        credential = auth_service.get_admin_credential()

        if not credential:
            console.print("[yellow]No admin dashboard configured[/yellow]")
            console.print("\nUse 'ruyidv admin setup-dashboard' to create one.")
            return

        # Display dashboard info
        last_login = (
            credential.last_login_at.strftime("%Y-%m-%d %H:%M:%S")
            if credential.last_login_at
            else "[dim]Never[/dim]"
        )

        console.print()
        console.print(
            Panel.fit(
                f"[green]✅ Admin Dashboard Configured[/green]\n\n"
                f"[bold]Dashboard URL:[/bold] http://{{host}}:{{port}}/{credential.dashboard_path}\n"
                f"[bold]Dashboard Path:[/bold] /{credential.dashboard_path}\n"
                f"[bold]Created:[/bold] {credential.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"[bold]Last Updated:[/bold] {credential.updated_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"[bold]Last Login:[/bold] {last_login}",
                title="Admin Dashboard Status",
                border_style="green",
            )
        )

    except Exception as e:
        console.print(f"[red]Error checking dashboard: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("remove-dashboard")
@click.option("--confirm", is_flag=True, help="Skip confirmation prompt")
def remove_dashboard(confirm: bool):
    """
    Remove admin dashboard configuration
    """
    if not confirm:
        if not click.confirm(
            "Are you sure you want to remove the admin dashboard? This cannot be undone."
        ):
            console.print("[yellow]Operation cancelled[/yellow]")
            return

    console.print("[blue]Removing admin dashboard...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        auth_service = AuthService(db)

        # Check if credential exists
        existing_cred = auth_service.get_admin_credential()
        if not existing_cred:
            console.print("[red]No admin dashboard configured[/red]")
            raise click.Abort()

        # Delete credential
        success = auth_service.delete_admin_credential()

        if success:
            console.print("[green]✅ Admin dashboard removed successfully[/green]")
        else:
            console.print("[red]Failed to remove admin dashboard[/red]")

    except Exception as e:
        console.print(f"[red]Error removing dashboard: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("list-sessions")
@click.option(
    "--limit", type=int, default=20, help="Maximum number of sessions to display"
)
@click.option("--offset", type=int, default=0, help="Number of sessions to skip")
@click.option("--annotator", help="Filter by annotator name")
def list_sessions_admin(limit: int, offset: int, annotator: Optional[str]):
    """List annotation sessions (admin view)"""
    console.print("[blue]Listing annotation sessions...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        annotation_service = AnnotationService(db)

        if annotator:
            sessions = annotation_service.get_annotation_sessions_by_annotator(
                annotator, limit=limit, offset=offset
            )
        else:
            sessions = annotation_service.get_all_annotation_sessions(
                limit=limit, offset=offset
            )

        if not sessions:
            console.print("[yellow]No annotation sessions found[/yellow]")
            return

        # Create table
        table = Table(title="Annotation Sessions")
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Name", style="magenta")
        table.add_column("Annotator", style="green")
        table.add_column("Status", style="yellow")
        table.add_column("Progress", style="blue")
        table.add_column("Created", style="dim")
        table.add_column("Has Credentials", style="red")

        for session in sessions:
            # Calculate progress
            if session.total_samples > 0:
                progress_pct = (session.completed_samples / session.total_samples) * 100
                progress = f"{session.completed_samples}/{session.total_samples} ({progress_pct:.1f}%)"
            else:
                progress = "0/0 (0%)"

            # Check if session has credentials
            has_credentials = (
                "Yes" if hasattr(session, "credential") and session.credential else "No"
            )
            cred_style = "green" if has_credentials == "Yes" else "red"

            table.add_row(
                str(session.id),
                session.name,
                session.annotator_name,
                session.status.value.title(),
                progress,
                session.created_at.strftime("%Y-%m-%d %H:%M"),
                f"[{cred_style}]{has_credentials}[/{cred_style}]",
            )

        console.print(table)

        if len(sessions) == limit:
            console.print(
                f"[dim]Showing {limit} sessions (use --offset to see more)[/dim]"
            )

    except Exception as e:
        console.print(f"[red]Error listing sessions: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("session-info")
@click.argument("session_id", type=int)
def session_info_admin(session_id: int):
    """Show detailed session information including credentials"""
    console.print(f"[blue]Getting information for session {session_id}...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        annotation_service = AnnotationService(db)

        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            console.print(f"[red]✗ Session {session_id} not found[/red]")
            raise click.Abort()

        # Display session information
        console.print(f"\n[bold]Session {session.id}: {session.name}[/bold]")
        console.print(f"[dim]Description:[/dim] {session.description or 'None'}")
        console.print(f"[dim]Annotator:[/dim] {session.annotator_name}")
        console.print(f"[dim]Email:[/dim] {session.annotator_email or 'None'}")
        console.print(f"[dim]Status:[/dim] {session.status.value.title()}")
        console.print(
            f"[dim]Created:[/dim] {session.created_at.strftime('%Y-%m-%d %H:%M:%S')}"
        )

        if session.started_at:
            console.print(
                f"[dim]Started:[/dim] {session.started_at.strftime('%Y-%m-%d %H:%M:%S')}"
            )

        if session.completed_at:
            console.print(
                f"[dim]Completed:[/dim] {session.completed_at.strftime('%Y-%m-%d %H:%M:%S')}"
            )

        # Progress information
        console.print("\n[bold]Progress:[/bold]")
        console.print(f"[dim]Total samples:[/dim] {session.total_samples}")
        console.print(f"[dim]Completed:[/dim] {session.completed_samples}")
        console.print(f"[dim]Correct:[/dim] {session.correct_samples}")
        console.print(f"[dim]Incorrect:[/dim] {session.incorrect_samples}")
        console.print(f"[dim]Skipped:[/dim] {session.skipped_samples}")

        # Query filters
        if session.query_filters:
            console.print("\n[bold]Query Filters:[/bold]")
            for key, value in session.query_filters.items():
                console.print(f"[dim]{key}:[/dim] {value}")

        # Credentials information
        console.print("\n[bold]Credentials:[/bold]")
        if hasattr(session, "credential") and session.credential:
            console.print(f"[dim]Username:[/dim] {session.credential.username}")
            console.print(
                f"[dim]Created:[/dim] {session.credential.created_at.strftime('%Y-%m-%d %H:%M:%S')}"
            )
            console.print("[green]✓ Session has credentials[/green]")
        else:
            console.print("[red]✗ No credentials found for this session[/red]")
            console.print(
                "[yellow]Use 'admin create-session-credentials' to add credentials[/yellow]"
            )

    except Exception as e:
        console.print(f"[red]Error getting session info: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("delete-session")
@click.argument("session_id", type=int)
@click.option("--confirm", is_flag=True, help="Skip confirmation prompt")
def delete_session(session_id: int, confirm: bool):
    """
    Delete an annotation session and all its data

    SESSION_ID: ID of the session to delete
    """
    console.print(f"[blue]Getting session information for {session_id}...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        annotation_service = AnnotationService(db)

        # Get session info first
        session = annotation_service.get_annotation_session_by_id(session_id)
        if not session:
            console.print(f"[red]✗ Session {session_id} not found[/red]")
            raise click.Abort()

        # Show session info before deletion
        console.print("\n[bold]Session to delete:[/bold]")
        console.print(f"[dim]ID:[/dim] {session.id}")
        console.print(f"[dim]Name:[/dim] {session.name}")
        console.print(f"[dim]Annotator:[/dim] {session.annotator_name}")
        console.print(f"[dim]Status:[/dim] {session.status.value.title()}")
        console.print(f"[dim]Total samples:[/dim] {session.total_samples}")
        console.print(f"[dim]Completed:[/dim] {session.completed_samples}")

        if not confirm:
            console.print(
                "\n[red]⚠️  This will permanently delete the session and all annotation data![/red]"
            )
            if not click.confirm(
                f"Are you sure you want to delete session {session_id}?"
            ):
                console.print("[yellow]Operation cancelled[/yellow]")
                return

        console.print(f"[blue]Deleting session {session_id}...[/blue]")

        # Delete the session (this should cascade to delete credentials and sample results)
        success = annotation_service.delete_annotation_session(session_id)

        if success:
            console.print(f"[green]✓ Session {session_id} deleted successfully[/green]")
        else:
            console.print(f"[red]✗ Failed to delete session {session_id}[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error deleting session: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@admin.command("audit-logs")
@click.option("--event-type", help="Filter by event type")
@click.option("--username", help="Filter by username")
@click.option("--session-id", type=int, help="Filter by session ID")
@click.option("--limit", type=int, default=50, help="Maximum number of logs to display")
@click.option("--offset", type=int, default=0, help="Number of logs to skip")
def audit_logs(
    event_type: Optional[str],
    username: Optional[str],
    session_id: Optional[int],
    limit: int,
    offset: int,
):
    """View audit logs"""
    console.print("[blue]Retrieving audit logs...[/blue]")

    db = None
    try:
        from ...database import SessionLocal
        from ...models import AuditEventType
        from ...services.audit_service import AuditService

        db = SessionLocal()
        audit_service = AuditService(db)

        # Convert event type string to enum if provided
        event_type_enum = None
        if event_type:
            try:
                event_type_enum = AuditEventType(event_type)
            except ValueError:
                console.print(f"[red]Invalid event type: {event_type}[/red]")
                console.print(
                    f"Valid types: {', '.join([e.value for e in AuditEventType])}"
                )
                raise click.Abort()

        # Get audit logs
        logs = audit_service.get_audit_logs(
            event_type=event_type_enum,
            username=username,
            session_id=session_id,
            limit=limit,
            offset=offset,
        )

        if not logs:
            console.print("[yellow]No audit logs found matching the criteria[/yellow]")
            return

        # Display logs in a table
        logs_table = Table(show_header=True, header_style="bold cyan")
        logs_table.add_column("Timestamp", width=20)
        logs_table.add_column("Event", width=15)
        logs_table.add_column("User", width=15)
        logs_table.add_column("Session", width=8)
        logs_table.add_column("Status", width=8)
        logs_table.add_column("Details", width=30)

        for log in logs:
            # Format timestamp
            timestamp = log.timestamp.strftime("%Y-%m-%d %H:%M:%S")

            # Format event type
            event = log.event_type.value

            # Format username
            user = log.username or "N/A"

            # Format session ID
            session = str(log.session_id) if log.session_id else "N/A"

            # Format status
            status = "✅" if log.success else "❌"

            # Format details
            details = ""
            if log.details:
                details_list = []
                for key, value in log.details.items():
                    details_list.append(f"{key}: {value}")
                details = ", ".join(details_list[:2])  # Show first 2 details
                if len(log.details) > 2:
                    details += "..."

            if log.error_message:
                details = (
                    log.error_message[:30] + "..."
                    if len(log.error_message) > 30
                    else log.error_message
                )

            logs_table.add_row(timestamp, event, user, session, status, details)

        console.print(
            Panel(
                logs_table,
                title=f"🔍 Audit Logs (showing {len(logs)} of {limit} max)",
                border_style="cyan",
            )
        )

        # Show summary
        if len(logs) == limit:
            console.print(
                f"[yellow]Showing {limit} logs. Use --offset and --limit to see more.[/yellow]"
            )

    except Exception as e:
        console.print(f"[red]Error retrieving audit logs: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()
