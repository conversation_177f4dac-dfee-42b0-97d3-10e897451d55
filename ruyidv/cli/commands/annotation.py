"""
Annotation CLI Commands
标注相关的命令行工具
"""

from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table
from sqlalchemy.orm import Session

from ...database import get_db
from ...services import AnnotationApplyService, AnnotationExportService

console = Console()


@click.group()
def annotation():
    """Annotation management commands"""
    pass


@annotation.command("export-session")
@click.argument("session_id", type=int)
@click.option(
    "--output",
    "-o",
    type=click.Path(path_type=Path),
    required=True,
    help="Output JSON file path",
)
@click.option(
    "--only-incorrect", is_flag=True, help="Export only samples marked as incorrect"
)
@click.option(
    "--include-notes",
    is_flag=True,
    default=True,
    help="Include annotator notes in export",
)
def export_session(
    session_id: int, output: Path, only_incorrect: bool, include_notes: bool
):
    """
    Export annotation session results to JSON format

    SESSION_ID: ID of the annotation session to export
    """
    console.print(f"[blue]Exporting annotation session {session_id}...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        export_service = AnnotationExportService(db)

        # Get session summary first
        summary = export_service.get_session_summary(session_id)
        if not summary:
            console.print(
                f"[red]Error: Annotation session {session_id} not found[/red]"
            )
            raise click.Abort()

        # Display session info
        console.print("\n[bold]Session Information:[/bold]")
        info_table = Table(show_header=False, box=None)
        info_table.add_column("Field", style="cyan")
        info_table.add_column("Value", style="white")

        info_table.add_row("Session ID", str(summary["session_id"]))
        info_table.add_row("Session Name", summary["session_name"])
        info_table.add_row(
            "Annotator", f"{summary['annotator_name']} ({summary['annotator_email']})"
        )
        info_table.add_row("Status", summary["status"])
        info_table.add_row("Total Samples", str(summary["total_samples"]))
        info_table.add_row("Completed", str(summary["completed_samples"]))
        info_table.add_row("Correct", str(summary["correct_samples"]))
        info_table.add_row("Incorrect", str(summary["incorrect_samples"]))
        info_table.add_row("Skipped", str(summary["skipped_samples"]))

        console.print(info_table)

        # Confirm export
        if not click.confirm(f"\nProceed with export to {output}?"):
            console.print("[yellow]Export cancelled[/yellow]")
            return

        # Perform export
        result = export_service.export_session_to_json(session_id, output)

        if result.success:
            console.print(f"[green]✓ {result.message}[/green]")

            # Show export summary
            console.print("\n[bold]Export Summary:[/bold]")
            console.print(f"• Exported {result.exported_count} annotation results")
            console.print(f"• Output file: {output}")
            console.print(f"• File size: {output.stat().st_size:,} bytes")

        else:
            console.print(f"[red]✗ {result.message}[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error during export: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("list-sessions")
@click.option("--annotator", help="Filter by annotator name")
@click.option(
    "--status",
    type=click.Choice(["active", "completed", "cancelled"]),
    help="Filter by session status",
)
@click.option(
    "--limit", type=int, default=20, help="Maximum number of sessions to display"
)
def list_sessions(annotator: Optional[str], status: Optional[str], limit: int):
    """List annotation sessions"""
    console.print("[blue]Listing annotation sessions...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        export_service = AnnotationExportService(db)

        sessions = export_service.list_annotation_sessions(annotator, status, limit)

        if not sessions:
            console.print("[yellow]No annotation sessions found[/yellow]")
            return

        # Display sessions table
        sessions_table = Table()
        sessions_table.add_column("ID", style="cyan", justify="right")
        sessions_table.add_column("Name", style="white", max_width=30)
        sessions_table.add_column("Annotator", style="green", max_width=20)
        sessions_table.add_column("Status", style="yellow")
        sessions_table.add_column("Samples", style="blue", justify="right")
        sessions_table.add_column("Completed", style="blue", justify="right")
        sessions_table.add_column("Created", style="dim")

        for session in sessions:
            completed_at = session["completed_at"]
            status_display = session["status"]
            if status_display == "completed" and completed_at:
                status_display = f"✓ {status_display}"
            elif status_display == "active":
                status_display = f"⏳ {status_display}"
            elif status_display == "cancelled":
                status_display = f"✗ {status_display}"

            sessions_table.add_row(
                str(session["session_id"]),
                session["session_name"],
                session["annotator_name"],
                status_display,
                str(session["total_samples"]),
                str(session["completed_samples"]),
                session["created_at"][:10],  # Show only date
            )

        console.print(sessions_table)
        console.print(f"\n[dim]Showing {len(sessions)} of up to {limit} sessions[/dim]")

    except Exception as e:
        console.print(f"[red]Error listing sessions: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("session-info")
@click.argument("session_id", type=int)
def session_info(session_id: int):
    """Show detailed information about an annotation session"""
    console.print(f"[blue]Getting information for session {session_id}...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        export_service = AnnotationExportService(db)

        summary = export_service.get_session_summary(session_id)
        if not summary:
            console.print(
                f"[red]Error: Annotation session {session_id} not found[/red]"
            )
            raise click.Abort()

        # Display detailed session information
        console.print(f"\n[bold]Session {session_id} Details:[/bold]")

        info_table = Table(show_header=False, box=None)
        info_table.add_column("Field", style="cyan", width=20)
        info_table.add_column("Value", style="white")

        info_table.add_row("Session ID", str(summary["session_id"]))
        info_table.add_row("Name", summary["session_name"])
        info_table.add_row("Annotator", summary["annotator_name"])
        info_table.add_row("Email", summary["annotator_email"])
        info_table.add_row("Status", summary["status"])
        info_table.add_row("Created", summary["created_at"])
        if summary["completed_at"]:
            info_table.add_row("Completed", summary["completed_at"])

        console.print(info_table)

        # Statistics table
        console.print("\n[bold]Annotation Statistics:[/bold]")
        stats_table = Table(show_header=False, box=None)
        stats_table.add_column("Metric", style="cyan", width=20)
        stats_table.add_column("Count", style="white", justify="right")
        stats_table.add_column("Percentage", style="green", justify="right")

        total = summary["total_samples"]
        completed = summary["completed_samples"]
        correct = summary["correct_samples"]
        incorrect = summary["incorrect_samples"]
        skipped = summary["skipped_samples"]

        stats_table.add_row("Total Samples", str(total), "100.0%")
        stats_table.add_row(
            "Completed",
            str(completed),
            f"{completed / total * 100:.1f}%" if total > 0 else "0.0%",
        )
        stats_table.add_row(
            "Correct",
            str(correct),
            f"{correct / total * 100:.1f}%" if total > 0 else "0.0%",
        )
        stats_table.add_row(
            "Incorrect",
            str(incorrect),
            f"{incorrect / total * 100:.1f}%" if total > 0 else "0.0%",
        )
        stats_table.add_row(
            "Skipped",
            str(skipped),
            f"{skipped / total * 100:.1f}%" if total > 0 else "0.0%",
        )

        console.print(stats_table)

    except Exception as e:
        console.print(f"[red]Error getting session info: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("validate")
@click.argument("json_file", type=click.Path(exists=True, path_type=Path))
def validate(json_file: Path):
    """
    Validate annotation corrections JSON file

    JSON_FILE: Path to the annotation corrections JSON file
    """
    console.print(f"[blue]Validating annotation corrections file: {json_file}[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        apply_service = AnnotationApplyService(db)

        # Perform validation
        result = apply_service.validate_corrections_json(json_file)

        if result.success:
            console.print("[green]✓ Validation successful[/green]")

            # Show validation summary
            console.print("\n[bold]Validation Summary:[/bold]")
            summary_table = Table(show_header=False, box=None)
            summary_table.add_column("Metric", style="cyan", width=20)
            summary_table.add_column("Count", style="white", justify="right")

            summary_table.add_row("Total Samples", str(result.total_samples))
            summary_table.add_row("Valid Samples", str(result.valid_samples))
            summary_table.add_row("Invalid Samples", str(result.invalid_samples))
            summary_table.add_row("Warnings", str(len(result.warnings)))

            console.print(summary_table)

            # Show warnings if any
            if result.warnings:
                console.print(f"\n[yellow]Warnings ({len(result.warnings)}):[/yellow]")
                for warning in result.warnings:
                    console.print(f"  • {warning}")

        else:
            console.print(f"[red]✗ Validation failed: {result.message}[/red]")

            # Show errors
            if result.errors:
                console.print(f"\n[red]Errors ({len(result.errors)}):[/red]")
                for error in result.errors:
                    console.print(f"  • {error}")

            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error during validation: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("preview")
@click.argument("json_file", type=click.Path(exists=True, path_type=Path))
@click.option(
    "--limit", type=int, default=10, help="Maximum number of changes to display"
)
def preview(json_file: Path, limit: int):
    """
    Preview changes that will be applied from annotation corrections

    JSON_FILE: Path to the annotation corrections JSON file
    """
    console.print(f"[blue]Previewing changes from: {json_file}[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        apply_service = AnnotationApplyService(db)

        # Generate preview
        result = apply_service.preview_changes(json_file)

        if result.success:
            console.print("[green]✓ Preview generated successfully[/green]")

            if result.total_changes == 0:
                console.print(
                    "[yellow]No changes will be applied (no incorrect samples with corrections)[/yellow]"
                )
                return

            console.print("\n[bold]Preview Summary:[/bold]")
            console.print(f"• Total changes: {result.total_changes}")
            console.print(
                f"• Showing first {min(limit, result.total_changes)} changes:"
            )

            # Show changes table
            changes_table = Table()
            changes_table.add_column("Sample ID", style="cyan")
            changes_table.add_column("Action", style="green")
            changes_table.add_column("Current Metadata", style="white", max_width=30)
            changes_table.add_column("New Metadata", style="yellow", max_width=30)
            changes_table.add_column("Notes", style="dim", max_width=20)

            for change in result.changes[:limit]:
                current_meta = str(change["current_metadata"])
                new_meta = str(change["new_metadata"])
                notes = change.get("notes", "")

                # Truncate long strings
                if len(current_meta) > 50:
                    current_meta = current_meta[:47] + "..."
                if len(new_meta) > 50:
                    new_meta = new_meta[:47] + "..."
                if len(notes) > 30:
                    notes = notes[:27] + "..."

                changes_table.add_row(
                    str(change["sample_id"]),
                    change["action"],
                    current_meta,
                    new_meta,
                    notes,
                )

            console.print(changes_table)

            if result.total_changes > limit:
                console.print(
                    f"\n[dim]... and {result.total_changes - limit} more changes[/dim]"
                )

        else:
            console.print(f"[red]✗ Preview failed: {result.message}[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error during preview: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("apply")
@click.argument("json_file", type=click.Path(exists=True, path_type=Path))
@click.option(
    "--interactive",
    "-i",
    is_flag=True,
    help="Enable interactive confirmation before applying changes",
)
def apply_corrections(json_file: Path, interactive: bool):
    """
    Apply annotation corrections to the database

    JSON_FILE: Path to the annotation corrections JSON file
    """
    console.print(f"[blue]Applying corrections from: {json_file}[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        apply_service = AnnotationApplyService(db)

        # Show preview first
        preview_result = apply_service.preview_changes(json_file)
        if not preview_result.success:
            console.print(f"[red]✗ Preview failed: {preview_result.message}[/red]")
            raise click.Abort()

        if preview_result.total_changes == 0:
            console.print(
                "[yellow]No changes to apply (no incorrect samples with corrections)[/yellow]"
            )
            return

        console.print("\n[bold]Changes to Apply:[/bold]")
        console.print(f"• Total samples to update: {preview_result.total_changes}")

        # Apply corrections
        result = apply_service.apply_corrections(json_file, interactive)

        if result.success:
            console.print(f"[green]✓ {result.message}[/green]")

            if result.applied_count > 0:
                console.print("\n[bold]Apply Summary:[/bold]")
                console.print(f"• Successfully updated: {result.applied_count} samples")
                if result.failed_count > 0:
                    console.print(f"• Failed to update: {result.failed_count} samples")

                # Show operation ID for rollback
                if result.operation_id:
                    console.print(f"• Operation ID: {result.operation_id}")
                    console.print("  [dim](Save this ID for potential rollback)[/dim]")

                # Show applied sample IDs
                if result.applied_samples:
                    sample_ids = ", ".join(map(str, result.applied_samples[:10]))
                    if len(result.applied_samples) > 10:
                        sample_ids += (
                            f" ... and {len(result.applied_samples) - 10} more"
                        )
                    console.print(f"• Updated sample IDs: {sample_ids}")

            # Show errors if any
            if result.errors:
                console.print(f"\n[red]Errors ({len(result.errors)}):[/red]")
                for error in result.errors:
                    console.print(f"  • {error}")

        else:
            console.print(f"[red]✗ {result.message}[/red]")

            if result.errors:
                console.print(f"\n[red]Errors ({len(result.errors)}):[/red]")
                for error in result.errors:
                    console.print(f"  • {error}")

            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error during apply: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("rollback")
@click.argument("operation_id", type=str)
@click.option(
    "--rollback-by", default="admin", help="Name of the person performing the rollback"
)
def rollback_operation(operation_id: str, rollback_by: str):
    """
    Rollback a previous apply operation

    OPERATION_ID: The operation ID to rollback (from apply operation output)
    """
    console.print(f"[blue]Rolling back operation: {operation_id}[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        apply_service = AnnotationApplyService(db)

        # Confirm rollback
        if not click.confirm(
            f"Are you sure you want to rollback operation {operation_id}?"
        ):
            console.print("[yellow]Rollback cancelled[/yellow]")
            return

        # Perform rollback
        result = apply_service.rollback_operation(operation_id, rollback_by)

        if result.success:
            console.print(f"[green]✓ {result.message}[/green]")

            if result.applied_count > 0:
                console.print("\n[bold]Rollback Summary:[/bold]")
                console.print(
                    f"• Successfully rolled back: {result.applied_count} samples"
                )
                if result.failed_count > 0:
                    console.print(
                        f"• Failed to rollback: {result.failed_count} samples"
                    )

                # Show rolled back sample IDs
                if result.applied_samples:
                    sample_ids = ", ".join(map(str, result.applied_samples[:10]))
                    if len(result.applied_samples) > 10:
                        sample_ids += (
                            f" ... and {len(result.applied_samples) - 10} more"
                        )
                    console.print(f"• Rolled back sample IDs: {sample_ids}")

            # Show errors if any
            if result.errors:
                console.print(f"\n[red]Errors ({len(result.errors)}):[/red]")
                for error in result.errors:
                    console.print(f"  • {error}")

        else:
            console.print(f"[red]✗ {result.message}[/red]")

            if result.errors:
                console.print(f"\n[red]Errors ({len(result.errors)}):[/red]")
                for error in result.errors:
                    console.print(f"  • {error}")

            raise click.Abort()

    except Exception as e:
        console.print(f"[red]Error during rollback: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()


@annotation.command("list-operations")
@click.option(
    "--limit", type=int, default=20, help="Maximum number of operations to display"
)
def list_operations(limit: int):
    """List annotation apply operations history"""
    console.print("[blue]Listing annotation apply operations...[/blue]")

    db: Session | None = None
    try:
        db = next(get_db())
        apply_service = AnnotationApplyService(db)

        operations = apply_service.list_apply_operations(limit)

        if not operations:
            console.print("[yellow]No apply operations found[/yellow]")
            return

        # Display operations table
        ops_table = Table()
        ops_table.add_column("Operation ID", style="cyan", max_width=12)
        ops_table.add_column("Applied At", style="white")
        ops_table.add_column("Applied By", style="green")
        ops_table.add_column("Source File", style="blue", max_width=30)
        ops_table.add_column("Changes", style="yellow", justify="right")
        ops_table.add_column("Status", style="magenta")

        for op in operations:
            # Truncate operation ID for display
            op_id_display = op["operation_id"][:8] + "..."

            # Truncate source file path
            source_file = op["source_file"]
            if len(source_file) > 40:
                source_file = "..." + source_file[-37:]

            # Status display with icons
            status = op["status"]
            if status == "applied":
                status_display = "✓ Applied"
            elif status == "rolled_back":
                status_display = "↶ Rolled Back"
            elif status == "partially_rolled_back":
                status_display = "⚠ Partial Rollback"
            else:
                status_display = status

            ops_table.add_row(
                op_id_display,
                op["applied_at"][:19].replace("T", " "),  # Show date and time
                op["applied_by"],
                source_file,
                str(op["total_changes"]),
                status_display,
            )

        console.print(ops_table)
        console.print(
            f"\n[dim]Showing {len(operations)} of up to {limit} operations[/dim]"
        )
        console.print(
            "[dim]Use the full operation ID from apply command output for rollback[/dim]"
        )

    except Exception as e:
        console.print(f"[red]Error listing operations: {str(e)}[/red]")
        raise click.Abort()
    finally:
        if db is not None:
            db.close()
