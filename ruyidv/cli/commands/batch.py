"""
Batch management commands
"""

from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table

from ruyidv.database import SessionLocal
from ruyidv.schemas import BatchCreate
from ruyidv.services import BatchService

console = Console()


@click.group()
def batch():
    """Batch management commands"""
    pass


@batch.command()
@click.argument("name")
@click.option("--description", "-d", help="Batch description")
def create(name: str, description: Optional[str] = None):
    """Create a new batch"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        # 检查名称是否已存在
        existing = batch_service.get_batch_by_name(name)
        if existing:
            console.print(
                f"[red]❌ Batch with name '{name}' already exists (ID: {existing.id})[/red]"
            )
            raise click.Abort()

        # 创建批次
        batch_data = BatchCreate(name=name, description=description)
        batch = batch_service.create_batch(batch_data)

        console.print(
            Panel.fit(
                f"[green]✓[/green] Batch created successfully!\n\n"
                f"[bold]ID:[/bold] {batch.id}\n"
                f"[bold]Name:[/bold] {batch.name}\n"
                f"[bold]Description:[/bold] {batch.description or 'None'}\n"
                f"[bold]Created:[/bold] {batch.created_at}",
                title="🎉 Batch Created",
                border_style="green",
            )
        )

    except Exception as e:
        console.print(f"[red]❌ Failed to create batch: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@batch.command()
@click.option("--limit", "-l", default=50, help="Maximum number of batches to show")
@click.option("--search", "-s", help="Search keyword in name and description")
def list(limit: int, search: Optional[str] = None):
    """List all batches"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        if search:
            batches = batch_service.search_batches(search, limit)
            title = f"🔍 Search Results for '{search}'"
        else:
            batches = batch_service.get_all_batches(limit=limit)
            title = "📦 All Batches"

        if not batches:
            console.print("[yellow]No batches found.[/yellow]")
            return

        # 创建表格
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("ID", justify="right", width=6)
        table.add_column("Name", min_width=20)
        table.add_column("Description", min_width=30)
        table.add_column("Created", width=20)

        for batch in batches:
            table.add_row(
                str(batch.id),
                batch.name,
                batch.description or "[dim]No description[/dim]",
                batch.created_at.strftime("%Y-%m-%d %H:%M"),
            )

        console.print(Panel(table, title=title, border_style="blue"))
        console.print(f"\n[dim]Showing {len(batches)} batch(es)[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Failed to list batches: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@batch.command()
@click.argument("batch_id", type=int)
def show(batch_id: int):
    """Show detailed information about a batch"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        # 获取批次基本信息
        batch = batch_service.get_batch_by_id(batch_id)
        if not batch:
            console.print(f"[red]❌ Batch with ID {batch_id} not found[/red]")
            raise click.Abort()

        # 获取统计信息
        stats = batch_service.get_batch_statistics(batch_id)

        # 基本信息面板
        basic_info = (
            f"[bold]Name:[/bold] {batch.name}\n"
            f"[bold]Description:[/bold] {batch.description or 'None'}\n"
            f"[bold]Created:[/bold] {batch.created_at}\n"
        )

        console.print(
            Panel(
                basic_info,
                title=f"📦 Batch {batch_id} - Basic Info",
                border_style="blue",
            )
        )

        if stats:
            # 统计信息表格
            stats_table = Table(show_header=True, header_style="bold cyan")
            stats_table.add_column("Metric", style="bold")
            stats_table.add_column("Value", justify="right")

            stats_table.add_row("Total Samples", str(stats["total_samples"]))
            stats_table.add_row("Unique Images", str(stats["unique_images"]))
            stats_table.add_row("Unique Labels", str(len(stats["all_labels"])))

            console.print(
                Panel(stats_table, title="📊 Statistics", border_style="green")
            )

            # 模式分布
            if stats["mode_distribution"]:
                mode_table = Table(show_header=True, header_style="bold yellow")
                mode_table.add_column("Mode", style="bold")
                mode_table.add_column("Count", justify="right")

                for mode, count in stats["mode_distribution"].items():
                    mode_table.add_row(mode, str(count))

                console.print(
                    Panel(
                        mode_table,
                        title="🎯 Mode Distribution",
                        border_style="yellow",
                    )
                )

            # 标签列表
            if stats["all_labels"]:
                labels_str = ", ".join(stats["all_labels"][:20])  # 显示前20个标签
                if len(stats["all_labels"]) > 20:
                    labels_str += f" ... and {len(stats['all_labels']) - 20} more"

                console.print(
                    Panel(
                        f"[bold]Labels ({len(stats['all_labels'])}):[/bold]\n{labels_str}",
                        title="🏷️ Labels",
                        border_style="magenta",
                    )
                )

    except Exception as e:
        console.print(f"[red]❌ Failed to show batch: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@batch.command()
@click.argument("batch_id", type=int)
@click.option("--force", is_flag=True, help="Force deletion without confirmation")
def delete(batch_id: int, force: bool):
    """Delete a batch"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        # 检查批次是否存在
        batch = batch_service.get_batch_by_id(batch_id)
        if not batch:
            console.print(f"[red]❌ Batch with ID {batch_id} not found[/red]")
            raise click.Abort()

        # 获取统计信息以显示删除影响
        stats = batch_service.get_batch_statistics(batch_id)

        # 确认删除
        if not force:
            console.print(
                Panel(
                    f"[bold red]Warning![/bold red] You are about to delete:\n\n"
                    f"[bold]Batch:[/bold] {batch.name} (ID: {batch_id})\n"
                    f"[bold]Samples:[/bold] {stats['total_samples'] if stats else 'Unknown'}\n"
                    f"[bold]Images:[/bold] {stats['unique_images'] if stats else 'Unknown'}\n\n"
                    "[red]This action cannot be undone![/red]",
                    title="⚠️ Confirm Deletion",
                    border_style="red",
                )
            )

            if not Confirm.ask("Are you sure you want to delete this batch?"):
                console.print("[yellow]Deletion cancelled.[/yellow]")
                return

        # 执行删除
        if batch_service.delete_batch(batch_id):
            console.print(
                Panel.fit(
                    f"[green]✓[/green] Batch '{batch.name}' (ID: {batch_id}) deleted successfully!",
                    title="🗑️ Batch Deleted",
                    border_style="green",
                )
            )
        else:
            console.print(f"[red]❌ Failed to delete batch {batch_id}[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]❌ Failed to delete batch: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@batch.command()
@click.argument("batch_id", type=int)
def stats(batch_id: int):
    """Show detailed statistics for a batch"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        # 获取统计信息
        stats = batch_service.get_batch_statistics(batch_id)
        if not stats:
            console.print(f"[red]❌ Batch with ID {batch_id} not found[/red]")
            raise click.Abort()

        # 概览统计
        overview_table = Table(show_header=True, header_style="bold cyan")
        overview_table.add_column("Metric", style="bold", width=20)
        overview_table.add_column("Value", justify="right", width=15)

        overview_table.add_row("Batch ID", str(stats["batch_id"]))
        overview_table.add_row("Batch Name", stats["batch_name"])
        overview_table.add_row("Total Samples", str(stats["total_samples"]))
        overview_table.add_row("Unique Images", str(stats["unique_images"]))
        overview_table.add_row("Unique Labels", str(len(stats["all_labels"])))
        overview_table.add_row(
            "Created", stats["created_at"].strftime("%Y-%m-%d %H:%M:%S")
        )

        console.print(
            Panel(overview_table, title="📊 Batch Overview", border_style="blue")
        )

        # 模式分布详情
        if stats["mode_distribution"]:
            mode_table = Table(show_header=True, header_style="bold yellow")
            mode_table.add_column("Training Mode", style="bold", width=20)
            mode_table.add_column("Sample Count", justify="right", width=15)
            mode_table.add_column("Percentage", justify="right", width=12)

            total_samples = stats["total_samples"]
            for mode, count in stats["mode_distribution"].items():
                percentage = (count / total_samples * 100) if total_samples > 0 else 0
                mode_table.add_row(
                    mode.replace("_", " ").title(), str(count), f"{percentage:.1f}%"
                )

            console.print(
                Panel(mode_table, title="🎯 Mode Distribution", border_style="yellow")
            )

        # 标签分布
        if stats["all_labels"]:
            # 将标签按10个一行显示
            labels = stats["all_labels"]
            label_rows = [labels[i : i + 10] for i in range(0, len(labels), 10)]

            labels_text = ""
            for row in label_rows:
                labels_text += ", ".join(row) + "\n"

            console.print(
                Panel(
                    f"[bold]Total Labels:[/bold] {len(labels)}\n\n{labels_text.rstrip()}",
                    title="🏷️ All Labels",
                    border_style="magenta",
                )
            )
        else:
            console.print(
                Panel(
                    "[dim]No labels found in this batch[/dim]",
                    title="🏷️ Labels",
                    border_style="magenta",
                )
            )

    except Exception as e:
        console.print(f"[red]❌ Failed to get batch statistics: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()
