"""
Data integrity check commands
"""

from pathlib import Path
from typing import Dict, List, Tuple

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from sqlalchemy import text

from ruyidv.config import settings
from ruyidv.database import SessionLocal
from ruyidv.models import Image, Sample
from ruyidv.services import ImageService, ImportService

console = Console()


@click.group()
def check():
    """Data integrity check commands"""
    pass


@check.command()
@click.option(
    "--fix",
    is_flag=True,
    help="Automatically fix detected issues (orphaned files, broken links)",
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Show what would be fixed without making changes",
)
def integrity(fix: bool = False, dry_run: bool = False):
    """Comprehensive data integrity check"""
    db = SessionLocal()
    try:
        console.print(
            "[bold blue]🔍 Starting comprehensive data integrity check...[/bold blue]\n"
        )

        image_service = ImageService(db)
        import_service = ImportService(db)

        # 统计变量
        issues_found = 0
        issues_fixed = 0

        # 1. 检查数据库中的图片记录
        console.print("[cyan]1. Checking database image records...[/cyan]")
        db_orphaned_images, db_broken_links = check_database_images_optimized(
            db, image_service, console=console
        )

        if db_orphaned_images:
            issues_found += len(db_orphaned_images)
            console.print(
                f"   [yellow]⚠️ Found {len(db_orphaned_images)} orphaned image records[/yellow]"
            )

            if fix and not dry_run:
                cleaned = import_service._cleanup_orphaned_images()
                issues_fixed += cleaned
                console.print(
                    f"   [green]✅ Cleaned {cleaned} orphaned records[/green]"
                )
            elif dry_run:
                console.print(
                    f"   [blue]🔧 Would clean {len(db_orphaned_images)} orphaned records[/blue]"
                )
        else:
            console.print("   [green]✅ No orphaned image records found[/green]")

        if db_broken_links:
            issues_found += len(db_broken_links)
            console.print(
                f"   [yellow]⚠️ Found {len(db_broken_links)} broken file links[/yellow]"
            )

            if fix and not dry_run:
                fixed_links = fix_broken_links(db, db_broken_links)
                issues_fixed += fixed_links
                console.print(f"   [green]✅ Fixed {fixed_links} broken links[/green]")
            elif dry_run:
                console.print(
                    f"   [blue]🔧 Would attempt to fix {len(db_broken_links)} broken links[/blue]"
                )
        else:
            console.print("   [green]✅ No broken file links found[/green]")

        # 2. 检查存储目录中的物理文件
        console.print("\n[cyan]2. Checking physical storage files...[/cyan]")
        storage_orphaned_files = check_storage_files_optimized(db, console=console)

        if storage_orphaned_files:
            issues_found += len(storage_orphaned_files)
            console.print(
                f"   [yellow]⚠️ Found {len(storage_orphaned_files)} orphaned files in storage[/yellow]"
            )

            if fix and not dry_run:
                cleaned_files = cleanup_orphaned_files(storage_orphaned_files)
                issues_fixed += cleaned_files
                console.print(
                    f"   [green]✅ Cleaned {cleaned_files} orphaned files[/green]"
                )
            elif dry_run:
                console.print(
                    f"   [blue]🔧 Would clean {len(storage_orphaned_files)} orphaned files[/blue]"
                )
        else:
            console.print("   [green]✅ No orphaned files found in storage[/green]")

        # 3. 检查批次和样本完整性
        console.print("\n[cyan]3. Checking batch and sample integrity...[/cyan]")
        batch_issues = check_batch_integrity_optimized(db, console=console)

        if batch_issues:
            issues_found += len(batch_issues)
            console.print(
                f"   [yellow]⚠️ Found {len(batch_issues)} batch/sample issues[/yellow]"
            )
            for issue in batch_issues[:5]:  # 显示前5个问题
                console.print(f"     • {issue}")
            if len(batch_issues) > 5:
                console.print(f"     ... and {len(batch_issues) - 5} more issues")
        else:
            console.print("   [green]✅ All batches and samples are valid[/green]")

        # 4. 检查存储空间使用
        console.print("\n[cyan]4. Checking storage usage...[/cyan]")
        storage_stats = get_storage_statistics_optimized(console=console)
        console.print(f"   📁 Storage location: {settings.STORAGE_ROOT}")
        console.print(f"   📊 Total files: {storage_stats['total_files']}")
        console.print(f"   💾 Total size: {format_size(storage_stats['total_size'])}")
        console.print(f"   📈 Database records: {storage_stats['db_records']}")

        # 显示汇总结果
        console.print("\n" + "=" * 60)

        if issues_found == 0:
            console.print(
                Panel.fit(
                    "[green]✅ Data integrity check completed successfully![/green]\n"
                    "No issues found. Your data is in perfect condition.",
                    title="🎉 Integrity Check Complete",
                    border_style="green",
                )
            )
        else:
            if fix and not dry_run:
                console.print(
                    Panel.fit(
                        f"[yellow]⚠️ Integrity check completed with fixes applied[/yellow]\n\n"
                        f"[bold]Issues found:[/bold] {issues_found}\n"
                        f"[bold]Issues fixed:[/bold] {issues_fixed}\n"
                        f"[bold]Remaining issues:[/bold] {issues_found - issues_fixed}",
                        title="🔧 Integrity Check with Fixes",
                        border_style="yellow",
                    )
                )
            elif dry_run:
                console.print(
                    Panel.fit(
                        f"[blue]🔍 Dry run completed[/blue]\n\n"
                        f"[bold]Issues found:[/bold] {issues_found}\n"
                        f"[bold]Would be fixed:[/bold] {issues_found}\n\n"
                        "Run with --fix to apply fixes.",
                        title="🔍 Integrity Check Dry Run",
                        border_style="blue",
                    )
                )
            else:
                console.print(
                    Panel.fit(
                        f"[red]⚠️ Integrity check found issues[/red]\n\n"
                        f"[bold]Issues found:[/bold] {issues_found}\n\n"
                        "Run with --fix to automatically fix issues, or --dry-run to preview fixes.",
                        title="⚠️ Integrity Issues Found",
                        border_style="red",
                    )
                )

    except Exception as e:
        console.print(f"[red]❌ Integrity check failed: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@check.command()
@click.option(
    "--show-orphaned",
    is_flag=True,
    help="Show orphaned image records",
)
@click.option(
    "--show-broken",
    is_flag=True,
    help="Show images with broken file links",
)
def images(show_orphaned: bool = False, show_broken: bool = False):
    """Check image integrity and show detailed status"""
    db = SessionLocal()
    try:
        console.print("[bold blue]🖼️ Image integrity check...[/bold blue]\n")

        image_service = ImageService(db)

        # 检查数据库图片记录
        db_orphaned_images, db_broken_links = check_database_images_optimized(
            db, image_service, console=console
        )

        # 创建汇总表
        summary_table = Table(show_header=True, header_style="bold cyan")
        summary_table.add_column("Check", style="bold")
        summary_table.add_column("Status", justify="center")
        summary_table.add_column("Count", justify="right")
        summary_table.add_column("Details")

        # 总图片数
        total_images = db.query(Image).count()
        summary_table.add_row(
            "Total Images", "ℹ️", str(total_images), "Database records"
        )

        # 孤立图片记录
        summary_table.add_row(
            "Orphaned Records",
            "✅" if not db_orphaned_images else "⚠️",
            str(len(db_orphaned_images)),
            "Images without samples",
        )

        # 破损链接
        summary_table.add_row(
            "Broken Links",
            "✅" if not db_broken_links else "❌",
            str(len(db_broken_links)),
            "Files not found on disk",
        )

        console.print(
            Panel(summary_table, title="📊 Image Status Summary", border_style="blue")
        )

        # 显示详细信息
        if show_orphaned and db_orphaned_images:
            console.print(
                f"\n[bold yellow]🔍 Orphaned Image Records ({len(db_orphaned_images)}):[/bold yellow]"
            )
            orphaned_table = Table(show_header=True, header_style="bold yellow")
            orphaned_table.add_column("ID", justify="right", width=6)
            orphaned_table.add_column("Hash", width=12)
            orphaned_table.add_column("File Path", width=40)
            orphaned_table.add_column("Size", justify="right", width=10)

            for img in db_orphaned_images[:20]:  # 显示前20个
                orphaned_table.add_row(
                    str(img.id),
                    img.hash_value[:10] + "...",
                    img.file_path,
                    f"{img.file_size // 1024}KB" if img.file_size else "Unknown",
                )

            console.print(orphaned_table)

            if len(db_orphaned_images) > 20:
                console.print(f"[dim]... and {len(db_orphaned_images) - 20} more[/dim]")

        if show_broken and db_broken_links:
            console.print(
                f"\n[bold red]🔍 Broken File Links ({len(db_broken_links)}):[/bold red]"
            )
            broken_table = Table(show_header=True, header_style="bold red")
            broken_table.add_column("ID", justify="right", width=6)
            broken_table.add_column("Hash", width=12)
            broken_table.add_column("Missing File Path", width=40)
            broken_table.add_column("Size", justify="right", width=10)

            for img in db_broken_links[:20]:  # 显示前20个
                broken_table.add_row(
                    str(img.id),
                    img.hash_value[:10] + "...",
                    img.file_path,
                    f"{img.file_size // 1024}KB" if img.file_size else "Unknown",
                )

            console.print(broken_table)

            if len(db_broken_links) > 20:
                console.print(f"[dim]... and {len(db_broken_links) - 20} more[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Image check failed: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


def check_database_images(db, image_service) -> Tuple[List[Image], List[Image]]:
    """检查数据库中的图片记录，返回(孤立记录, 破损链接) - 优化版本"""
    return check_database_images_optimized(db, image_service)


def check_database_images_optimized(
    db, image_service, chunk_size: int = 1000, console=None
) -> Tuple[List[Image], List[Image]]:
    """
    优化版本的数据库图片检查，使用分块处理和并行文件检查

    Args:
        db: 数据库会话
        image_service: 图片服务
        chunk_size: 分块大小
        console: Rich Console对象

    Returns:
        Tuple[List[Image], List[Image]]: (孤立记录, 破损链接)
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed

    from rich.progress import (
        BarColumn,
        MofNCompleteColumn,
        Progress,
        SpinnerColumn,
        TextColumn,
        TimeRemainingColumn,
    )

    if console is None:
        from rich.console import Console

        console = Console()

    # 1. 使用优化的SQL查询查找孤立的图片记录
    orphaned_images = db.execute(
        text("""
        SELECT i.id, i.file_path, i.width, i.height, i.created_at
        FROM images i
        LEFT JOIN samples s ON i.id = s.image_id
        WHERE s.id IS NULL
    """)
    ).fetchall()

    # 直接构造Image对象，避免N+1查询
    orphaned_image_objects = []
    for row in orphaned_images:
        image = Image(
            id=row.id,
            file_path=row.file_path,
            width=row.width,
            height=row.height,
            created_at=row.created_at,
        )
        orphaned_image_objects.append(image)

    # 2. 分块检查破损的文件链接
    total_images = db.query(Image).count()
    broken_links = []

    if total_images == 0:
        return orphaned_image_objects, broken_links

    def check_image_file_exists(image_data):
        """检查单个图片文件是否存在"""
        image_id, file_path = image_data
        abs_path = settings.STORAGE_ROOT / file_path
        return image_id, file_path, abs_path.exists()

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        MofNCompleteColumn(),
        TimeRemainingColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("Checking image files...", total=total_images)

        # 分块处理图片
        offset = 0
        chunk_num = 0
        total_chunks = (total_images + chunk_size - 1) // chunk_size

        while offset < total_images:
            chunk_num += 1

            # 获取当前块的图片数据
            chunk_images = (
                db.query(Image.id, Image.file_path)
                .offset(offset)
                .limit(chunk_size)
                .all()
            )

            if not chunk_images:
                break

            # 并行检查文件存在性
            max_workers = min(settings.EXPORT_MAX_WORKERS, len(chunk_images))

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_image = {
                    executor.submit(
                        check_image_file_exists, (img.id, img.file_path)
                    ): img
                    for img in chunk_images
                }

                for future in as_completed(future_to_image):
                    image_data = future_to_image[future]
                    try:
                        image_id, file_path, exists = future.result()
                        if not exists:
                            # 创建Image对象用于返回
                            broken_image = (
                                db.query(Image).filter(Image.id == image_id).first()
                            )
                            if broken_image:
                                broken_links.append(broken_image)
                    except Exception as e:
                        console.print(
                            f"[yellow]Warning: Error checking image {image_data.id}: {e}[/yellow]"
                        )

            # 更新进度
            processed_count = min(offset + len(chunk_images), total_images)
            progress.update(
                task,
                completed=processed_count,
                description=f"Checking image files... (chunk {chunk_num}/{total_chunks})",
            )

            offset += chunk_size

    return orphaned_image_objects, broken_links


def check_storage_files(db) -> List[Path]:
    """检查存储目录中的物理文件，返回孤立文件列表 - 优化版本"""
    return check_storage_files_optimized(db)


def check_storage_files_optimized(
    db, chunk_size: int = 1000, console=None
) -> List[Path]:
    """
    优化版本的存储文件检查，使用分块处理和并行文件操作

    Args:
        db: 数据库会话
        chunk_size: 分块大小
        console: Rich Console对象

    Returns:
        List[Path]: 孤立文件列表
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed

    from rich.progress import (
        BarColumn,
        MofNCompleteColumn,
        Progress,
        SpinnerColumn,
        TextColumn,
        TimeRemainingColumn,
    )

    if console is None:
        from rich.console import Console

        console = Console()

    orphaned_files = []
    storage_root = settings.STORAGE_ROOT / "images"

    if not storage_root.exists():
        return orphaned_files

    # 1. 分块获取数据库中的文件路径，避免一次性加载所有数据
    db_file_paths = set()
    total_images = db.query(Image).count()

    if total_images > 0:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Loading database paths...", total=total_images)

            offset = 0
            chunk_num = 0
            total_chunks = (total_images + chunk_size - 1) // chunk_size

            while offset < total_images:
                chunk_num += 1

                # 分块获取文件路径
                chunk_paths = (
                    db.query(Image.file_path).offset(offset).limit(chunk_size).all()
                )

                if not chunk_paths:
                    break

                # 添加到路径集合
                for (file_path,) in chunk_paths:
                    db_file_paths.add(str(settings.STORAGE_ROOT / file_path))

                # 更新进度
                processed_count = min(offset + len(chunk_paths), total_images)
                progress.update(
                    task,
                    completed=processed_count,
                    description=f"Loading database paths... (chunk {chunk_num}/{total_chunks})",
                )

                offset += chunk_size

    # 2. 并行遍历存储目录
    def collect_files_in_directory(directory_path):
        """收集目录中的所有文件"""
        files = []
        try:
            for file_path in directory_path.rglob("*"):
                if file_path.is_file():
                    files.append(file_path)
        except Exception as e:
            console.print(
                f"[yellow]Warning: Error scanning directory {directory_path}: {e}[/yellow]"
            )
        return files

    # 获取所有文件
    all_files = collect_files_in_directory(storage_root)

    if not all_files:
        return orphaned_files

    # 3. 并行检查文件是否为孤立文件
    def check_file_orphaned(file_path):
        """检查文件是否为孤立文件"""
        return file_path, str(file_path) not in db_file_paths

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        MofNCompleteColumn(),
        TimeRemainingColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("Checking orphaned files...", total=len(all_files))

        max_workers = min(settings.EXPORT_MAX_WORKERS, len(all_files))

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(check_file_orphaned, file_path): file_path
                for file_path in all_files
            }

            completed_count = 0
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    checked_file, is_orphaned = future.result()
                    if is_orphaned:
                        orphaned_files.append(checked_file)
                except Exception as e:
                    console.print(
                        f"[yellow]Warning: Error checking file {file_path}: {e}[/yellow]"
                    )

                completed_count += 1
                progress.update(
                    task,
                    completed=completed_count,
                    description=f"Checking orphaned files... ({completed_count}/{len(all_files)})",
                )

    return orphaned_files


def check_batch_integrity(db) -> List[str]:
    """检查批次和样本完整性，返回问题列表 - 优化版本"""
    return check_batch_integrity_optimized(db)


def check_batch_integrity_optimized(
    db, chunk_size: int = 1000, console=None
) -> List[str]:
    """
    优化版本的批次完整性检查，使用分块处理和优化的SQL查询

    Args:
        db: 数据库会话
        chunk_size: 分块大小
        console: Rich Console对象

    Returns:
        List[str]: 问题列表
    """
    from rich.progress import (
        BarColumn,
        MofNCompleteColumn,
        Progress,
        SpinnerColumn,
        TextColumn,
        TimeRemainingColumn,
    )

    if console is None:
        from rich.console import Console

        console = Console()

    issues = []

    # 1. 检查空批次 - 使用优化的SQL查询

    empty_batches = db.execute(
        text("""
        SELECT b.id, b.name
        FROM batches b
        LEFT JOIN samples s ON b.id = s.batch_id
        WHERE s.id IS NULL
    """)
    ).fetchall()

    for row in empty_batches:
        issues.append(f"Empty batch: '{row.name}' (ID: {row.id})")

    # 2. 分块检查样本完整性
    total_samples = db.query(Sample).count()

    if total_samples == 0:
        return issues

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        MofNCompleteColumn(),
        TimeRemainingColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("Checking sample integrity...", total=total_samples)

        offset = 0
        chunk_num = 0
        total_chunks = (total_samples + chunk_size - 1) // chunk_size

        while offset < total_samples:
            chunk_num += 1

            # 使用JOIN查询避免N+1问题
            chunk_issues = db.execute(
                text("""
                SELECT
                    s.id as sample_id,
                    s.image_id,
                    s.batch_id,
                    CASE WHEN i.id IS NULL THEN 1 ELSE 0 END as missing_image,
                    CASE WHEN b.id IS NULL THEN 1 ELSE 0 END as missing_batch
                FROM samples s
                LEFT JOIN images i ON s.image_id = i.id
                LEFT JOIN batches b ON s.batch_id = b.id
                WHERE (i.id IS NULL OR b.id IS NULL)
                LIMIT :limit OFFSET :offset
            """),
                {"limit": chunk_size, "offset": offset},
            ).fetchall()

            # 处理当前块的问题
            for row in chunk_issues:
                if row.missing_image:
                    issues.append(
                        f"Sample {row.sample_id} references missing image (ID: {row.image_id})"
                    )
                if row.missing_batch:
                    issues.append(
                        f"Sample {row.sample_id} references missing batch (ID: {row.batch_id})"
                    )

            # 更新进度
            processed_count = min(offset + chunk_size, total_samples)
            progress.update(
                task,
                completed=processed_count,
                description=f"Checking sample integrity... (chunk {chunk_num}/{total_chunks})",
            )

            offset += chunk_size

    return issues


def fix_broken_links(db, broken_images: List[Image]) -> int:
    """尝试修复破损的文件链接"""
    fixed_count = 0

    # 这里可以实现更复杂的修复逻辑
    # 例如：搜索具有相同哈希值的文件，尝试重新链接等
    # 当前实现：删除破损记录

    for image in broken_images:
        try:
            # 检查是否有样本引用
            if not image.samples:
                db.delete(image)
                fixed_count += 1
        except Exception as _:
            continue

    if fixed_count > 0:
        db.commit()

    return fixed_count


def cleanup_orphaned_files(orphaned_files: List[Path]) -> int:
    """清理孤立的物理文件"""
    cleaned_count = 0

    for file_path in orphaned_files:
        try:
            # 安全删除：如果是软链接，只删除链接本身，不删除目标文件
            if file_path.is_symlink():
                # 对于软链接，使用unlink()只会删除链接本身
                console.print(f"   [dim]Removing symlink: {file_path}[/dim]")
                file_path.unlink()
            else:
                # 对于普通文件，正常删除
                console.print(f"   [dim]Removing file: {file_path}[/dim]")
                file_path.unlink()
            cleaned_count += 1
        except Exception as e:
            console.print(
                f"   [yellow]Warning: Failed to remove {file_path}: {e}[/yellow]"
            )
            continue

    return cleaned_count


def get_storage_statistics() -> Dict[str, int]:
    """获取存储使用统计 - 优化版本"""
    return get_storage_statistics_optimized()


def get_storage_statistics_optimized(console=None) -> Dict[str, int]:
    """
    优化版本的存储统计，使用并行文件处理

    Args:
        console: Rich Console对象

    Returns:
        Dict[str, int]: 存储统计信息
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed

    from rich.progress import (
        BarColumn,
        MofNCompleteColumn,
        Progress,
        SpinnerColumn,
        TextColumn,
        TimeRemainingColumn,
    )

    from ruyidv.database import SessionLocal

    if console is None:
        from rich.console import Console

        console = Console()

    db = SessionLocal()
    try:
        total_files = 0
        total_size = 0

        storage_root = settings.STORAGE_ROOT / "images"
        if not storage_root.exists():
            db_records = db.query(Image).count()
            return {
                "total_files": 0,
                "total_size": 0,
                "db_records": db_records,
            }

        # 1. 收集所有文件路径
        all_files = []
        for file_path in storage_root.rglob("*"):
            if file_path.is_file():
                all_files.append(file_path)

        if not all_files:
            db_records = db.query(Image).count()
            return {
                "total_files": 0,
                "total_size": 0,
                "db_records": db_records,
            }

        # 2. 并行获取文件统计信息
        def get_file_stats(file_path):
            """获取单个文件的统计信息"""
            try:
                stat_info = file_path.stat()
                return 1, stat_info.st_size
            except Exception:
                return 0, 0

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task(
                "Calculating storage statistics...", total=len(all_files)
            )

            max_workers = min(settings.EXPORT_MAX_WORKERS, len(all_files))

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_file = {
                    executor.submit(get_file_stats, file_path): file_path
                    for file_path in all_files
                }

                completed_count = 0
                for future in as_completed(future_to_file):
                    try:
                        file_count, file_size = future.result()
                        total_files += file_count
                        total_size += file_size
                    except Exception as e:
                        console.print(
                            f"[yellow]Warning: Error getting file stats: {e}[/yellow]"
                        )

                    completed_count += 1
                    progress.update(
                        task,
                        completed=completed_count,
                        description=f"Calculating storage statistics... ({completed_count}/{len(all_files)})",
                    )

        db_records = db.query(Image).count()

        return {
            "total_files": total_files,
            "total_size": total_size,
            "db_records": db_records,
        }
    finally:
        db.close()


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
