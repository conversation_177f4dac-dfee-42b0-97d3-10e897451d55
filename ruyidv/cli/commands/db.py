"""
Database management commands
"""

import shutil
import sqlite3
import subprocess
from datetime import datetime
from pathlib import Path

import click
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table
from sqlalchemy import text

from ruyidv.config import settings
from ruyidv.database import SessionLocal, engine

console = Console()


@click.group()
def db():
    """Database management commands"""
    pass


@db.command()
def migrate():
    """Run database migrations"""
    try:
        console.print("[blue]🔄 Running database migrations...[/blue]")

        # 检查是否存在alembic配置
        alembic_ini = Path("alembic.ini")
        if not alembic_ini.exists():
            console.print(
                "[red]❌ alembic.ini not found. Please run 'alembic init alembic' first.[/red]"
            )
            raise click.Abort()

        # 运行alembic upgrade
        result = subprocess.run(
            ["alembic", "upgrade", "head"], capture_output=True, text=True
        )

        if result.returncode == 0:
            console.print(
                Panel.fit(
                    "[green]✅ Database migrations completed successfully![/green]\n\n"
                    f"Migration output:\n{result.stdout}",
                    title="🎉 Migration Success",
                    border_style="green",
                )
            )
        else:
            console.print(
                Panel.fit(
                    f"[red]❌ Migration failed![/red]\n\n"
                    f"Error:\n{result.stderr}\n"
                    f"Output:\n{result.stdout}",
                    title="❌ Migration Error",
                    border_style="red",
                )
            )
            raise click.Abort()

    except FileNotFoundError:
        console.print(
            "[red]❌ Alembic not found. Please install alembic: pip install alembic[/red]"
        )
        raise click.Abort()
    except Exception as e:
        console.print(f"[red]❌ Migration failed: {e}[/red]")
        raise click.Abort()


@db.command()
@click.option("--force", is_flag=True, help="Force reset without confirmation")
def reset(force: bool):
    """Reset database (drop all tables and recreate)"""
    try:
        # 警告信息
        console.print(
            Panel(
                "[bold red]WARNING![/bold red] This will permanently delete:\n\n"
                "• All batches and samples\n"
                "• All imported data\n"
                "• Migration history\n\n"
                "[red]This action cannot be undone![/red]",
                title="⚠️ Database Reset",
                border_style="red",
            )
        )

        # 确认重置
        if not force and not Confirm.ask(
            "Are you sure you want to reset the database?"
        ):
            console.print("[yellow]Database reset cancelled.[/yellow]")
            return

        console.print("[blue]🔄 Resetting database...[/blue]")

        # 关闭所有连接
        engine.dispose()

        # 如果是SQLite，直接删除文件
        if "sqlite" in settings.DATABASE_URL:
            db_path = Path(settings.DATABASE_URL.replace("sqlite:///", ""))
            if db_path.exists():
                db_path.unlink()
                console.print(f"[yellow]Deleted database file: {db_path}[/yellow]")
        else:
            # 对于其他数据库类型，删除所有表
            from ruyidv.models import Base

            Base.metadata.drop_all(bind=engine)
            console.print("[yellow]Dropped all database tables[/yellow]")

        # 重新创建表结构
        from ruyidv.models import Base

        Base.metadata.create_all(bind=engine)

        # 重置alembic版本历史
        try:
            result = subprocess.run(
                ["alembic", "stamp", "head"], capture_output=True, text=True
            )
            if result.returncode == 0:
                console.print("[green]✅ Alembic version history reset[/green]")
        except FileNotFoundError:
            console.print(
                "[yellow]⚠️ Alembic not found, skipping version reset[/yellow]"
            )

        console.print(
            Panel.fit(
                "[green]✅ Database reset completed successfully![/green]\n\n"
                "Database has been reset to clean state.",
                title="🎉 Reset Complete",
                border_style="green",
            )
        )

    except Exception as e:
        console.print(f"[red]❌ Database reset failed: {e}[/red]")
        raise click.Abort()


@db.command()
@click.argument("backup_file", type=click.Path(path_type=Path))
@click.option("--compress", is_flag=True, help="Compress backup file")
def backup(backup_file: Path, compress: bool):
    """Backup database to file"""
    try:
        console.print("[blue]💾 Creating database backup...[/blue]")

        # 确保备份目录存在
        backup_file.parent.mkdir(parents=True, exist_ok=True)

        if "sqlite" in settings.DATABASE_URL:
            # SQLite备份
            db_path = Path(settings.DATABASE_URL.replace("sqlite:///", ""))

            if not db_path.exists():
                console.print("[red]❌ Database file not found[/red]")
                raise click.Abort()

            # 创建备份
            if compress:
                import gzip

                with open(db_path, "rb") as f_in:
                    with gzip.open(f"{backup_file}.gz", "wb") as f_out:
                        shutil.copyfileobj(f_in, f_out)
                final_backup = f"{backup_file}.gz"
            else:
                shutil.copy2(db_path, backup_file)
                final_backup = backup_file

            # 获取备份文件大小
            backup_size = Path(final_backup).stat().st_size

        else:
            # 其他数据库类型使用SQL dump
            console.print(
                "[yellow]⚠️ SQL dump backup not implemented for non-SQLite databases[/yellow]"
            )
            raise click.Abort()

        # 验证备份
        backup_valid = True
        try:
            if compress:
                import gzip

                with gzip.open(final_backup, "rb") as f:
                    # 简单验证：读取前几个字节
                    header = f.read(16)
                    backup_valid = len(header) > 0
            else:
                # 尝试连接备份的数据库
                test_conn = sqlite3.connect(final_backup)
                test_conn.execute("SELECT count(*) FROM sqlite_master")
                test_conn.close()
        except Exception:
            backup_valid = False

        # 显示结果
        status_table = Table(show_header=False, box=None, padding=(0, 2))
        status_table.add_column("Field", style="bold cyan", width=20)
        status_table.add_column("Value", width=40)

        status_table.add_row("Backup File", str(final_backup))
        status_table.add_row("File Size", f"{backup_size:,} bytes")
        status_table.add_row("Compressed", "Yes" if compress else "No")
        status_table.add_row("Status", "✅ Valid" if backup_valid else "❌ Invalid")
        status_table.add_row("Created", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        console.print(
            Panel(
                status_table,
                title="💾 Backup Complete" if backup_valid else "❌ Backup Failed",
                border_style="green" if backup_valid else "red",
            )
        )

        if not backup_valid:
            console.print("[red]❌ Backup verification failed[/red]")
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]❌ Backup failed: {e}[/red]")
        raise click.Abort()


@db.command()
def status():
    """Show database status and information"""
    try:
        console.print("[blue]📊 Checking database status...[/blue]")
        connection_error = None

        # 基本连接测试
        try:
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            db_connected = True
        except Exception as e:
            db_connected = False
            connection_error = str(e)

        # 获取数据库信息
        db_info = {}
        if db_connected:
            db = SessionLocal()
            try:
                # 检查表是否存在
                with engine.connect() as conn:
                    if "sqlite" in settings.DATABASE_URL:
                        result = conn.execute(
                            text("SELECT name FROM sqlite_master WHERE type='table'")
                        )
                        tables = [row[0] for row in result]
                    else:
                        # 对于其他数据库类型
                        result = conn.execute(
                            text(
                                "SELECT table_name FROM information_schema.tables "
                                "WHERE table_schema = 'public'"
                            )
                        )
                        tables = [row[0] for row in result]

                db_info["tables"] = tables

                # 获取记录数量
                if "batches" in tables:
                    result = db.execute(text("SELECT COUNT(*) FROM batches"))
                    db_info["batch_count"] = result.scalar()

                if "samples" in tables:
                    result = db.execute(text("SELECT COUNT(*) FROM samples"))
                    db_info["sample_count"] = result.scalar()

                if "images" in tables:
                    result = db.execute(text("SELECT COUNT(*) FROM images"))
                    db_info["image_count"] = result.scalar()

            finally:
                db.close()

        # 状态表格
        status_table = Table(show_header=True, header_style="bold cyan")
        status_table.add_column("Component", style="bold", width=25)
        status_table.add_column("Status", justify="center", width=12)
        status_table.add_column("Details", width=40)

        # 数据库连接状态
        if db_connected:
            status_table.add_row(
                "Database Connection",
                "✅ Online",
                f"Connected to {settings.DATABASE_URL}",
            )
        else:
            status_table.add_row(
                "Database Connection",
                "❌ Offline",
                f"Error: {connection_error or 'Unknown'}",
            )

        # 数据库文件状态（仅SQLite）
        if "sqlite" in settings.DATABASE_URL:
            db_path = Path(settings.DATABASE_URL.replace("sqlite:///", ""))
            if db_path.exists():
                file_size = db_path.stat().st_size
                status_table.add_row(
                    "Database File", "✅ Exists", f"Size: {file_size:,} bytes"
                )
            else:
                status_table.add_row(
                    "Database File", "❌ Missing", "Database file not found"
                )

        # 表状态
        if db_connected and db_info.get("tables"):
            table_status = f"{len(db_info['tables'])} tables found"
            expected_tables = {"batches", "samples", "images"}
            missing_tables = expected_tables - set(db_info["tables"])
            if missing_tables:
                table_status += f" (missing: {', '.join(missing_tables)})"
                status_icon = "⚠️ Partial"
            else:
                status_icon = "✅ Complete"

            status_table.add_row("Database Schema", status_icon, table_status)

        # 数据统计
        if db_connected and db_info:
            data_summary = []
            if "batch_count" in db_info:
                data_summary.append(f"Batches: {db_info['batch_count']}")
            if "sample_count" in db_info:
                data_summary.append(f"Samples: {db_info['sample_count']}")
            if "image_count" in db_info:
                data_summary.append(f"Images: {db_info['image_count']}")

            if data_summary:
                status_table.add_row(
                    "Data Records", "📊 Info", " | ".join(data_summary)
                )

        # Alembic状态
        try:
            result = subprocess.run(
                ["alembic", "current"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                current_rev = result.stdout.strip()
                status_table.add_row(
                    "Migration Status",
                    "✅ OK",
                    f"Current: {current_rev}" if current_rev else "No migrations",
                )
            else:
                status_table.add_row(
                    "Migration Status", "❌ Error", "Failed to get migration status"
                )
        except (FileNotFoundError, subprocess.TimeoutExpired):
            status_table.add_row("Migration Status", "⚠️ N/A", "Alembic not available")

        console.print(
            Panel(status_table, title="📊 Database Status", border_style="blue")
        )

        # 存储状态
        storage_table = Table(show_header=True, header_style="bold yellow")
        storage_table.add_column("Storage Path", style="bold", width=25)
        storage_table.add_column("Status", justify="center", width=12)
        storage_table.add_column("Details", width=40)

        # 检查存储目录
        storage_paths = [
            ("Root Directory", settings.STORAGE_ROOT),
            ("Images Directory", settings.STORAGE_ROOT / "images"),
            ("Exports Directory", settings.STORAGE_ROOT / "exports"),
            ("Imports Directory", settings.STORAGE_ROOT / "imports"),
        ]

        for name, path in storage_paths:
            if path.exists():
                if path.is_dir():
                    file_count = len(list(path.glob("*"))) if path.is_dir() else 0
                    storage_table.add_row(name, "✅ OK", f"{path} ({file_count} items)")
                else:
                    storage_table.add_row(name, "❌ Error", f"{path} (not a directory)")
            else:
                storage_table.add_row(name, "⚠️ Missing", f"{path} (not found)")

        console.print(
            Panel(storage_table, title="📁 Storage Status", border_style="yellow")
        )

    except Exception as e:
        console.print(f"[red]❌ Failed to check database status: {e}[/red]")
        raise click.Abort()


@db.command()
@click.argument("backup_file", type=click.Path(exists=True, path_type=Path))
@click.option("--force", is_flag=True, help="Force restore without confirmation")
def restore(backup_file: Path, force: bool):
    """Restore database from backup file"""
    restore_error = None
    try:
        console.print("[blue]📥 Restoring database from backup...[/blue]")

        # 警告信息
        if not force:
            console.print(
                Panel(
                    "[bold red]WARNING![/bold red] This will replace the current database with the backup.\n\n"
                    "[red]All current data will be lost![/red]",
                    title="⚠️ Restore Database",
                    border_style="red",
                )
            )

            if not Confirm.ask("Are you sure you want to restore from backup?"):
                console.print("[yellow]Restore cancelled.[/yellow]")
                return

        # 检查备份文件
        if not backup_file.exists():
            console.print(f"[red]❌ Backup file not found: {backup_file}[/red]")
            raise click.Abort()

        # 检测是否压缩文件
        is_compressed = backup_file.suffix == ".gz"

        if "sqlite" in settings.DATABASE_URL:
            db_path = Path(settings.DATABASE_URL.replace("sqlite:///", ""))

            # 关闭数据库连接
            engine.dispose()

            # 备份当前数据库（如果存在）
            if db_path.exists():
                backup_current = db_path.with_suffix(
                    f".backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                )
                shutil.copy2(db_path, backup_current)
                console.print(
                    f"[yellow]Current database backed up to: {backup_current}[/yellow]"
                )

            # 恢复数据库
            if is_compressed:
                import gzip

                with gzip.open(backup_file, "rb") as f_in:
                    with open(db_path, "wb") as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                shutil.copy2(backup_file, db_path)

            # 验证恢复的数据库
            try:
                test_conn = sqlite3.connect(db_path)
                test_conn.execute("SELECT count(*) FROM sqlite_master")
                test_conn.close()
                restore_valid = True
            except Exception as e:
                restore_valid = False
                restore_error = str(e)

            if restore_valid:
                console.print(
                    Panel.fit(
                        f"[green]✅ Database restored successfully![/green]\n\n"
                        f"Restored from: {backup_file}",
                        title="🎉 Restore Complete",
                        border_style="green",
                    )
                )
            else:
                console.print(
                    Panel.fit(
                        f"[red]❌ Database restore failed![/red]\n\n"
                        f"Error: {restore_error or 'Unknown'}",
                        title="❌ Restore Error",
                        border_style="red",
                    )
                )
                raise click.Abort()
        else:
            console.print(
                "[red]❌ Restore not implemented for non-SQLite databases[/red]"
            )
            raise click.Abort()

    except Exception as e:
        console.print(f"[red]❌ Restore failed: {e}[/red]")
        raise click.Abort()
