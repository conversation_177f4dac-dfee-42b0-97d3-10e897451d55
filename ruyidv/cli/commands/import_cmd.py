"""
Data import commands
"""

from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, TextColumn
from rich.table import Table

from ruyidv.database import SessionLocal
from ruyidv.services import BatchService, ImportService

console = Console()


@click.group(name="import")
def import_cmd():
    """Data import commands"""
    pass


@import_cmd.command(name="json")
@click.argument("json_file", type=click.Path(exists=True, path_type=Path))
@click.option(
    "--image-base-path",
    "-p",
    type=click.Path(exists=True, path_type=Path),
    help="Base path for images (default: JSON file directory)",
)
@click.option("--dry-run", is_flag=True, help="Validate only, don't actually import")
@click.option(
    "--copy-images",
    is_flag=True,
    help="Copy image files to storage instead of creating soft links",
)
@click.option(
    "--force-mapreduce",
    is_flag=True,
    help="Force use of Map-Reduce pipeline regardless of dataset size",
)
@click.option(
    "--chunk-size",
    type=int,
    help="Chunk size for Map-Reduce processing (default: from config)",
)
@click.option(
    "--verbose-duplicates",
    is_flag=True,
    help="Show detailed information about duplicate images",
)
def import_json(
    json_file: Path,
    image_base_path: Optional[Path] = None,
    dry_run: bool = False,
    copy_images: bool = False,
    force_mapreduce: bool = False,
    chunk_size: Optional[int] = None,
    verbose_duplicates: bool = False,
):
    """Import data from JSON file"""
    db = SessionLocal()
    try:
        import_service = ImportService(db)
        batch_service = BatchService(db)

        console.print(f"[blue]📂 Processing JSON file: {json_file}[/blue]")

        # 解析JSON文件
        try:
            data = import_service.parse_import_json(json_file)
        except ValueError as e:
            console.print(f"[red]❌ Failed to parse JSON file: {e}[/red]")
            raise click.Abort()

        # 验证数据格式
        if not import_service.validate_import_data(data):
            console.print("[red]❌ Invalid JSON data format[/red]")
            console.print("\n[yellow]Expected format:[/yellow]")
            console.print("""
{
    "batch_name": "Batch Name",
    "batch_description": "Optional description",
    "samples": [
        {
            "image_path": "relative/path/to/image.jpg",
            "mode": "grounding|describe|enumerate_text|enumerate_coord|checklist|ensure",
            "metadata": {...},
            "labels": ["label1", "label2"]
        }
    ]
}
            """)
            raise click.Abort()

        # 检查批次名称是否已存在
        batch_name = data["batch_name"]
        existing_batch = batch_service.get_batch_by_name(batch_name)
        if existing_batch:
            console.print(
                f"[red]❌ Batch with name '{batch_name}' already exists (ID: {existing_batch.id})[/red]"
            )
            raise click.Abort()

        # 设置图片基础路径
        if image_base_path is None:
            image_base_path = json_file.parent

        console.print(f"[blue]📁 Image base path: {image_base_path}[/blue]")

        # 显示导入预览
        samples_data = data["samples"]
        console.print("\n[bold]Import Preview:[/bold]")
        console.print(f"[bold]Batch Name:[/bold] {batch_name}")
        console.print(
            f"[bold]Description:[/bold] {data.get('batch_description', 'None')}"
        )
        console.print(f"[bold]Total Samples:[/bold] {len(samples_data)}")

        # 统计模式分布
        mode_counts = {}
        for sample in samples_data:
            mode = sample["mode"]
            mode_counts[mode] = mode_counts.get(mode, 0) + 1

        mode_table = Table(show_header=True, header_style="bold cyan")
        mode_table.add_column("Mode", style="bold")
        mode_table.add_column("Count", justify="right")

        for mode, count in mode_counts.items():
            mode_table.add_row(mode.replace("_", " ").title(), str(count))

        console.print(
            Panel(mode_table, title="🎯 Mode Distribution", border_style="cyan")
        )

        # 检查前几个图片路径
        console.print("\n[bold]Sample Image Paths:[/bold]")
        for i, sample in enumerate(samples_data[:5]):
            image_path = image_base_path / sample["image_path"]
            status = "✓" if image_path.exists() else "❌"
            console.print(f"  {status} {sample['image_path']}")

        if len(samples_data) > 5:
            console.print(f"  ... and {len(samples_data) - 5} more images")

        if dry_run:
            console.print(
                Panel.fit(
                    "[green]✓[/green] Validation passed! Use without --dry-run to import.",
                    title="🔍 Dry Run Complete",
                    border_style="green",
                )
            )
            return

        # 确认导入
        if not click.confirm(f"\nProceed with importing {len(samples_data)} samples?"):
            console.print("[yellow]Import cancelled.[/yellow]")
            return

        # 执行导入
        from rich.progress import BarColumn, MofNCompleteColumn, TimeRemainingColumn

        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            # 创建三个进度条：图片文件处理、图片记录创建、样本创建
            image_file_task = progress.add_task(
                "Processing image files...", total=len(samples_data)
            )
            image_db_task = progress.add_task(
                "Creating image records...", total=len(samples_data), visible=False
            )
            sample_task = progress.add_task(
                "Creating samples...", total=len(samples_data), visible=False
            )

            def update_image_progress(current: int, total: int):
                # 检查是否进入数据库操作阶段
                if current > len(samples_data):
                    # 数据库操作阶段
                    if not progress.tasks[image_db_task].visible:
                        # 首次进入数据库阶段，显示数据库进度条并完成文件进度条
                        progress.update(
                            image_file_task,
                            completed=len(samples_data),
                            description="Image files processed!",
                        )
                        progress.update(image_db_task, visible=True)

                    db_current = current - len(samples_data)
                    progress.update(
                        image_db_task,
                        completed=db_current,
                        description="Creating image records...",
                    )
                else:
                    # 文件处理阶段
                    progress.update(
                        image_file_task,
                        completed=current,
                        description="Processing image files...",
                    )

            def update_sample_progress(current: int, total: int):
                if not progress.tasks[sample_task].visible:
                    # 首次进入样本创建阶段，显示样本进度条并完成图片记录进度条
                    progress.update(
                        image_db_task,
                        completed=progress.tasks[image_db_task].total,
                        description="Image records created!",
                    )
                    progress.update(sample_task, visible=True)

                progress.update(
                    sample_task,
                    completed=current,
                    description="Creating samples...",
                )

            try:
                # Convert copy_images flag to use_symlinks parameter (inverted logic)
                use_symlinks = not copy_images

                # Show storage mode
                storage_mode = "copying files" if copy_images else "creating soft links"
                console.print(f"[blue]📁 Storage mode: {storage_mode}[/blue]")

                # Choose import method based on dataset size and user preferences
                from ruyidv.config import settings

                # Determine if we should use map-reduce
                use_mapreduce = (
                    force_mapreduce
                    or len(samples_data) > settings.IMPORT_MAPREDUCE_THRESHOLD
                )

                # Determine chunk size
                effective_chunk_size = (
                    chunk_size
                    if chunk_size is not None
                    else settings.IMPORT_MAPREDUCE_CHUNK_SIZE
                )

                if use_mapreduce:
                    if force_mapreduce:
                        console.print(
                            f"[blue]🚀 Map-Reduce mode forced by user ({len(samples_data)} samples)[/blue]"
                        )
                    else:
                        console.print(
                            f"[blue]🚀 Large dataset detected ({len(samples_data)} samples). "
                            f"Using Map-Reduce pipeline for optimal performance[/blue]"
                        )
                    console.print(
                        f"[blue]📦 Processing in chunks of {effective_chunk_size} samples[/blue]"
                    )

                    if verbose_duplicates:
                        console.print(
                            "[blue]🔍 Verbose duplicate reporting enabled[/blue]"
                        )

                    batch, samples, errors = (
                        import_service.import_batch_from_json_mapreduce(
                            json_file,
                            image_base_path,
                            update_image_progress,
                            update_sample_progress,
                            use_symlinks,
                            chunk_size=effective_chunk_size,
                        )
                    )
                else:
                    # Show threading info for smaller imports
                    if len(samples_data) > 5:
                        from ruyidv.config import settings

                        max_workers = min(
                            settings.IMPORT_MAX_WORKERS, len(samples_data)
                        )
                        console.print(
                            f"[blue]🧵 Using {max_workers} worker threads for image processing[/blue]"
                        )

                    batch, samples, errors = import_service.import_batch_from_json(
                        json_file,
                        image_base_path,
                        update_image_progress,
                        update_sample_progress,
                        use_symlinks,
                    )

                # 完成所有进度条
                progress.update(
                    image_file_task,
                    completed=len(samples_data),
                    description="Image files processed!",
                )
                progress.update(
                    image_db_task,
                    completed=len(samples_data),
                    description="Image records created!",
                    visible=True,
                )
                progress.update(
                    sample_task,
                    completed=len(samples),
                    description="Samples created!",
                    visible=True,
                )

            except ValueError as e:
                console.print(f"\n[red]❌ Import failed: {e}[/red]")
                raise click.Abort()

        # 显示导入结果
        summary = import_service.get_import_summary(batch, samples, errors)

        # 成功信息
        console.print(
            Panel.fit(
                f"[green]✓[/green] Import completed successfully!\n\n"
                f"[bold]Batch ID:[/bold] {summary['batch_id']}\n"
                f"[bold]Batch Name:[/bold] {summary['batch_name']}\n"
                f"[bold]Total Samples:[/bold] {summary['total_samples']}\n"
                f"[bold]Success Rate:[/bold] {((len(samples) / (len(samples) + len(errors))) * 100) if (len(samples) + len(errors)) > 0 else 100:.1f}%",
                title="🎉 Import Complete",
                border_style="green",
            )
        )

        # 模式分布
        if summary["mode_distribution"]:
            result_table = Table(show_header=True, header_style="bold yellow")
            result_table.add_column("Mode", style="bold")
            result_table.add_column("Count", justify="right")

            for mode, count in summary["mode_distribution"].items():
                result_table.add_row(mode.replace("_", " ").title(), str(count))

            console.print(
                Panel(result_table, title="📊 Imported Samples", border_style="yellow")
            )

        # 错误信息
        if errors:
            console.print(
                f"\n[yellow]⚠️ {len(errors)} warnings/errors occurred:[/yellow]"
            )
            for i, error in enumerate(errors[:10], 1):
                console.print(f"  {i}. {error}")
            if len(errors) > 10:
                console.print(f"  ... and {len(errors) - 10} more errors")

    except Exception as e:
        console.print(f"[red]❌ Unexpected error during import: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@import_cmd.command()
@click.argument("json_file", type=click.Path(exists=True, path_type=Path))
@click.option(
    "--image-base-path",
    "-p",
    type=click.Path(exists=True, path_type=Path),
    help="Base path for images (default: JSON file directory)",
)
def validate(json_file: Path, image_base_path: Optional[Path] = None):
    """Validate import file format without importing"""
    db = SessionLocal()
    try:
        import_service = ImportService(db)

        console.print(f"[blue]🔍 Validating JSON file: {json_file}[/blue]")

        # 解析JSON文件
        try:
            data = import_service.parse_import_json(json_file)
        except ValueError as e:
            console.print(f"[red]❌ JSON parsing failed: {e}[/red]")
            raise click.Abort()

        # 验证基本格式
        is_valid, validation_errors = import_service.validate_import_data(data)
        if not is_valid:
            console.print("[red]❌ JSON数据格式无效[/red]")
            console.print(f"[red]发现 {len(validation_errors)} 个格式错误:[/red]")
            for i, error in enumerate(validation_errors[:15], 1):
                console.print(f"  {i}. {error}")
            if len(validation_errors) > 15:
                console.print(f"  ... 还有 {len(validation_errors) - 15} 个错误")
            raise click.Abort()

        # 详细验证
        batch_name = data["batch_name"]
        samples_data = data["samples"]

        console.print("[green]✓[/green] JSON基本格式有效")
        console.print(f"[green]✓[/green] 批次名称: {batch_name}")
        console.print(f"[green]✓[/green] 样本总数: {len(samples_data)}")

        # 如果基本验证有警告，也显示出来
        if validation_errors:
            console.print(
                f"[yellow]⚠️ 发现 {len(validation_errors)} 个格式警告:[/yellow]"
            )
            for i, error in enumerate(validation_errors[:5], 1):
                console.print(f"  {i}. {error}")
            if len(validation_errors) > 5:
                console.print(f"  ... 还有 {len(validation_errors) - 5} 个警告")

        # 统计模式分布（此逻辑已包含在详细验证中，但我们仍然显示统计）
        mode_counts = {}
        for sample in samples_data:
            if "mode" in sample and isinstance(sample["mode"], str):
                mode = sample["mode"]
                mode_counts[mode] = mode_counts.get(mode, 0) + 1

        if mode_counts:
            console.print("\n[blue]📊 模式分布:[/blue]")
            for mode, count in mode_counts.items():
                console.print(f"  {mode}: {count} 个样本")

        # 验证图片文件
        if image_base_path is None:
            image_base_path = json_file.parent

        console.print(f"\n[blue]📁 Checking images in: {image_base_path}[/blue]")

        missing_images = []
        existing_images = 0

        for sample in samples_data:
            image_path = image_base_path / sample["image_path"]
            if image_path.exists():
                existing_images += 1
            else:
                missing_images.append(sample["image_path"])

        if missing_images:
            console.print(f"[red]❌ Found {len(missing_images)} missing images:[/red]")
            for img_path in missing_images[:10]:
                console.print(f"  {img_path}")
            if len(missing_images) > 10:
                console.print(f"  ... and {len(missing_images) - 10} more")
        else:
            console.print(f"[green]✓[/green] All {existing_images} images found")

        # 显示统计结果
        result_table = Table(show_header=True, header_style="bold magenta")
        result_table.add_column("Check", style="bold")
        result_table.add_column("Status", justify="center")
        result_table.add_column("Details")

        result_table.add_row("JSON Format", "✓" if is_valid else "❌", "基本格式检查")
        result_table.add_row(
            "Format Details",
            "✓" if not validation_errors else "❌",
            f"{len(validation_errors)} 个问题" if validation_errors else "所有格式正确",
        )
        result_table.add_row(
            "Image Files",
            "✓" if not missing_images else "❌",
            f"{len(missing_images)} 个文件缺失" if missing_images else "所有文件存在",
        )

        console.print(Panel(result_table, title="📋 验证结果", border_style="blue"))

        # 最终结果
        all_valid = is_valid and not validation_errors and not missing_images
        if all_valid:
            console.print(
                Panel.fit(
                    "[green]✅ 所有验证通过！文件可以导入。[/green]",
                    title="🎉 验证成功",
                    border_style="green",
                )
            )
        else:
            console.print(
                Panel.fit(
                    "[yellow]⚠️ 验证完成，但发现问题。请修复上述问题后重试。[/yellow]",
                    title="⚠️ 验证问题",
                    border_style="yellow",
                )
            )

    except Exception as e:
        console.print(f"[red]❌ Validation failed: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@import_cmd.command()
def status():
    """Show import status and recent imports"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)

        # 获取最近的批次
        recent_batches = batch_service.get_all_batches(limit=10)

        if not recent_batches:
            console.print("[yellow]No import batches found.[/yellow]")
            return

        console.print("[bold]📊 Recent Import Batches[/bold]\n")

        # 创建表格
        table = Table(show_header=True, header_style="bold cyan")
        table.add_column("ID", justify="right", width=6)
        table.add_column("Name", min_width=25)
        table.add_column("Samples", justify="right", width=10)
        table.add_column("Images", justify="right", width=8)
        table.add_column("Created", width=16)
        table.add_column("Status", width=10)

        for batch in recent_batches:
            stats = batch_service.get_batch_statistics(batch.id)

            status = "✅ Complete"
            if stats and stats["total_samples"] == 0:
                status = "⚠️ Empty"

            table.add_row(
                str(batch.id),
                batch.name,
                str(stats["total_samples"]) if stats else "0",
                str(stats["unique_images"]) if stats else "0",
                batch.created_at.strftime("%m-%d %H:%M"),
                status,
            )

        console.print(Panel(table, title="📦 Import History", border_style="blue"))

        # 总体统计
        total_batches = batch_service.count_batches()
        console.print(f"\n[dim]Total batches: {total_batches}[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Failed to get import status: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()
