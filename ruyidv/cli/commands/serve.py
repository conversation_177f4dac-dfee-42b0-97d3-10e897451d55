"""
API Server commands
"""

import click
import uvicorn
from rich.console import Console

from ruyidv.config import settings

console = Console()


@click.command()
@click.option("--host", default=None, help="Host to bind")
@click.option("--port", type=int, default=None, help="Port to bind")
@click.option("--reload", is_flag=True, help="Enable auto-reload (development mode)")
@click.option(
    "--log-level",
    default="info",
    type=click.Choice(["critical", "error", "warning", "info", "debug", "trace"]),
    help="Log level",
)
def serve(host: str, port: int, reload: bool, log_level: str):
    """Start the API server"""
    try:
        # Settings class now automatically loads from YAML and environment variables
        # 获取配置值，优先级：命令行参数 > 环境变量 > YAML配置 > 默认值
        actual_host = host or settings.HOST
        actual_port = port or settings.PORT
        debug_mode = settings.DEBUG

        console.print("[blue]🚀 Starting Ruyi Dataverse API Server...[/blue]")
        console.print(f"[dim]Host: {actual_host}[/dim]")
        console.print(f"[dim]Port: {actual_port}[/dim]")
        console.print(f"[dim]Debug mode: {debug_mode or reload}[/dim]")
        console.print(f"[dim]API docs: http://{actual_host}:{actual_port}/docs[/dim]")

        if host or port:
            console.print("[dim]Note: Using command line overrides[/dim]")
        elif actual_port != 8000:  # Default port
            console.print(
                f"[dim]Note: Using port {actual_port} from configuration[/dim]"
            )

        # 启动uvicorn服务器
        uvicorn.run(
            "ruyidv.api:app",
            host=actual_host,
            port=actual_port,
            reload=reload or debug_mode,
            log_level=log_level,
            access_log=True,
        )

    except KeyboardInterrupt:
        console.print("\n[yellow]🛑 Server stopped by user[/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Failed to start server: {e}[/red]")
        raise click.Abort()
