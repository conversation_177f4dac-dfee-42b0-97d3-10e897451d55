"""
Statistics commands
"""

from collections import Counter

import click
from rich.console import Console
from rich.panel import Panel
from rich.progress import track
from rich.table import Table

from ruyidv.database import SessionLocal
from ruyidv.models import SampleMode
from ruyidv.services import BatchService, SampleService

console = Console()


@click.group()
def stats():
    """Statistics and analytics commands"""
    pass


@stats.command()
def overview():
    """Show overall system statistics"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)
        sample_service = SampleService(db)

        console.print("[blue]📊 Generating system overview...[/blue]")

        # 基础统计
        total_batches = batch_service.count_batches()

        # 使用高效的统计方法
        system_stats = sample_service.get_system_statistics()
        total_samples = system_stats["total_samples"]
        unique_images = system_stats["unique_images"]
        mode_counts = Counter(system_stats["mode_distribution"])

        # 高效获取所有标签
        all_labels = sample_service.get_all_labels_efficiently()
        unique_labels = len(set(all_labels))

        # 创建概览表格
        overview_table = Table(show_header=True, header_style="bold cyan")
        overview_table.add_column("Metric", style="bold", width=25)
        overview_table.add_column("Count", justify="right", width=15)
        overview_table.add_column("Description", width=40)

        overview_table.add_row(
            "Total Batches", str(total_batches), "Number of data batches"
        )
        overview_table.add_row(
            "Total Samples", str(total_samples), "Number of training samples"
        )
        overview_table.add_row(
            "Unique Images", str(unique_images), "Number of unique images"
        )
        overview_table.add_row(
            "Unique Labels", str(unique_labels), "Number of unique labels"
        )

        # 计算一些比率
        if unique_images > 0:
            samples_per_image = total_samples / unique_images
            overview_table.add_row(
                "Samples/Image", f"{samples_per_image:.1f}", "Average samples per image"
            )

        if total_batches > 0:
            samples_per_batch = total_samples / total_batches
            overview_table.add_row(
                "Samples/Batch", f"{samples_per_batch:.1f}", "Average samples per batch"
            )

        console.print(
            Panel(overview_table, title="📈 System Overview", border_style="blue")
        )

        # 模式分布
        if mode_counts:
            mode_table = Table(show_header=True, header_style="bold yellow")
            mode_table.add_column("Training Mode", style="bold", width=20)
            mode_table.add_column("Count", justify="right", width=10)
            mode_table.add_column("Percentage", justify="right", width=12)
            mode_table.add_column("Description", width=35)

            mode_descriptions = {
                "grounding": "Description → Coordinate mapping",
                "describe": "Coordinate → Description mapping",
                "enumerate_text": "Element text enumeration",
                "enumerate_coord": "Element coordinate enumeration",
                "checklist": "Boolean checklist validation",
                "ensure": "Target state achievement",
            }

            for mode, count in mode_counts.most_common():
                percentage = (count / total_samples * 100) if total_samples > 0 else 0
                mode_table.add_row(
                    mode.replace("_", " ").title(),
                    str(count),
                    f"{percentage:.1f}%",
                    mode_descriptions.get(mode, ""),
                )

            console.print(
                Panel(mode_table, title="🎯 Mode Distribution", border_style="yellow")
            )

        # 最热门标签
        if all_labels:
            label_counts = Counter(all_labels)
            top_labels = label_counts.most_common(10)

            label_table = Table(show_header=True, header_style="bold green")
            label_table.add_column("Rank", justify="right", width=6)
            label_table.add_column("Label", style="bold", width=20)
            label_table.add_column("Count", justify="right", width=10)
            label_table.add_column("Percentage", justify="right", width=12)

            for i, (label, count) in enumerate(top_labels, 1):
                percentage = count / len(all_labels) * 100
                label_table.add_row(str(i), label, str(count), f"{percentage:.1f}%")

            console.print(
                Panel(label_table, title="🏷️ Top 10 Labels", border_style="green")
            )

        # 最近活跃的批次
        recent_batches = batch_service.get_all_batches(limit=5)
        if recent_batches:
            recent_table = Table(show_header=True, header_style="bold magenta")
            recent_table.add_column("ID", justify="right", width=6)
            recent_table.add_column("Name", width=25)
            recent_table.add_column("Samples", justify="right", width=10)
            recent_table.add_column("Created", width=16)

            for batch in recent_batches:
                stats = batch_service.get_batch_statistics(batch.id)
                sample_count = stats["total_samples"] if stats else 0

                recent_table.add_row(
                    str(batch.id),
                    batch.name[:23] + "..." if len(batch.name) > 25 else batch.name,
                    str(sample_count),
                    batch.created_at.strftime("%m-%d %H:%M"),
                )

            console.print(
                Panel(recent_table, title="📦 Recent Batches", border_style="magenta")
            )

    except Exception as e:
        console.print(f"[red]❌ Failed to generate overview: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@stats.command()
@click.argument("batch_id", type=int)
def batch(batch_id: int):
    """Show detailed statistics for a specific batch"""
    db = SessionLocal()
    try:
        batch_service = BatchService(db)
        sample_service = SampleService(db)

        # 检查批次是否存在
        batch_obj = batch_service.get_batch_by_id(batch_id)
        if not batch_obj:
            console.print(f"[red]❌ Batch with ID {batch_id} not found[/red]")
            raise click.Abort()

        console.print(f"[blue]📊 Analyzing batch {batch_id}...[/blue]")

        # 获取批次统计信息
        stats = batch_service.get_batch_statistics(batch_id)
        if not stats:
            console.print(
                f"[red]❌ Unable to get statistics for batch {batch_id}[/red]"
            )
            raise click.Abort()

        # 基本信息
        basic_info = Table(show_header=False, box=None, padding=(0, 2))
        basic_info.add_column("Field", style="bold cyan", width=20)
        basic_info.add_column("Value", width=40)

        basic_info.add_row("Batch ID", str(batch_id))
        basic_info.add_row("Batch Name", stats["batch_name"])
        basic_info.add_row("Total Samples", str(stats["total_samples"]))
        basic_info.add_row("Unique Images", str(stats["unique_images"]))
        basic_info.add_row("Unique Labels", str(len(stats["all_labels"])))
        basic_info.add_row("Created", stats["created_at"].strftime("%Y-%m-%d %H:%M:%S"))

        console.print(
            Panel(basic_info, title="📋 Batch Information", border_style="blue")
        )

        # Extract common variables used throughout the function
        total_samples = stats["total_samples"]
        unique_images = stats["unique_images"]
        batch_samples = sample_service.get_samples_by_batch(batch_id)

        # 模式分布详细统计
        if stats["mode_distribution"]:
            mode_table = Table(show_header=True, header_style="bold yellow")
            mode_table.add_column("Training Mode", style="bold", width=20)
            mode_table.add_column("Sample Count", justify="right", width=12)
            mode_table.add_column("Percentage", justify="right", width=12)
            mode_table.add_column("Avg per Image", justify="right", width=12)

            for mode, count in stats["mode_distribution"].items():
                percentage = (count / total_samples * 100) if total_samples > 0 else 0
                avg_per_image = (count / unique_images) if unique_images > 0 else 0

                mode_table.add_row(
                    mode.replace("_", " ").title(),
                    str(count),
                    f"{percentage:.1f}%",
                    f"{avg_per_image:.1f}",
                )

            console.print(
                Panel(mode_table, title="🎯 Mode Analysis", border_style="yellow")
            )

        # 标签使用统计
        if stats["all_labels"]:
            # 分析标签使用情况
            label_usage = Counter()
            for sample in batch_samples:
                if sample.labels:
                    label_usage.update(sample.labels)

            # 显示前15个最常用标签
            top_labels = label_usage.most_common(15)

            label_table = Table(show_header=True, header_style="bold green")
            label_table.add_column("Rank", justify="right", width=6)
            label_table.add_column("Label", style="bold", width=25)
            label_table.add_column("Usage Count", justify="right", width=12)
            label_table.add_column("Sample %", justify="right", width=12)

            for i, (label, count) in enumerate(top_labels, 1):
                sample_percentage = (
                    (count / total_samples * 100) if total_samples > 0 else 0
                )
                label_table.add_row(
                    str(i), label, str(count), f"{sample_percentage:.1f}%"
                )

            console.print(
                Panel(label_table, title="🏷️ Label Usage in Batch", border_style="green")
            )

        # 数据质量指标
        quality_table = Table(show_header=True, header_style="bold red")
        quality_table.add_column("Quality Metric", style="bold", width=25)
        quality_table.add_column("Value", justify="right", width=15)
        quality_table.add_column("Status", justify="center", width=10)

        # 图片复用率
        if unique_images > 0:
            reuse_rate = int(total_samples) / int(unique_images)
            status = (
                "✅ Good"
                if reuse_rate >= 1.5
                else "⚠️ Low"
                if reuse_rate >= 1.2
                else "❌ Poor"
            )
            quality_table.add_row("Image Reuse Rate", f"{reuse_rate:.2f}x", status)

        # 标签覆盖率
        labeled_samples = sum(1 for sample in batch_samples if sample.labels)
        label_coverage = (
            (labeled_samples / total_samples * 100) if total_samples > 0 else 0
        )
        status = (
            "✅ Good"
            if label_coverage >= 80
            else "⚠️ Fair"
            if label_coverage >= 50
            else "❌ Poor"
        )
        quality_table.add_row("Label Coverage", f"{label_coverage:.1f}%", status)

        # 模式多样性
        mode_diversity = len(stats["mode_distribution"])
        status = (
            "✅ High"
            if mode_diversity >= 3
            else "⚠️ Medium"
            if mode_diversity >= 2
            else "❌ Low"
        )
        quality_table.add_row("Mode Diversity", str(mode_diversity), status)

        console.print(
            Panel(quality_table, title="📈 Data Quality Metrics", border_style="red")
        )

    except Exception as e:
        console.print(f"[red]❌ Failed to analyze batch: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@stats.command()
def modes():
    """Show mode distribution across all samples"""
    db = SessionLocal()
    try:
        sample_service = SampleService(db)

        console.print("[blue]🎯 Analyzing mode distribution...[/blue]")

        # 获取所有样本
        all_samples = sample_service.get_samples_with_filters(limit=10000)

        if not all_samples:
            console.print("[yellow]No samples found.[/yellow]")
            return

        # 统计模式分布
        mode_counts = Counter(sample.mode.value for sample in all_samples)
        total_samples = len(all_samples)

        # 创建详细的模式统计表
        mode_table = Table(show_header=True, header_style="bold cyan")
        mode_table.add_column("Training Mode", style="bold", width=20)
        mode_table.add_column("Sample Count", justify="right", width=12)
        mode_table.add_column("Percentage", justify="right", width=12)
        mode_table.add_column("Use Case", width=35)

        mode_descriptions = {
            "grounding": "Find objects/regions by description",
            "describe": "Describe what's at given coordinates",
            "enumerate_text": "List text elements in image",
            "enumerate_coord": "List coordinate elements in image",
            "checklist": "Verify presence/absence of items",
            "ensure": "Click to achieve target state",
        }

        for mode, count in mode_counts.most_common():
            percentage = count / total_samples * 100
            mode_table.add_row(
                mode.replace("_", " ").title(),
                str(count),
                f"{percentage:.1f}%",
                mode_descriptions.get(mode, ""),
            )

        console.print(
            Panel(
                mode_table, title="🎯 Mode Distribution Analysis", border_style="cyan"
            )
        )

        # 模式使用趋势（按批次）
        from ruyidv.models import Batch

        batches = db.query(Batch).order_by(Batch.created_at.desc()).limit(10).all()

        if len(batches) > 1:
            trend_table = Table(show_header=True, header_style="bold yellow")
            trend_table.add_column("Batch", width=25)
            for mode in SampleMode:
                trend_table.add_column(
                    mode.value.replace("_", " ").title()[:8], justify="right", width=8
                )

            for batch in batches:
                batch_samples = sample_service.get_samples_by_batch(batch.id)
                batch_mode_counts = Counter(
                    sample.mode.value for sample in batch_samples
                )

                row_data = [
                    batch.name[:23] + "..." if len(batch.name) > 25 else batch.name
                ]
                for mode in SampleMode:
                    count = batch_mode_counts.get(mode.value, 0)
                    row_data.append(str(count))

                trend_table.add_row(*row_data)

            console.print(
                Panel(
                    trend_table,
                    title="📈 Mode Usage by Recent Batches",
                    border_style="yellow",
                )
            )

    except Exception as e:
        console.print(f"[red]❌ Failed to analyze modes: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()


@stats.command()
@click.option("--top", default=20, help="Number of top labels to show (default: 20)")
@click.option(
    "--min-usage", default=1, help="Minimum usage count to include (default: 1)"
)
def labels(top: int = 20, min_usage: int = 1):
    """Show label usage statistics"""
    db = SessionLocal()
    try:
        sample_service = SampleService(db)

        console.print("[blue]🏷️ Analyzing label usage...[/blue]")

        # 获取所有样本
        all_samples = sample_service.get_samples_with_filters(limit=10000)

        if not all_samples:
            console.print("[yellow]No samples found.[/yellow]")
            return

        # 统计标签使用情况
        label_usage = Counter()
        labeled_samples_count = 0

        for sample in track(all_samples, description="Processing samples..."):
            if sample.labels:
                labeled_samples_count += 1
                label_usage.update(sample.labels)

        if not label_usage:
            console.print("[yellow]No labels found in any samples.[/yellow]")
            return

        # 过滤低使用量的标签
        filtered_labels = {
            label: count for label, count in label_usage.items() if count >= min_usage
        }

        # 基本统计
        stats_table = Table(show_header=False, box=None, padding=(0, 2))
        stats_table.add_column("Metric", style="bold cyan", width=25)
        stats_table.add_column("Value", justify="right", width=15)

        stats_table.add_row("Total Unique Labels", str(len(label_usage)))
        stats_table.add_row("Labels (≥ min usage)", str(len(filtered_labels)))
        stats_table.add_row("Total Label Instances", str(sum(label_usage.values())))
        stats_table.add_row("Samples with Labels", str(labeled_samples_count))
        stats_table.add_row(
            "Label Coverage", f"{(labeled_samples_count / len(all_samples) * 100):.1f}%"
        )

        console.print(
            Panel(stats_table, title="📊 Label Statistics", border_style="blue")
        )

        # 最常用标签
        top_labels = Counter(filtered_labels).most_common(top)

        label_table = Table(show_header=True, header_style="bold green")
        label_table.add_column("Rank", justify="right", width=6)
        label_table.add_column("Label", style="bold", width=25)
        label_table.add_column("Usage Count", justify="right", width=12)
        label_table.add_column("% of Samples", justify="right", width=12)
        label_table.add_column("% of Labels", justify="right", width=12)

        total_label_instances = sum(label_usage.values())

        for i, (label, count) in enumerate(top_labels, 1):
            sample_percentage = count / len(all_samples) * 100
            label_percentage = count / total_label_instances * 100

            label_table.add_row(
                str(i),
                label,
                str(count),
                f"{sample_percentage:.1f}%",
                f"{label_percentage:.1f}%",
            )

        title = f"🏷️ Top {len(top_labels)} Labels"
        if min_usage > 1:
            title += f" (≥{min_usage} uses)"

        console.print(Panel(label_table, title=title, border_style="green"))

        # 标签长度分布
        label_lengths = [len(label) for label in label_usage.keys()]
        length_stats = {
            "Shortest": min(label_lengths),
            "Longest": max(label_lengths),
            "Average": sum(label_lengths) / len(label_lengths),
            "Median": sorted(label_lengths)[len(label_lengths) // 2],
        }

        length_table = Table(show_header=True, header_style="bold yellow")
        length_table.add_column("Length Metric", style="bold", width=15)
        length_table.add_column("Characters", justify="right", width=12)

        for metric, value in length_stats.items():
            if metric == "Average":
                length_table.add_row(metric, f"{value:.1f}")
            else:
                length_table.add_row(metric, str(int(value)))

        console.print(
            Panel(length_table, title="📏 Label Length Analysis", border_style="yellow")
        )

        # 标签共现分析（前10对）
        if len(all_samples) > 0:
            console.print("\n[bold]🔗 Label Co-occurrence Analysis:[/bold]")

            co_occurrence = Counter()
            for sample in all_samples:
                if sample.labels and len(sample.labels) > 1:
                    # 生成标签对
                    for i, label1 in enumerate(sample.labels):
                        for label2 in sample.labels[i + 1 :]:
                            pair = tuple(sorted([label1, label2]))
                            co_occurrence[pair] += 1

            if co_occurrence:
                top_pairs = co_occurrence.most_common(5)

                pair_table = Table(show_header=True, header_style="bold magenta")
                pair_table.add_column("Rank", justify="right", width=6)
                pair_table.add_column("Label Pair", width=35)
                pair_table.add_column("Co-occurrence", justify="right", width=12)

                for i, ((label1, label2), count) in enumerate(top_pairs, 1):
                    pair_table.add_row(str(i), f"{label1} + {label2}", str(count))

                console.print(
                    Panel(
                        pair_table, title="🔗 Top Label Pairs", border_style="magenta"
                    )
                )
            else:
                console.print(
                    "[dim]No label co-occurrences found (samples have ≤1 label each)[/dim]"
                )

    except Exception as e:
        console.print(f"[red]❌ Failed to analyze labels: {e}[/red]")
        raise click.Abort()
    finally:
        db.close()
