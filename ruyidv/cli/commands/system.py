"""
System management commands
"""

import shutil
import subprocess
from pathlib import Path

import click
from rich.console import Console
from rich.panel import Panel
from sqlalchemy import text

from ruyidv.config import settings
from ruyidv.database import engine

console = Console()


@click.command()
@click.option(
    "--force", is_flag=True, help="Force initialization even if database already exists"
)
def init(force: bool):
    """Initialize the Ruyi Dataverse system"""
    try:
        console.print("[blue]🔄 Initializing Ruyi Dataverse...[/blue]")

        # 检查数据库表是否已存在
        tables_exist = False
        try:
            with engine.connect() as conn:
                if "sqlite" in settings.DATABASE_URL:
                    result = conn.execute(
                        text("SELECT name FROM sqlite_master WHERE type='table'")
                    )
                    tables = [row[0] for row in result]
                else:
                    # 对于其他数据库类型
                    result = conn.execute(
                        text(
                            "SELECT table_name FROM information_schema.tables "
                            "WHERE table_schema = 'public'"
                        )
                    )
                    tables = [row[0] for row in result]

                # 检查是否存在核心表
                expected_tables = {"batches", "samples", "images"}
                tables_exist = expected_tables.issubset(set(tables))

        except Exception:
            # 数据库连接失败或表不存在，需要初始化
            tables_exist = False

        if tables_exist and not force:
            console.print(
                "[yellow]Database tables already exist. Use --force to reinitialize.[/yellow]"
            )
            return

        # 创建存储目录
        console.print("[blue]📁 Creating storage directories...[/blue]")
        shutil.rmtree(settings.STORAGE_ROOT, ignore_errors=True)
        settings.STORAGE_ROOT.mkdir(parents=True, exist_ok=False)
        (settings.STORAGE_ROOT / "images").mkdir(exist_ok=True)
        (settings.STORAGE_ROOT / "exports").mkdir(exist_ok=True)
        (settings.STORAGE_ROOT / "imports").mkdir(exist_ok=True)

        # 创建数据库表结构
        console.print("[blue]🗄️  Creating database tables...[/blue]")
        from ruyidv.models import Base

        if force and tables_exist:
            # 如果强制重新初始化，先删除现有表
            Base.metadata.drop_all(bind=engine)
            console.print("[yellow]Dropped existing database tables[/yellow]")

        # 创建所有表
        Base.metadata.create_all(bind=engine)
        console.print("[green]✅ Database tables created successfully[/green]")

        # 设置alembic版本历史（如果alembic可用）
        try:
            result = subprocess.run(
                ["alembic", "stamp", "head"],
                capture_output=True,
                text=True,
                cwd=Path.cwd(),
            )
            if result.returncode == 0:
                console.print("[green]✅ Alembic version history initialized[/green]")
            else:
                console.print(
                    "[yellow]⚠️ Alembic stamp failed, but database tables are ready[/yellow]"
                )
        except FileNotFoundError:
            console.print(
                "[yellow]⚠️ Alembic not found, skipping version setup[/yellow]"
            )

        console.print(
            Panel.fit(
                "[green]✓[/green] Ruyi Dataverse initialized successfully!\n\n"
                f"[bold]Storage Path:[/bold] {settings.STORAGE_ROOT}\n"
                f"[bold]Database:[/bold] {settings.DATABASE_URL}\n"
                f"[bold]Tables Created:[/bold] Ready for use",
                title="🎉 Initialization Complete",
                border_style="green",
            )
        )

    except Exception as e:
        console.print(f"[red]❌ Initialization failed: {e}[/red]")
        raise click.Abort()
