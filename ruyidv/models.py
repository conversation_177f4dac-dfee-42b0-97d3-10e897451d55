from datetime import datetime
from enum import Enum as PyEnum
from typing import Any, Dict, List, Optional

from sqlalchemy import (
    JSO<PERSON>,
    Boolean,
    DateTime,
    Enum,
    ForeignKey,
    String,
    Text,
    UniqueConstraint,
    func,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .database import Base


class SampleMode(PyEnum):
    """样本模式枚举"""

    GROUNDING = "grounding"
    DESCRIBE = "describe"
    ENUMERATE_TEXT = "enumerate_text"
    ENUMERATE_COORD = "enumerate_coord"
    CHECKLIST = "checklist"
    ENSURE = "ensure"


class AnnotationStatus(PyEnum):
    """标注状态枚举"""

    PENDING = "pending"  # 待标注
    IN_PROGRESS = "in_progress"  # 标注中
    COMPLETED = "completed"  # 已完成
    PAUSED = "paused"  # 已暂停


class AnnotationResult(PyEnum):
    """标注结果枚举"""

    CORRECT = "correct"  # 正确
    INCORRECT = "incorrect"  # 错误
    SKIPPED = "skipped"  # 跳过


class Batch(Base):
    """批次模型"""

    __tablename__ = "batches"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(255), index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    # 关联关系
    samples: Mapped[List["Sample"]] = relationship(
        back_populates="batch", cascade="all, delete-orphan"
    )


class Image(Base):
    """图片模型"""

    __tablename__ = "images"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    hash_value: Mapped[str] = mapped_column(String(64), unique=True, index=True)
    file_path: Mapped[str] = mapped_column(String(500))
    file_size: Mapped[int] = mapped_column()
    width: Mapped[Optional[int]] = mapped_column(nullable=True)
    height: Mapped[Optional[int]] = mapped_column(nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    # 关联关系
    samples: Mapped[List["Sample"]] = relationship(
        back_populates="image", cascade="all, delete-orphan"
    )


class Sample(Base):
    """样本模型"""

    __tablename__ = "samples"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    image_id: Mapped[int] = mapped_column(ForeignKey("images.id"), index=True)
    batch_id: Mapped[int] = mapped_column(ForeignKey("batches.id"), index=True)
    mode: Mapped[SampleMode] = mapped_column(Enum(SampleMode), index=True)
    sample_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON)
    labels: Mapped[Optional[List[str]]] = mapped_column(
        JSON, nullable=True, default=list
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    # 关联关系
    image: Mapped["Image"] = relationship(back_populates="samples")
    batch: Mapped["Batch"] = relationship(back_populates="samples")
    annotation_results: Mapped[List["AnnotationSampleResult"]] = relationship(
        back_populates="sample", cascade="all, delete-orphan"
    )
    apply_logs: Mapped[List["AnnotationApplyLog"]] = relationship(
        back_populates="sample", cascade="all, delete-orphan"
    )


class AnnotationSession(Base):
    """标注会话模型"""

    __tablename__ = "annotation_sessions"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(255), index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    annotator_name: Mapped[str] = mapped_column(String(255), index=True)
    annotator_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # 查询条件 - 存储用于筛选样本的条件
    query_filters: Mapped[Dict[str, Any]] = mapped_column(JSON)

    # 会话状态
    status: Mapped[AnnotationStatus] = mapped_column(
        Enum(AnnotationStatus), default=AnnotationStatus.PENDING, index=True
    )

    # 进度统计
    total_samples: Mapped[int] = mapped_column(default=0)
    completed_samples: Mapped[int] = mapped_column(default=0)
    correct_samples: Mapped[int] = mapped_column(default=0)
    incorrect_samples: Mapped[int] = mapped_column(default=0)
    skipped_samples: Mapped[int] = mapped_column(default=0)

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    last_activity_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    # 关联关系
    sample_results: Mapped[List["AnnotationSampleResult"]] = relationship(
        back_populates="session", cascade="all, delete-orphan"
    )
    credential: Mapped[Optional["SessionCredential"]] = relationship(
        back_populates="session", cascade="all, delete-orphan"
    )


class AnnotationSampleResult(Base):
    """标注样本结果模型"""

    __tablename__ = "annotation_sample_results"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    session_id: Mapped[int] = mapped_column(
        ForeignKey("annotation_sessions.id"), index=True
    )
    sample_id: Mapped[int] = mapped_column(ForeignKey("samples.id"), index=True)

    # 标注结果
    result: Mapped[AnnotationResult] = mapped_column(Enum(AnnotationResult), index=True)

    # 原始元数据（标注前）
    original_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON)

    # 修正后的元数据（如果有修正）
    corrected_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )

    # 标注者备注
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # 标注时间
    annotated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    # 标注耗时（秒）
    annotation_duration: Mapped[Optional[int]] = mapped_column(nullable=True)

    # 关联关系
    session: Mapped["AnnotationSession"] = relationship(back_populates="sample_results")
    sample: Mapped["Sample"] = relationship(back_populates="annotation_results")

    # 添加唯一约束，确保同一会话中的样本不会重复标注
    __table_args__ = (
        UniqueConstraint("session_id", "sample_id", name="uq_session_sample"),
    )


class AnnotationApplyLog(Base):
    """标注应用日志模型 - 用于跟踪和回滚修改"""

    __tablename__ = "annotation_apply_logs"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # 应用操作信息
    operation_id: Mapped[str] = mapped_column(
        String(36), index=True
    )  # UUID for grouping related changes
    applied_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, index=True
    )
    applied_by: Mapped[str] = mapped_column(String(255))  # 操作员标识
    source_file: Mapped[str] = mapped_column(String(500))  # 源JSON文件路径

    # 样本修改信息
    sample_id: Mapped[int] = mapped_column(ForeignKey("samples.id"), index=True)

    # 元数据变更记录
    original_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON)  # 修改前的元数据
    new_metadata: Mapped[Dict[str, Any]] = mapped_column(JSON)  # 修改后的元数据

    # 操作状态
    operation_status: Mapped[str] = mapped_column(
        String(20), default="applied"
    )  # applied, rolled_back
    rollback_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    rollback_by: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # 备注信息
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # 关联关系
    sample: Mapped["Sample"] = relationship(back_populates="apply_logs")


# 在Sample模型中添加反向关系（需要在Sample类中添加）
# apply_logs: Mapped[List["AnnotationApplyLog"]] = relationship(back_populates="sample")


class SessionCredential(Base):
    """会话凭证模型"""

    __tablename__ = "session_credentials"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    session_id: Mapped[int] = mapped_column(
        ForeignKey("annotation_sessions.id"), unique=True, index=True
    )
    username: Mapped[str] = mapped_column(String(50), index=True)
    hashed_password: Mapped[str] = mapped_column(String(255))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    # 关联关系
    session: Mapped["AnnotationSession"] = relationship(back_populates="credential")


class AdminCredential(Base):
    """管理员凭证模型 - 单一管理员系统"""

    __tablename__ = "admin_credentials"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    dashboard_path: Mapped[str] = mapped_column(String(255), unique=True, index=True)
    hashed_password: Mapped[str] = mapped_column(String(255))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    last_login_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )


class AuditEventType(PyEnum):
    """审计事件类型枚举"""

    # Authentication events
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    SESSION_TIMEOUT = "session_timeout"

    # Session events
    SESSION_ACCESS = "session_access"
    SESSION_CREATE = "session_create"
    SESSION_DELETE = "session_delete"

    # Annotation events
    ANNOTATION_SUBMIT = "annotation_submit"
    ANNOTATION_UPDATE = "annotation_update"
    ANNOTATION_DELETE = "annotation_delete"

    # Admin events
    ADMIN_ACTION = "admin_action"
    CONFIG_CHANGE = "config_change"


class AuditLog(Base):
    """审计日志模型"""

    __tablename__ = "audit_logs"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    event_type: Mapped[AuditEventType] = mapped_column(Enum(AuditEventType), index=True)

    session_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("annotation_sessions.id"), nullable=True, index=True
    )
    username: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, index=True
    )
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45), nullable=True
    )  # IPv6 support
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    details: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    success: Mapped[bool] = mapped_column(Boolean, default=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True
    )

    # 关联关系
    session: Mapped[Optional["AnnotationSession"]] = relationship()
