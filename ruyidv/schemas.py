from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel, ConfigDict, Field

from .models import AnnotationResult, AnnotationStatus, SampleMode


class BatchBase(BaseModel):
    """批次基础模型"""

    name: str = Field(..., min_length=1, max_length=255, description="批次名称")
    description: Optional[str] = Field(None, description="批次描述")


class BatchCreate(BatchBase):
    """创建批次模型"""

    pass


class BatchResponse(BatchBase):
    """批次响应模型"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    created_at: datetime


class ImageBase(BaseModel):
    """图片基础模型"""

    hash_value: str = Field(..., min_length=32, max_length=64, description="图片哈希值")
    file_path: str = Field(..., min_length=1, max_length=500, description="文件路径")
    file_size: int = Field(..., gt=0, description="文件大小")
    width: Optional[int] = Field(None, gt=0, description="图片宽度")
    height: Optional[int] = Field(None, gt=0, description="图片高度")


class ImageCreate(ImageBase):
    """创建图片模型"""

    pass


class ImageResponse(ImageBase):
    """图片响应模型"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    created_at: datetime


class SampleBase(BaseModel):
    """样本基础模型"""

    mode: SampleMode = Field(..., description="样本模式")
    sample_metadata: dict[str, Any] = Field(..., description="样本元数据")
    labels: Optional[list[str]] = Field(default_factory=list, description="样本标签")


class SampleCreate(SampleBase):
    """创建样本模型"""

    image_id: int = Field(..., gt=0, description="关联图片ID")
    batch_id: int = Field(..., gt=0, description="关联批次ID")


class SampleUpdate(BaseModel):
    """更新样本模型"""

    sample_metadata: Optional[dict[str, Any]] = Field(None, description="样本元数据")
    labels: Optional[list[str]] = Field(None, description="样本标签")


class SampleResponse(SampleBase):
    """样本响应模型"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    image_id: int
    batch_id: int
    created_at: datetime


class SampleWithRelations(SampleResponse):
    """包含关联信息的样本响应模型"""

    image: ImageResponse
    batch: BatchResponse


# 批量操作模型
class BatchImportRequest(BaseModel):
    """批量导入请求模型"""

    batch_name: str = Field(..., min_length=1, max_length=255, description="批次名称")
    batch_description: Optional[str] = Field(None, description="批次描述")
    samples: list[dict[str, Any]] = Field(..., description="样本数据列表")


# 查询参数模型
class SampleQueryParams(BaseModel):
    """样本查询参数模型"""

    batch_id: Optional[int] = Field(None, gt=0, description="批次ID")
    mode: Optional[SampleMode] = Field(None, description="样本模式")
    labels: Optional[list[str]] = Field(None, description="标签过滤")
    limit: int = Field(100, ge=1, le=1000, description="返回数量限制")
    offset: int = Field(0, ge=0, description="偏移量")


# API响应包装模型
class ApiResponse(BaseModel):
    """API响应包装模型"""

    success: bool = Field(True, description="请求是否成功")
    message: str = Field("", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class PaginatedResponse(BaseModel):
    """分页响应模型"""

    items: list[Any] = Field(..., description="数据列表")
    total: int = Field(..., ge=0, description="总数量")
    limit: int = Field(..., ge=1, description="每页数量")
    offset: int = Field(..., ge=0, description="偏移量")


# 标注相关模型
class AnnotationSessionBase(BaseModel):
    """标注会话基础模型"""

    name: str = Field(..., min_length=1, max_length=255, description="会话名称")
    description: Optional[str] = Field(None, description="会话描述")
    annotator_name: str = Field(
        ..., min_length=1, max_length=255, description="标注者姓名"
    )
    annotator_email: Optional[str] = Field(None, description="标注者邮箱")
    query_filters: dict[str, Any] = Field(..., description="样本查询过滤条件")


class AnnotationSessionCreate(AnnotationSessionBase):
    """创建标注会话模型"""

    pass


class AnnotationSessionUpdate(BaseModel):
    """更新标注会话模型"""

    name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="会话名称"
    )
    description: Optional[str] = Field(None, description="会话描述")
    status: Optional[AnnotationStatus] = Field(None, description="会话状态")


class AnnotationSessionResponse(AnnotationSessionBase):
    """标注会话响应模型"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    status: AnnotationStatus
    total_samples: int
    completed_samples: int
    correct_samples: int
    incorrect_samples: int
    skipped_samples: int
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    last_activity_at: Optional[datetime]


class AnnotationSampleResultBase(BaseModel):
    """标注样本结果基础模型"""

    result: AnnotationResult = Field(..., description="标注结果")
    corrected_metadata: Optional[dict[str, Any]] = Field(
        None, description="修正后的元数据"
    )
    notes: Optional[str] = Field(None, description="标注者备注")
    annotation_duration: Optional[int] = Field(None, ge=0, description="标注耗时（秒）")


class AnnotationSampleResultCreate(AnnotationSampleResultBase):
    """创建标注样本结果模型"""

    session_id: int = Field(..., gt=0, description="标注会话ID")
    sample_id: int = Field(..., gt=0, description="样本ID")


class AnnotationSampleResultResponse(AnnotationSampleResultBase):
    """标注样本结果响应模型"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    session_id: int
    sample_id: int
    original_metadata: dict[str, Any]
    annotated_at: datetime


class AnnotationSampleResultWithRelations(AnnotationSampleResultResponse):
    """包含关联信息的标注样本结果响应模型"""

    sample: SampleWithRelations
    session: AnnotationSessionResponse


# Authentication schemas


class AdminAuthRequest(BaseModel):
    """Admin dashboard authentication request"""

    dashboard_path: str = Field(
        ..., min_length=1, max_length=255, description="Admin dashboard path"
    )
    password: str = Field(..., min_length=1, description="Admin password")


class AdminCredentialResponse(BaseModel):
    """Admin credential response model"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    dashboard_path: str
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime]


class SessionCredentialBase(BaseModel):
    """会话凭证基础模型"""

    username: str = Field(..., min_length=1, max_length=50, description="用户名")


class SessionCredentialCreate(SessionCredentialBase):
    """创建会话凭证模型"""

    session_id: int = Field(..., gt=0, description="会话ID")
    password: str = Field(..., min_length=8, description="密码")


class SessionCredentialResponse(SessionCredentialBase):
    """会话凭证响应模型"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    session_id: int
    created_at: datetime


class SessionAuthRequest(BaseModel):
    """会话认证请求模型"""

    session_id: int = Field(..., gt=0, description="会话ID")
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=1, description="密码")
