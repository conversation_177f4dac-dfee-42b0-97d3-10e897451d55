"""
Ruyi Dataverse Services Layer
提供核心业务逻辑服务
"""

import hashlib
import importlib.util
import json
import sys
from pathlib import Path
from typing import Any, Callable, Dict

from PIL import Image as PILImage
from rich import print

from .annotation_export_service import AnnotationApplyService, AnnotationExportService
from .annotation_service import AnnotationService
from .auth_service import AuthService
from .batch_service import BatchService
from .export_service import ExportService
from .image_service import ImageService
from .import_service import ImportService
from .sample_service import SampleService

# 自定义格式化器注册系统
_CUSTOM_FORMATTERS: Dict[str, Callable] = {}
_FORMATTER_VALIDATION_CACHE: Dict[str, Dict[str, Any]] = {}


def get_cache_file_path():
    """获取验证缓存文件路径"""
    from ..config import settings

    cache_dir = settings.USER_FORMATTERS_DIR.parent / ".cache"
    cache_dir.mkdir(exist_ok=True)
    return cache_dir / "formatter_validation_cache.json"


def load_validation_cache():
    """加载验证缓存"""
    global _FORMATTER_VALIDATION_CACHE
    cache_file = get_cache_file_path()

    if cache_file.exists():
        try:
            with open(cache_file, "r", encoding="utf-8") as f:
                _FORMATTER_VALIDATION_CACHE = json.load(f)
        except Exception as e:
            print(f"Warning: Failed to load validation cache: {e}")
            _FORMATTER_VALIDATION_CACHE = {}
    else:
        _FORMATTER_VALIDATION_CACHE = {}


def save_validation_cache():
    """保存验证缓存"""
    cache_file = get_cache_file_path()

    try:
        with open(cache_file, "w", encoding="utf-8") as f:
            json.dump(_FORMATTER_VALIDATION_CACHE, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Warning: Failed to save validation cache: {e}")


def get_file_signature(file_path: Path) -> str:
    """
    获取文件签名（基于修改时间和大小）

    Args:
        file_path: 文件路径

    Returns:
        str: 文件签名
    """
    if not file_path.exists():
        return ""

    stat = file_path.stat()
    signature_data = f"{stat.st_mtime}_{stat.st_size}"
    return hashlib.md5(signature_data.encode()).hexdigest()


def is_formatter_validation_cached(formatter_name: str, file_path: Path) -> bool:
    """
    检查formatter验证是否已缓存且有效

    Args:
        formatter_name: formatter名称
        file_path: formatter文件路径

    Returns:
        bool: 是否有有效缓存
    """
    if formatter_name not in _FORMATTER_VALIDATION_CACHE:
        return False

    cached_data = _FORMATTER_VALIDATION_CACHE[formatter_name]
    current_signature = get_file_signature(file_path)

    return cached_data.get("file_signature") == current_signature


def cache_formatter_validation(
    formatter_name: str, file_path: Path, validation_result: Dict[str, Any]
):
    """
    缓存formatter验证结果

    Args:
        formatter_name: formatter名称
        file_path: formatter文件路径
        validation_result: 验证结果
    """
    file_signature = get_file_signature(file_path)

    _FORMATTER_VALIDATION_CACHE[formatter_name] = {
        "file_signature": file_signature,
        "file_path": str(file_path),
        "validation_result": validation_result,
        "cached_at": str(Path(file_path).stat().st_mtime),
    }


def get_cached_validation_result(formatter_name: str) -> Dict[str, Any]:
    """
    获取缓存的验证结果

    Args:
        formatter_name: formatter名称

    Returns:
        Dict: 验证结果
    """
    if formatter_name in _FORMATTER_VALIDATION_CACHE:
        return _FORMATTER_VALIDATION_CACHE[formatter_name]["validation_result"]
    return {}


def create_test_data_for_validation():
    """
    创建用于验证formatter的测试数据集

    Returns:
        Dict: 包含各种模式测试数据的字典
    """
    return {
        "grounding": {
            "metadata": {
                "description": "Submit button",
                "coordinate": [100, 200, 150, 220],
            },
            "kwargs": {
                "mode": "grounding",
                "labels": ["ui_element"],
                "image_id": 1,
                "batch_id": 1,
            },
        },
        "describe": {
            "metadata": {
                "description": "A blue submit button with white text",
                "coordinate": [100, 200, 150, 220],
            },
            "kwargs": {
                "mode": "describe",
                "labels": ["ui_element"],
                "image_id": 1,
                "batch_id": 1,
            },
        },
        "enumerate_text": {
            "metadata": {
                "general_description": "form input fields",
                "elements": ["Username", "Password", "Email", "Submit"],
            },
            "kwargs": {
                "mode": "enumerate_text",
                "labels": ["form"],
                "image_id": 1,
                "batch_id": 1,
            },
        },
        "enumerate_coord": {
            "metadata": {
                "general_description": "clickable buttons",
                "elements": [
                    [100, 200, 150, 220],
                    [200, 300, 250, 320],
                    [300, 400, 350, 420],
                ],
            },
            "kwargs": {
                "mode": "enumerate_coord",
                "labels": ["buttons"],
                "image_id": 1,
                "batch_id": 1,
            },
        },
        "checklist": {
            "metadata": {
                "descriptions": ["Login form is visible", "Submit button is enabled"],
                "results": [True, False],
            },
            "kwargs": {
                "mode": "checklist",
                "labels": ["validation"],
                "image_id": 1,
                "batch_id": 1,
            },
        },
        "ensure": {
            "metadata": {
                "expected_state": "User is logged in",
                "coordinate": [100, 200, 150, 220],
            },
            "kwargs": {
                "mode": "ensure",
                "labels": ["state_check"],
                "image_id": 1,
                "batch_id": 1,
            },
        },
    }


def create_test_image_for_validation() -> PILImage.Image:
    """
    创建用于验证图像处理函数的测试图像

    Returns:
        PIL.Image.Image: 测试图像对象
    """
    # 创建一个简单的测试图像 (200x100, RGB)
    image = PILImage.new("RGB", (200, 100), color="white")
    return image


def validate_image_processor(image_processor: Callable) -> Dict[str, Any]:
    """
    验证图像处理函数

    Args:
        image_processor: 图像处理函数

    Returns:
        Dict: 验证结果
    """
    validation_result = {"valid": True, "errors": []}

    try:
        # 创建测试图像
        test_image = create_test_image_for_validation()

        # 调用图像处理函数
        result_image = image_processor(test_image)

        # 验证返回值类型
        if not isinstance(result_image, PILImage.Image):
            raise ValueError(
                f"Image processor must return PIL.Image.Image, got {type(result_image)}"
            )

        # 验证图像是否有效
        try:
            result_image.verify()
        except Exception as e:
            raise ValueError(f"Returned image is corrupted: {e}")

        # 重新创建图像对象用于进一步检查（verify()会消耗图像）
        test_image = create_test_image_for_validation()
        result_image = image_processor(test_image)

        # 检查基本属性
        if result_image.size == (0, 0):
            raise ValueError("Returned image has zero size")

    except Exception as e:
        validation_result["valid"] = False
        validation_result["errors"].append(
            f"Image processor validation failed: {str(e)}"
        )

    return validation_result


def validate_formatter(formatter_func: Callable, formatter_name: str) -> Dict[str, Any]:
    """
    验证formatter函数是否能正确处理所有支持的模式

    Args:
        formatter_func: 待验证的formatter函数
        formatter_name: formatter名称

    Returns:
        Dict: 验证结果，包含是否通过验证和详细信息
    """
    test_data = create_test_data_for_validation()
    validation_results = {
        "valid": True,
        "tested_modes": [],
        "passed_modes": [],
        "failed_modes": [],
        "errors": [],
        "has_image_processor": False,
        "image_processor_valid": False,
    }

    for mode, data in test_data.items():
        validation_results["tested_modes"].append(mode)

        try:
            # 调用formatter函数
            result = formatter_func(data["metadata"], **data["kwargs"])

            # 验证返回值结构
            if not isinstance(result, dict):
                raise ValueError(f"Formatter must return a dict, got {type(result)}")

            if "instruction" not in result:
                raise ValueError("Formatter result must contain 'instruction' key")

            if "output" not in result:
                raise ValueError("Formatter result must contain 'output' key")

            # 验证返回值不为空
            if not result["instruction"] or not result["output"]:
                raise ValueError(
                    "Formatter result 'instruction' and 'output' must not be empty"
                )

            # 验证是字符串类型
            if not isinstance(result["instruction"], str):
                raise ValueError(
                    f"'instruction' must be string, got {type(result['instruction'])}"
                )

            if not isinstance(result["output"], str):
                raise ValueError(
                    f"'output' must be string, got {type(result['output'])}"
                )

            # 检查是否包含图像处理函数（可选）
            if "image_processor" in result:
                validation_results["has_image_processor"] = True

                if result["image_processor"] is not None:
                    if not callable(result["image_processor"]):
                        raise ValueError("'image_processor' must be callable or None")

                    # 只在第一次遇到时验证图像处理函数
                    if not validation_results["image_processor_valid"]:
                        image_validation = validate_image_processor(
                            result["image_processor"]
                        )
                        if image_validation["valid"]:
                            validation_results["image_processor_valid"] = True
                        else:
                            raise ValueError(
                                f"Image processor validation failed: {image_validation['errors']}"
                            )

            validation_results["passed_modes"].append(mode)

        except Exception as e:
            validation_results["valid"] = False
            validation_results["failed_modes"].append(mode)
            validation_results["errors"].append(f"Mode '{mode}': {str(e)}")

    return validation_results


def register_formatter(name: str, func: Callable, validate: bool = True):
    """
    注册自定义格式化器函数

    Args:
        name: 格式化器名称
        func: 格式化器函数，接收(metadata, **kwargs)参数，返回{"instruction": str, "output": str, "image_processor": Callable (可选)}
        validate: 是否进行验证，默认True
    """
    if validate:
        validation_result = validate_formatter(func, name)

        if not validation_result["valid"]:
            error_msg = f"Formatter '{name}' validation failed:\n"
            for error in validation_result["errors"]:
                error_msg += f"  - {error}\n"
            error_msg += f"Passed modes: {validation_result['passed_modes']}\n"
            error_msg += f"Failed modes: {validation_result['failed_modes']}"

            print(f"Warning: {error_msg}")
            # 可以选择是否阻止注册失败的formatter
            # raise ValueError(error_msg)  # 如果要严格阻止，取消注释这行
        else:
            success_msg = f"✅ Formatter '{name}' passed validation for modes: {validation_result['passed_modes']}"
            if validation_result["has_image_processor"]:
                if validation_result["image_processor_valid"]:
                    success_msg += " (with image processing support)"
                else:
                    success_msg += " (image processor validation failed)"
            print(success_msg)

    _CUSTOM_FORMATTERS[name] = func


def unregister_formatter(name: str) -> bool:
    """
    取消注册格式化器

    Args:
        name: 格式化器名称

    Returns:
        bool: 是否成功取消注册
    """
    if name in _CUSTOM_FORMATTERS:
        del _CUSTOM_FORMATTERS[name]
        return True
    return False


def get_formatter(name: str) -> Callable | None:
    """获取已注册的格式化器"""
    return _CUSTOM_FORMATTERS.get(name)


def list_formatters() -> list[str]:
    """列出所有已注册的格式化器"""
    return list(_CUSTOM_FORMATTERS.keys())


def get_formatter_info(name: str) -> Dict[str, Any] | None:
    """
    获取格式化器的详细信息

    Args:
        name: 格式化器名称

    Returns:
        dict: 包含名称、描述、来源等信息的字典
    """
    formatter = get_formatter(name)
    if not formatter:
        return None

    return {
        "name": name,
        "description": formatter.__doc__ or "No description available",
        "module": formatter.__module__
        if hasattr(formatter, "__module__")
        else "unknown",
        "source": "user"
        if formatter.__module__.startswith("ruyidv.user_formatters")
        else "builtin",
    }


def load_user_formatters():
    """
    从用户目录加载自定义格式化器

    扫描 ~/.ruyidv/formatters/ 目录下的 .py 文件，
    自动导入并注册其中的格式化器函数
    """
    from ..config import settings

    formatters_dir = settings.USER_FORMATTERS_DIR
    if not formatters_dir.exists():
        return

    # 加载验证缓存
    load_validation_cache()

    # 为用户格式化器创建命名空间
    user_formatters_module = "ruyidv.user_formatters"
    if user_formatters_module not in sys.modules:
        sys.modules[user_formatters_module] = type(sys)("user_formatters")

    loaded_count = 0
    validation_failed_count = 0
    cached_count = 0

    for py_file in formatters_dir.glob("*.py"):
        if py_file.name.startswith("_"):
            continue  # 跳过私有文件

        try:
            # 动态导入模块
            module_name = f"{user_formatters_module}.{py_file.stem}"
            spec = importlib.util.spec_from_file_location(module_name, py_file)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                sys.modules[module_name] = module

                # 扫描模块中的格式化器函数
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)

                    # 检查是否是有效的格式化器函数
                    if (
                        callable(attr)
                        and not attr_name.startswith("_")
                        and hasattr(attr, "__doc__")
                        and "formatter" in attr_name.lower()
                    ):
                        formatter_name = f"user_{attr_name}"

                        # 检查是否有有效缓存
                        if is_formatter_validation_cached(formatter_name, py_file):
                            # 使用缓存的验证结果
                            cached_validation_result = get_cached_validation_result(
                                formatter_name
                            )

                            if cached_validation_result.get("valid", False):
                                register_formatter(formatter_name, attr, validate=False)
                                loaded_count += 1
                                cached_count += 1
                                print(
                                    f"✅ Loaded formatter '{formatter_name}' from {py_file.name} (cached validation)"
                                )
                            else:
                                validation_failed_count += 1
                                print(
                                    f"❌ Formatter '{formatter_name}' from {py_file.name} failed validation (cached):"
                                )
                                for error in cached_validation_result.get("errors", []):
                                    print(f"    - {error}")
                        else:
                            # 需要重新验证
                            validation_result = validate_formatter(attr, formatter_name)

                            # 缓存验证结果
                            cache_formatter_validation(
                                formatter_name, py_file, validation_result
                            )

                            if validation_result["valid"]:
                                register_formatter(formatter_name, attr, validate=False)
                                loaded_count += 1
                                print(
                                    f"✅ Loaded and validated formatter '{formatter_name}' from {py_file.name}"
                                )
                            else:
                                validation_failed_count += 1
                                print(
                                    f"❌ Formatter '{formatter_name}' from {py_file.name} failed validation:"
                                )
                                for error in validation_result["errors"]:
                                    print(f"    - {error}")
                                print(
                                    f"    Passed modes: {validation_result['passed_modes']}"
                                )
                                print(
                                    f"    Failed modes: {validation_result['failed_modes']}"
                                )

        except Exception as e:
            print(f"Warning: Failed to load formatter from {py_file}: {e}")

    # 保存更新的缓存
    save_validation_cache()

    if loaded_count > 0:
        cache_info = f" ({cached_count} from cache)" if cached_count > 0 else ""
        print(
            f"✅ Successfully loaded {loaded_count} user formatters from {formatters_dir}{cache_info}"
        )

    if validation_failed_count > 0:
        print(
            f"❌ Warning: {validation_failed_count} user formatters failed validation and were not loaded"
        )
        print(
            "Please check your formatter implementations and ensure they handle all required modes correctly."
        )


def create_example_formatter_file():
    """
    在用户目录创建示例格式化器文件
    """
    from ..config import settings

    example_file = settings.USER_FORMATTERS_DIR / "example_formatters.py"

    if example_file.exists():
        return False  # 文件已存在

    example_content = '''"""
Example Custom Formatters for Ruyi Dataverse

This file demonstrates how to create custom formatters.
Any function with "formatter" in its name will be automatically registered.

New features:
- Support for image processing via image_processor function
- Use PIL.Image objects for image manipulation
- Access to image information via kwargs (width, height, size, etc.)
"""

from PIL import Image, ImageEnhance, ImageFilter, ImageDraw


def my_grounding_formatter(metadata, **kwargs):
    """Custom grounding formatter with detailed output"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    instruction = f"Locate the following object in the image: {description}"
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        output = f"Found at bounding box: top-left({x1}, {y1}), bottom-right({x2}, {y2})"
    else:
        output = "Object not found in the image"
    
    return {
        "instruction": instruction,
        "output": output
    }


def advanced_describe_formatter(metadata, **kwargs):
    """Advanced describe formatter with context"""
    coordinate = metadata.get("coordinate", [None, None, None, None])
    description = metadata.get("description", "")
    mode = kwargs.get("mode", "")
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        instruction = f"Describe the content within the region ({x1}, {y1}) to ({x2}, {y2})"
    else:
        instruction = "Provide a comprehensive description of the entire image"
    
    output = f"Description: {description}"
    if mode:
        output += f" (Mode: {mode})"
    
    return {
        "instruction": instruction,
        "output": output
    }


def image_aware_grounding_formatter(metadata, **kwargs):
    """Grounding formatter that uses image dimensions"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    # 获取图像信息
    image_width = kwargs.get("image_width", 0)
    image_height = kwargs.get("image_height", 0)
    image_size = kwargs.get("image_size", 0)
    
    instruction = f"在 {image_width}x{image_height} 像素的图像中找到: {description}"
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        # 计算相对位置
        rel_x = (x1 + x2) / 2 / image_width if image_width > 0 else 0
        rel_y = (y1 + y2) / 2 / image_height if image_height > 0 else 0
        
        output = f"绝对坐标: [{x1}, {y1}, {x2}, {y2}], 相对位置: ({rel_x:.2f}, {rel_y:.2f})"
    else:
        output = "未找到目标对象"
    
    return {
        "instruction": instruction,
        "output": output
    }


def enhanced_grounding_formatter(metadata, **kwargs):
    """Grounding formatter with image enhancement"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    instruction = f"在增强后的图像中找到: {description}"
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        output = f"坐标: [{x1}, {y1}, {x2}, {y2}]"
    else:
        output = "未找到目标对象"
    
    def enhance_image(image):
        """图像增强函数 - 增加对比度和锐度"""
        # 增加对比度
        enhancer = ImageEnhance.Contrast(image)
        enhanced = enhancer.enhance(1.3)
        
        # 增加锐度
        enhancer = ImageEnhance.Sharpness(enhanced)
        enhanced = enhancer.enhance(1.2)
        
        return enhanced
    
    return {
        "instruction": instruction,
        "output": output,
        "image_processor": enhance_image
    }


def coordinate_overlay_formatter(metadata, **kwargs):
    """在图像上绘制坐标框的formatter"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    # 获取图像信息
    image_width = kwargs.get("image_width", 0)
    image_height = kwargs.get("image_height", 0)
    
    instruction = f"在标注了坐标框的图像中识别: {description}"
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        output = f"目标区域: ({x1}, {y1}) 到 ({x2}, {y2})"
    else:
        output = "无目标区域"
    
    def draw_coordinate_box(image):
        """在图像上绘制坐标框"""
        # 创建图像副本
        img_with_box = image.copy()
        draw = ImageDraw.Draw(img_with_box)
        
        # 如果有坐标信息，绘制边界框
        if any(c is not None for c in coordinate):
            x1, y1, x2, y2 = coordinate
            # 确保坐标在图像范围内
            x1 = max(0, min(x1, image.width))
            y1 = max(0, min(y1, image.height))
            x2 = max(0, min(x2, image.width))
            y2 = max(0, min(y2, image.height))
            
            # 绘制红色边界框
            draw.rectangle([x1, y1, x2, y2], outline="red", width=3)
            
            # 添加标签
            draw.text((x1, y1-20), description, fill="red")
        
        return img_with_box
    
    return {
        "instruction": instruction,
        "output": output,
        "image_processor": draw_coordinate_box
    }


def blur_background_formatter(metadata, **kwargs):
    """带背景模糊效果的formatter"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    instruction = f"在背景模糊的图像中识别: {description}"
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        output = f"目标位置: ({x1}, {y1}) 到 ({x2}, {y2})"
    else:
        output = "目标不在图像中"
    
    def blur_background(image):
        """背景模糊处理函数"""
        # 对整个图像应用轻微的高斯模糊
        blurred = image.filter(ImageFilter.GaussianBlur(radius=1.5))
        
        # 如果有坐标信息，可以选择性地保留目标区域的清晰度
        coordinate = metadata.get("coordinate", [None, None, None, None])
        if any(c is not None for c in coordinate):
            x1, y1, x2, y2 = coordinate
            # 确保坐标在图像范围内
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(image.width, x2), min(image.height, y2)
            
            # 提取目标区域
            if x2 > x1 and y2 > y1:
                target_region = image.crop((x1, y1, x2, y2))
                # 将清晰的目标区域粘贴回模糊图像
                blurred.paste(target_region, (x1, y1))
        
        return blurred
    
    return {
        "instruction": instruction,
        "output": output,
        "image_processor": blur_background
    }


def adaptive_resize_formatter(metadata, **kwargs):
    """根据图像尺寸自适应调整的formatter"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    # 获取图像信息
    image_width = kwargs.get("image_width", 0)
    image_height = kwargs.get("image_height", 0)
    image_format = kwargs.get("image_info", {}).get("format", "unknown")
    
    # 根据图像尺寸调整指令
    if image_width > 1920 or image_height > 1080:
        size_desc = "高分辨率"
    elif image_width < 640 or image_height < 480:
        size_desc = "低分辨率"
    else:
        size_desc = "标准分辨率"
    
    instruction = f"在这张{size_desc}({image_width}x{image_height}, {image_format})图像中找到: {description}"
    
    if any(c is not None for c in coordinate):
        x1, y1, x2, y2 = coordinate
        # 计算目标区域占图像的比例
        area_ratio = ((x2 - x1) * (y2 - y1)) / (image_width * image_height) if image_width > 0 and image_height > 0 else 0
        output = f"坐标: [{x1}, {y1}, {x2}, {y2}], 占图像面积: {area_ratio:.2%}"
    else:
        output = "未找到目标"
    
    def adaptive_process(image):
        """根据图像尺寸自适应处理"""
        # 如果图像太大，适当缩小以提高处理速度
        if image.width > 2048 or image.height > 2048:
            # 保持宽高比缩放
            ratio = min(2048 / image.width, 2048 / image.height)
            new_size = (int(image.width * ratio), int(image.height * ratio))
            resized = image.resize(new_size, Image.Resampling.LANCZOS)
            return resized
        
        # 如果图像太小，适当增强清晰度
        elif image.width < 640 or image.height < 480:
            enhancer = ImageEnhance.Sharpness(image)
            enhanced = enhancer.enhance(1.5)
            return enhanced
        
        # 标准尺寸，轻微增强
        else:
            enhancer = ImageEnhance.Contrast(image)
            enhanced = enhancer.enhance(1.1)
            return enhanced
    
    return {
        "instruction": instruction,
        "output": output,
        "image_processor": adaptive_process
    }


def color_adjust_formatter(metadata, **kwargs):
    """颜色调整formatter"""
    description = metadata.get("description", "")
    coordinate = metadata.get("coordinate", [None, None, None, None])
    
    instruction = f"在色彩调整后的图像中定位: {description}"
    
    if any(c is not None for c in coordinate):
        output = f"位置: {coordinate}"
    else:
        output = "未检测到目标"
    
    def adjust_colors(image):
        """颜色调整函数 - 增强饱和度和亮度"""
        # 调整色彩饱和度
        enhancer = ImageEnhance.Color(image)
        enhanced = enhancer.enhance(1.4)
        
        # 调整亮度
        enhancer = ImageEnhance.Brightness(enhanced)
        enhanced = enhancer.enhance(1.1)
        
        return enhanced
    
    return {
        "instruction": instruction,
        "output": output,
        "image_processor": adjust_colors
    }
'''

    try:
        with open(example_file, "w", encoding="utf-8") as f:
            f.write(example_content)
        return True
    except Exception as e:
        print(f"Failed to create example formatter file: {e}")
        return False


# 预定义一些示例格式化器
def grounding_zh_formatter(metadata, **kwargs):
    """中文grounding格式化器示例"""
    desc = metadata.get("description", "")
    coord = metadata.get("coordinate", [None, None, None, None])

    instruction = f"请在图片中找到：{desc}"

    if any(c is not None for c in coord):
        output = f"坐标：({coord[0]}, {coord[1]}, {coord[2]}, {coord[3]})"
    else:
        output = "图片中未找到指定目标"

    return {"instruction": instruction, "output": output}


def describe_simple_formatter(metadata, **kwargs):
    """简单describe格式化器示例"""
    coord = metadata.get("coordinate", [None, None, None, None])
    desc = metadata.get("description", "")

    if any(c is not None for c in coord):
        instruction = (
            f"描述坐标({coord[0]}, {coord[1]}, {coord[2]}, {coord[3]})处的内容"
        )
    else:
        instruction = "描述整张图片的内容"

    return {"instruction": instruction, "output": desc}


# 注册示例格式化器
register_formatter("grounding_zh", grounding_zh_formatter, validate=False)
register_formatter("describe_simple", describe_simple_formatter, validate=False)

# 在模块加载时自动加载用户格式化器
try:
    load_user_formatters()
except Exception as e:
    print(f"Warning: Failed to load user formatters: {e}")

__all__ = [
    "AnnotationService",
    "AnnotationExportService",
    "AnnotationApplyService",
    "ImageService",
    "SampleService",
    "BatchService",
    "ImportService",
    "ExportService",
    "register_formatter",
    "unregister_formatter",
    "get_formatter",
    "list_formatters",
    "get_formatter_info",
    "load_user_formatters",
    "create_example_formatter_file",
    "validate_formatter",
    "get_cache_file_path",
    "load_validation_cache",
    "save_validation_cache",
    "is_formatter_validation_cached",
    "cache_formatter_validation",
    "get_cached_validation_result",
    "AnnotationApplyService",
    "AuthService",
]
