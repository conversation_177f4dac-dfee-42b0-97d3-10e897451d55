"""
Annotation Export Service
提供标注结果导出功能
"""

import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from ..models import AnnotationSampleResult, AnnotationSession
from .annotation_service import AnnotationService
from .sample_service import SampleService


class ExportResult:
    """导出结果"""

    def __init__(self, success: bool, message: str, exported_count: int = 0):
        self.success = success
        self.message = message
        self.exported_count = exported_count


class AnnotationExportService:
    """标注导出服务"""

    def __init__(self, db: Session):
        self.db = db
        self.annotation_service = AnnotationService(db)

    def export_session_to_json(
        self, session_id: int, output_path: Path
    ) -> ExportResult:
        """
        导出标注会话结果到JSON文件

        Args:
            session_id: 会话ID
            output_path: 输出文件路径

        Returns:
            ExportResult: 导出结果
        """
        try:
            # 获取会话信息
            session = self.annotation_service.get_annotation_session_by_id(session_id)
            if not session:
                return ExportResult(
                    False, f"Annotation session with ID {session_id} not found"
                )

            # 获取所有标注结果
            results = self.annotation_service.get_annotation_results_by_session(
                session_id, limit=10000
            )

            if not results:
                return ExportResult(
                    False, f"No annotation results found for session {session_id}"
                )

            # 构建导出数据
            export_data = self._build_export_data(session, results)

            # 写入JSON文件
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)

            return ExportResult(
                True,
                f"Successfully exported {len(results)} annotation results to {output_path}",
                len(results),
            )

        except Exception as e:
            return ExportResult(False, f"Export failed: {str(e)}")

    def _build_export_data(
        self, session: AnnotationSession, results: List[AnnotationSampleResult]
    ) -> Dict[str, Any]:
        """
        构建导出数据结构

        Args:
            session: 标注会话
            results: 标注结果列表

        Returns:
            Dict: 导出数据
        """
        # 统计信息
        correct_count = sum(1 for r in results if r.result.value == "correct")
        incorrect_count = sum(1 for r in results if r.result.value == "incorrect")
        skipped_count = sum(1 for r in results if r.result.value == "skipped")

        export_data = {
            "export_metadata": {
                "session_id": session.id,
                "session_name": session.name,
                "annotator_name": session.annotator_name,
                "annotator_email": session.annotator_email,
                "exported_at": datetime.utcnow().isoformat() + "Z",
                "export_version": "1.0",
                "total_samples": len(results),
                "correct_samples": correct_count,
                "incorrect_samples": incorrect_count,
                "skipped_samples": skipped_count,
            },
            "annotation_results": [],
        }

        # 添加标注结果
        for result in results:
            result_data = {
                "sample_id": result.sample_id,
                "result": result.result.value,
                "original_metadata": result.original_metadata,
                "annotation_duration": result.annotation_duration,
                "annotated_at": result.annotated_at.isoformat() + "Z"
                if result.annotated_at
                else None,
            }

            # 只有标记为错误的样本才包含修正元数据
            if result.result.value == "incorrect" and result.corrected_metadata:
                result_data["corrected_metadata"] = result.corrected_metadata

            # 添加备注（如果有）
            if result.notes:
                result_data["notes"] = result.notes

            export_data["annotation_results"].append(result_data)

        return export_data

    def get_session_summary(self, session_id: int) -> Optional[Dict[str, Any]]:
        """
        获取会话摘要信息

        Args:
            session_id: 会话ID

        Returns:
            Dict: 会话摘要，如果会话不存在返回None
        """
        try:
            session = self.annotation_service.get_annotation_session_by_id(session_id)
            if not session:
                return None

            results = self.annotation_service.get_annotation_results_by_session(
                session_id, limit=10000
            )

            correct_count = sum(1 for r in results if r.result.value == "correct")
            incorrect_count = sum(1 for r in results if r.result.value == "incorrect")
            skipped_count = sum(1 for r in results if r.result.value == "skipped")

            return {
                "session_id": session.id,
                "session_name": session.name,
                "annotator_name": session.annotator_name,
                "annotator_email": session.annotator_email,
                "status": session.status.value,
                "created_at": session.created_at.isoformat() + "Z",
                "completed_at": session.completed_at.isoformat() + "Z"
                if session.completed_at
                else None,
                "total_samples": session.total_samples,
                "completed_samples": session.completed_samples,
                "correct_samples": correct_count,
                "incorrect_samples": incorrect_count,
                "skipped_samples": skipped_count,
                "annotation_results_count": len(results),
            }

        except Exception:
            return None

    def list_annotation_sessions(
        self,
        annotator: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 20,
    ) -> List[Dict[str, Any]]:
        """
        列出标注会话

        Args:
            annotator: 按标注员名称过滤
            status: 按状态过滤
            limit: 最大返回数量

        Returns:
            List[Dict]: 会话列表
        """
        try:
            from ..models import AnnotationSession

            query = self.db.query(AnnotationSession)

            # 应用过滤条件
            if annotator:
                query = query.filter(
                    AnnotationSession.annotator_name.ilike(f"%{annotator}%")
                )

            if status:
                query = query.filter(AnnotationSession.status == status)

            # 按创建时间倒序排列
            query = query.order_by(AnnotationSession.created_at.desc())

            # 限制数量
            sessions = query.limit(limit).all()

            # 转换为字典格式
            result = []
            for session in sessions:
                session_data = {
                    "session_id": session.id,
                    "session_name": session.name,
                    "annotator_name": session.annotator_name,
                    "annotator_email": session.annotator_email,
                    "status": session.status.value,
                    "created_at": session.created_at.isoformat() + "Z",
                    "completed_at": session.completed_at.isoformat() + "Z"
                    if session.completed_at
                    else None,
                    "total_samples": session.total_samples,
                    "completed_samples": session.completed_samples,
                }
                result.append(session_data)

            return result

        except Exception:
            return []


class ValidationResult:
    """验证结果"""

    def __init__(
        self,
        success: bool,
        message: str,
        errors: Optional[List[str]] = None,
        warnings: Optional[List[str]] = None,
    ):
        self.success = success
        self.message = message
        self.errors = errors or []
        self.warnings = warnings or []
        self.valid_samples = 0
        self.invalid_samples = 0
        self.total_samples = 0


class PreviewResult:
    """预览结果"""

    def __init__(
        self,
        success: bool,
        message: str,
        changes: Optional[List[Dict[str, Any]]] = None,
    ):
        self.success = success
        self.message = message
        self.changes = changes or []
        self.total_changes = len(self.changes)


class ApplyResult:
    """应用结果"""

    def __init__(self, success: bool, message: str, operation_id: Optional[str] = None):
        self.success = success
        self.message = message
        self.operation_id = operation_id
        self.applied_count = 0
        self.failed_count = 0
        self.errors = []
        self.applied_samples = []


class AnnotationApplyService:
    """标注应用服务"""

    def __init__(self, db: Session):
        self.db = db
        self.sample_service = SampleService(db)

    def validate_corrections_json(self, json_path: Path) -> ValidationResult:
        """
        验证修正JSON文件格式和内容

        Args:
            json_path: JSON文件路径

        Returns:
            ValidationResult: 验证结果
        """
        errors = []
        warnings = []

        try:
            # 检查文件是否存在
            if not json_path.exists():
                return ValidationResult(False, f"File not found: {json_path}")

            # 读取JSON文件
            with open(json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 验证JSON结构
            structure_errors = self._validate_json_structure(data)
            errors.extend(structure_errors)

            if errors:
                return ValidationResult(
                    False,
                    f"JSON structure validation failed with {len(errors)} errors",
                    errors,
                    warnings,
                )

            # 验证样本数据
            sample_errors, sample_warnings, valid_count, invalid_count = (
                self._validate_sample_data(data.get("annotation_results", []))
            )
            errors.extend(sample_errors)
            warnings.extend(sample_warnings)

            result = ValidationResult(
                len(errors) == 0,
                f"Validation completed: {valid_count} valid, {invalid_count} invalid samples",
                errors,
                warnings,
            )
            result.valid_samples = valid_count
            result.invalid_samples = invalid_count
            result.total_samples = valid_count + invalid_count

            return result

        except json.JSONDecodeError as e:
            return ValidationResult(False, f"Invalid JSON format: {str(e)}")
        except Exception as e:
            return ValidationResult(False, f"Validation failed: {str(e)}")

    def _validate_json_structure(self, data: Dict[str, Any]) -> List[str]:
        """验证JSON基本结构"""
        errors = []

        # 检查必需的顶级字段
        required_fields = ["export_metadata", "annotation_results"]
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")

        # 验证export_metadata结构
        if "export_metadata" in data:
            metadata = data["export_metadata"]
            required_metadata_fields = [
                "session_id",
                "session_name",
                "annotator_name",
                "exported_at",
                "export_version",
            ]
            for field in required_metadata_fields:
                if field not in metadata:
                    errors.append(f"Missing required metadata field: {field}")

        # 验证annotation_results是列表
        if "annotation_results" in data:
            if not isinstance(data["annotation_results"], list):
                errors.append("annotation_results must be a list")

        return errors

    def _validate_sample_data(
        self, results: List[Dict[str, Any]]
    ) -> Tuple[List[str], List[str], int, int]:
        """验证样本数据"""
        errors = []
        warnings = []
        valid_count = 0
        invalid_count = 0

        for i, result in enumerate(results):
            sample_errors = []

            # 检查必需字段
            required_fields = ["sample_id", "result", "original_metadata"]
            for field in required_fields:
                if field not in result:
                    sample_errors.append(
                        f"Sample {i}: Missing required field '{field}'"
                    )

            # 验证sample_id
            if "sample_id" in result:
                if not isinstance(result["sample_id"], int):
                    sample_errors.append(f"Sample {i}: sample_id must be an integer")
                else:
                    # 检查样本是否存在于数据库
                    sample = self.sample_service.get_sample_by_id(result["sample_id"])
                    if not sample:
                        sample_errors.append(
                            f"Sample {i}: sample_id {result['sample_id']} not found in database"
                        )

            # 验证result值
            if "result" in result:
                valid_results = ["correct", "incorrect", "skipped"]
                if result["result"] not in valid_results:
                    sample_errors.append(
                        f"Sample {i}: result must be one of {valid_results}"
                    )

            # 验证corrected_metadata（仅对incorrect样本）
            if result.get("result") == "incorrect":
                if "corrected_metadata" not in result:
                    warnings.append(
                        f"Sample {i}: incorrect sample missing corrected_metadata"
                    )
                elif result["corrected_metadata"] is None:
                    warnings.append(f"Sample {i}: corrected_metadata is null")

            if sample_errors:
                errors.extend(sample_errors)
                invalid_count += 1
            else:
                valid_count += 1

        return errors, warnings, valid_count, invalid_count

    def preview_changes(self, json_path: Path) -> PreviewResult:
        """
        预览将要应用的更改

        Args:
            json_path: JSON文件路径

        Returns:
            PreviewResult: 预览结果
        """
        try:
            # 首先验证文件
            validation_result = self.validate_corrections_json(json_path)
            if not validation_result.success:
                return PreviewResult(
                    False, f"Validation failed: {validation_result.message}"
                )

            # 读取JSON文件
            with open(json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            changes = []
            annotation_results = data.get("annotation_results", [])

            for result in annotation_results:
                sample_id = result["sample_id"]

                # 只处理标记为incorrect且有corrected_metadata的样本
                if result.get("result") == "incorrect" and result.get(
                    "corrected_metadata"
                ):
                    sample = self.sample_service.get_sample_by_id(sample_id)
                    if sample:
                        change = {
                            "sample_id": sample_id,
                            "action": "update_metadata",
                            "current_metadata": sample.sample_metadata,
                            "new_metadata": result["corrected_metadata"],
                            "notes": result.get("notes", ""),
                            "annotated_at": result.get("annotated_at"),
                        }
                        changes.append(change)

            return PreviewResult(
                True,
                f"Preview completed: {len(changes)} samples will be updated",
                changes,
            )

        except Exception as e:
            return PreviewResult(False, f"Preview failed: {str(e)}")

    def apply_corrections(
        self, json_path: Path, interactive: bool = False
    ) -> ApplyResult:
        """
        应用修正到数据库

        Args:
            json_path: JSON文件路径
            interactive: 是否启用交互式确认

        Returns:
            ApplyResult: 应用结果
        """
        try:
            # 首先验证文件
            validation_result = self.validate_corrections_json(json_path)
            if not validation_result.success:
                return ApplyResult(
                    False, f"Validation failed: {validation_result.message}"
                )

            # 获取预览信息
            preview_result = self.preview_changes(json_path)
            if not preview_result.success:
                return ApplyResult(False, f"Preview failed: {preview_result.message}")

            if preview_result.total_changes == 0:
                return ApplyResult(True, "No changes to apply")

            # 交互式确认
            if interactive:
                import click

                if not click.confirm(
                    f"Apply {preview_result.total_changes} changes to the database?"
                ):
                    return ApplyResult(False, "Operation cancelled by user")

            # 读取JSON文件获取原始元数据
            with open(json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 生成操作ID用于分组相关的修改
            operation_id = str(uuid.uuid4())
            applied_by = "admin"  # 可以从配置或参数获取

            # 开始事务
            result = ApplyResult(True, "", operation_id)

            try:
                annotation_results = data.get("annotation_results", [])

                for annotation_result in annotation_results:
                    sample_id = annotation_result["sample_id"]

                    # 只处理标记为incorrect且有corrected_metadata的样本
                    if annotation_result.get(
                        "result"
                    ) != "incorrect" or not annotation_result.get("corrected_metadata"):
                        continue

                    new_metadata = annotation_result["corrected_metadata"]
                    original_metadata = annotation_result["original_metadata"]

                    # 获取当前样本
                    sample = self.sample_service.get_sample_by_id(sample_id)
                    if not sample:
                        error_msg = f"Sample {sample_id} not found"
                        result.errors.append(error_msg)
                        result.failed_count += 1
                        continue

                    # 验证修正后的元数据格式
                    if not self.sample_service.validate_sample_metadata(
                        sample.mode,
                        new_metadata,
                        sample.image.width,
                        sample.image.height,
                    ):
                        error_msg = (
                            f"Sample {sample_id} corrected metadata validation failed"
                        )
                        result.errors.append(error_msg)
                        result.failed_count += 1
                        continue

                    # 乐观锁检查 - 确保元数据未被其他进程修改
                    current_metadata = sample.sample_metadata

                    if current_metadata != original_metadata:
                        error_msg = f"Sample {sample_id} metadata has been modified since export (optimistic lock failed)"
                        result.errors.append(error_msg)
                        result.failed_count += 1
                        continue

                    # 创建变更日志记录
                    from ..models import AnnotationApplyLog

                    apply_log = AnnotationApplyLog(
                        operation_id=operation_id,
                        applied_by=applied_by,
                        source_file=str(json_path.absolute()),
                        sample_id=sample_id,
                        original_metadata=current_metadata,
                        new_metadata=new_metadata,
                        operation_status="applied",
                        notes=annotation_result.get("notes"),
                    )
                    self.db.add(apply_log)

                    # 更新样本元数据
                    sample.sample_metadata = new_metadata
                    self.db.add(sample)

                    result.applied_count += 1
                    result.applied_samples.append(sample_id)

                # 提交事务
                self.db.commit()

                if result.failed_count > 0:
                    result.message = f"Applied {result.applied_count} changes, {result.failed_count} failed"
                else:
                    result.message = (
                        f"Successfully applied {result.applied_count} changes"
                    )

                return result

            except Exception as e:
                # 回滚事务
                self.db.rollback()
                return ApplyResult(
                    False, f"Transaction failed and rolled back: {str(e)}"
                )

        except Exception as e:
            return ApplyResult(False, f"Apply failed: {str(e)}")

    def rollback_operation(
        self, operation_id: str, rollback_by: str = "admin"
    ) -> ApplyResult:
        """
        回滚指定操作的所有修改

        Args:
            operation_id: 操作ID
            rollback_by: 回滚操作员

        Returns:
            ApplyResult: 回滚结果
        """
        try:
            from ..models import AnnotationApplyLog

            # 查找该操作的所有日志记录
            logs = (
                self.db.query(AnnotationApplyLog)
                .filter(
                    AnnotationApplyLog.operation_id == operation_id,
                    AnnotationApplyLog.operation_status == "applied",
                )
                .all()
            )

            if not logs:
                return ApplyResult(
                    False, f"No applied changes found for operation {operation_id}"
                )

            result = ApplyResult(True, "")

            try:
                for log in logs:
                    # 获取样本
                    sample = self.sample_service.get_sample_by_id(log.sample_id)
                    if not sample:
                        error_msg = f"Sample {log.sample_id} not found during rollback"
                        result.errors.append(error_msg)
                        result.failed_count += 1
                        continue

                    # 检查当前元数据是否与日志中的new_metadata一致
                    if sample.sample_metadata != log.new_metadata:
                        error_msg = f"Sample {log.sample_id} metadata has been modified since apply operation"
                        result.errors.append(error_msg)
                        result.failed_count += 1
                        continue

                    # 恢复原始元数据
                    sample.sample_metadata = log.original_metadata
                    self.db.add(sample)

                    # 更新日志状态
                    log.operation_status = "rolled_back"
                    log.rollback_at = datetime.utcnow()
                    log.rollback_by = rollback_by
                    self.db.add(log)

                    result.applied_count += 1
                    result.applied_samples.append(log.sample_id)

                # 提交事务
                self.db.commit()

                if result.failed_count > 0:
                    result.message = f"Rolled back {result.applied_count} changes, {result.failed_count} failed"
                else:
                    result.message = (
                        f"Successfully rolled back {result.applied_count} changes"
                    )

                return result

            except Exception as e:
                # 回滚事务
                self.db.rollback()
                return ApplyResult(False, f"Rollback transaction failed: {str(e)}")

        except Exception as e:
            return ApplyResult(False, f"Rollback failed: {str(e)}")

    def list_apply_operations(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        列出应用操作历史

        Args:
            limit: 最大返回数量

        Returns:
            List[Dict]: 操作历史列表
        """
        try:
            from ..models import AnnotationApplyLog

            # 按操作ID分组，获取操作摘要
            operations = (
                self.db.query(
                    AnnotationApplyLog.operation_id,
                    AnnotationApplyLog.applied_at,
                    AnnotationApplyLog.applied_by,
                    AnnotationApplyLog.source_file,
                    AnnotationApplyLog.operation_status,
                )
                .distinct(AnnotationApplyLog.operation_id)
                .order_by(AnnotationApplyLog.applied_at.desc())
                .limit(limit)
                .all()
            )

            result = []
            for op in operations:
                # 统计该操作的详细信息
                op_logs = (
                    self.db.query(AnnotationApplyLog)
                    .filter(AnnotationApplyLog.operation_id == op.operation_id)
                    .all()
                )

                applied_count = sum(
                    1 for log in op_logs if log.operation_status == "applied"
                )
                rolled_back_count = sum(
                    1 for log in op_logs if log.operation_status == "rolled_back"
                )

                operation_data = {
                    "operation_id": op.operation_id,
                    "applied_at": op.applied_at.isoformat() + "Z",
                    "applied_by": op.applied_by,
                    "source_file": op.source_file,
                    "total_changes": len(op_logs),
                    "applied_changes": applied_count,
                    "rolled_back_changes": rolled_back_count,
                    "status": "partially_rolled_back"
                    if rolled_back_count > 0 and applied_count > 0
                    else "rolled_back"
                    if rolled_back_count == len(op_logs)
                    else "applied",
                }
                result.append(operation_data)

            return result

        except Exception:
            return []
