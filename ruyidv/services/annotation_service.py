"""
标注服务模块
提供标注会话和标注结果的管理功能
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session

from ..models import (
    AnnotationResult,
    AnnotationSampleResult,
    AnnotationSession,
    AnnotationStatus,
    Sample,
    SampleMode,
)
from ..schemas import (
    AnnotationSampleResultCreate,
    AnnotationSessionCreate,
    AnnotationSessionUpdate,
)
from .sample_service import SampleService


class AnnotationService:
    """标注管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.sample_service = SampleService(db)

    def create_annotation_session(
        self, session_data: AnnotationSessionCreate
    ) -> AnnotationSession:
        """
        创建标注会话

        Args:
            session_data: 会话创建数据

        Returns:
            AnnotationSession: 创建的会话记录
        """
        # 根据查询条件获取样本总数
        total_samples = self.sample_service.count_samples_with_filters(
            batch_ids=session_data.query_filters.get("batch_ids"),
            modes=[
                SampleMode(mode) for mode in session_data.query_filters.get("modes", [])
            ]
            if session_data.query_filters.get("modes")
            else None,
            labels=session_data.query_filters.get("labels"),
        )

        session = AnnotationSession(
            name=session_data.name,
            description=session_data.description,
            annotator_name=session_data.annotator_name,
            annotator_email=session_data.annotator_email,
            query_filters=session_data.query_filters,
            total_samples=total_samples,
            status=AnnotationStatus.PENDING,
        )

        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)

        return session

    def get_annotation_session_by_id(
        self, session_id: int
    ) -> Optional[AnnotationSession]:
        """根据ID获取标注会话"""
        return (
            self.db.query(AnnotationSession)
            .filter(AnnotationSession.id == session_id)
            .first()
        )

    def get_annotation_sessions_by_annotator(
        self, annotator_name: str, limit: int = 100, offset: int = 0
    ) -> List[AnnotationSession]:
        """获取指定标注者的会话列表"""
        return (
            self.db.query(AnnotationSession)
            .filter(AnnotationSession.annotator_name == annotator_name)
            .order_by(AnnotationSession.created_at.desc())
            .limit(limit)
            .offset(offset)
            .all()
        )

    def get_all_annotation_sessions(
        self, limit: int = 100, offset: int = 0
    ) -> List[AnnotationSession]:
        """获取所有标注会话列表"""
        return (
            self.db.query(AnnotationSession)
            .order_by(AnnotationSession.created_at.desc())
            .limit(limit)
            .offset(offset)
            .all()
        )

    def update_annotation_session(
        self, session_id: int, update_data: AnnotationSessionUpdate
    ) -> Optional[AnnotationSession]:
        """更新标注会话"""
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(session, field, value)

        # 更新最后活动时间
        session.last_activity_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(session)

        return session

    def start_annotation_session(self, session_id: int) -> Optional[AnnotationSession]:
        """开始标注会话"""
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return None

        session.status = AnnotationStatus.IN_PROGRESS
        session.started_at = datetime.utcnow()
        session.last_activity_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(session)

        return session

    def complete_annotation_session(
        self, session_id: int
    ) -> Optional[AnnotationSession]:
        """完成标注会话"""
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return None

        session.status = AnnotationStatus.COMPLETED
        session.completed_at = datetime.utcnow()
        session.last_activity_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(session)

        return session

    def delete_annotation_session(self, session_id: int) -> bool:
        """
        删除标注会话及其所有相关数据

        Args:
            session_id: 会话ID

        Returns:
            bool: 删除是否成功
        """
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return False

        try:
            # SQLAlchemy will handle cascade deletion of:
            # - AnnotationSampleResult (via cascade="all, delete-orphan")
            # - SessionCredential (via cascade="all, delete-orphan")
            self.db.delete(session)
            self.db.commit()
            return True
        except Exception:
            self.db.rollback()
            return False

    def get_session_samples(
        self, session_id: int, limit: int = 100, offset: int = 0
    ) -> List[Sample]:
        """
        获取会话对应的样本列表

        Args:
            session_id: 会话ID
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            List[Sample]: 样本列表
        """
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return []

        # 根据会话的查询条件获取样本
        filters = session.query_filters
        modes = None
        if filters.get("modes"):
            modes = [SampleMode(mode) for mode in filters["modes"]]

        return self.sample_service.get_samples_with_filters(
            batch_ids=filters.get("batch_ids"),
            modes=modes,
            labels=filters.get("labels"),
            limit=limit,
            offset=offset,
        )

    def get_next_unannotated_sample(self, session_id: int) -> Optional[Sample]:
        """
        获取下一个未标注的样本

        Args:
            session_id: 会话ID

        Returns:
            Optional[Sample]: 下一个未标注的样本，如果没有则返回None
        """
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return None

        # 获取已标注的样本ID列表
        annotated_sample_ids_query = self.db.query(
            AnnotationSampleResult.sample_id
        ).filter(AnnotationSampleResult.session_id == session_id)

        # 根据会话的查询条件获取未标注的样本
        filters = session.query_filters
        query = self.db.query(Sample)

        # 应用过滤条件
        if filters.get("batch_ids"):
            query = query.filter(Sample.batch_id.in_(filters["batch_ids"]))

        if filters.get("modes"):
            modes = [SampleMode(mode) for mode in filters["modes"]]
            query = query.filter(Sample.mode.in_(modes))

        if filters.get("labels"):
            # 标签过滤：样本的标签列表中包含任一指定标签
            for label in filters["labels"]:
                query = query.filter(Sample.labels.contains([label]))

        # 排除已标注的样本
        query = query.filter(~Sample.id.in_(annotated_sample_ids_query))

        # 返回第一个未标注的样本
        return query.first()

    def create_annotation_result(
        self, result_data: AnnotationSampleResultCreate, allow_update: bool = False
    ) -> AnnotationSampleResult:
        """
        创建标注结果

        Args:
            result_data: 标注结果数据
            allow_update: 是否允许更新已存在的标注结果

        Returns:
            AnnotationSampleResult: 创建的标注结果记录

        Raises:
            ValueError: 如果会话或样本不存在，或样本已被标注且不允许更新
        """
        # 验证会话是否存在
        session = self.get_annotation_session_by_id(result_data.session_id)
        if not session:
            raise ValueError(f"标注会话不存在: ID={result_data.session_id}")

        # 验证样本是否存在
        sample = self.sample_service.get_sample_by_id(result_data.sample_id)
        if not sample:
            raise ValueError(f"样本不存在: ID={result_data.sample_id}")

        # 检查样本是否已被标注
        existing_result = (
            self.db.query(AnnotationSampleResult)
            .filter(
                and_(
                    AnnotationSampleResult.session_id == result_data.session_id,
                    AnnotationSampleResult.sample_id == result_data.sample_id,
                )
            )
            .first()
        )

        if existing_result:
            if not allow_update:
                raise ValueError(
                    f"样本已被标注: 会话ID={result_data.session_id}, 样本ID={result_data.sample_id}"
                )
            else:
                # 更新现有标注结果
                return self._update_existing_annotation_result(
                    existing_result, result_data, session
                )

        # 创建新的标注结果记录
        annotation_result = AnnotationSampleResult(
            session_id=result_data.session_id,
            sample_id=result_data.sample_id,
            result=result_data.result,
            original_metadata=sample.sample_metadata,
            corrected_metadata=result_data.corrected_metadata,
            notes=result_data.notes,
            annotation_duration=result_data.annotation_duration,
        )

        self.db.add(annotation_result)

        # 更新会话统计信息（仅对新标注）
        self._update_session_statistics(result_data.session_id, result_data.result)

        # 更新会话最后活动时间
        session.last_activity_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(annotation_result)

        return annotation_result

    def _update_existing_annotation_result(
        self,
        existing_result: AnnotationSampleResult,
        result_data: AnnotationSampleResultCreate,
        session: AnnotationSession,
    ) -> AnnotationSampleResult:
        """
        更新现有的标注结果

        Args:
            existing_result: 现有的标注结果
            result_data: 新的标注数据
            session: 标注会话

        Returns:
            AnnotationSampleResult: 更新后的标注结果记录
        """
        # 保存旧的结果用于统计更新
        old_result = existing_result.result

        # 更新标注结果
        existing_result.result = result_data.result
        existing_result.corrected_metadata = result_data.corrected_metadata
        existing_result.notes = result_data.notes
        existing_result.annotation_duration = result_data.annotation_duration
        existing_result.annotated_at = datetime.utcnow()

        # 更新会话统计信息（减去旧结果，加上新结果）
        self._update_session_statistics_for_change(
            session.id, old_result, result_data.result
        )

        # 更新会话最后活动时间
        session.last_activity_at = datetime.utcnow()

        self.db.commit()
        self.db.refresh(existing_result)

        return existing_result

    def _update_session_statistics(self, session_id: int, result: AnnotationResult):
        """更新会话统计信息"""
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return

        # 自动开始会话：如果是第一次提交标注且状态为PENDING，则自动转为IN_PROGRESS
        if session.status == AnnotationStatus.PENDING:
            session.status = AnnotationStatus.IN_PROGRESS
            session.started_at = datetime.utcnow()

        session.completed_samples += 1

        if result == AnnotationResult.CORRECT:
            session.correct_samples += 1
        elif result == AnnotationResult.INCORRECT:
            session.incorrect_samples += 1
        elif result == AnnotationResult.SKIPPED:
            session.skipped_samples += 1

        # 检查是否完成所有样本
        if session.completed_samples >= session.total_samples:
            session.status = AnnotationStatus.COMPLETED
            session.completed_at = datetime.utcnow()

    def _update_session_statistics_for_change(
        self,
        session_id: int,
        old_result: AnnotationResult,
        new_result: AnnotationResult,
    ):
        """更新会话统计信息（用于修改现有标注）"""
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return

        # 减去旧结果的统计
        if old_result == AnnotationResult.CORRECT:
            session.correct_samples = max(0, session.correct_samples - 1)
        elif old_result == AnnotationResult.INCORRECT:
            session.incorrect_samples = max(0, session.incorrect_samples - 1)
        elif old_result == AnnotationResult.SKIPPED:
            session.skipped_samples = max(0, session.skipped_samples - 1)

        # 加上新结果的统计
        if new_result == AnnotationResult.CORRECT:
            session.correct_samples += 1
        elif new_result == AnnotationResult.INCORRECT:
            session.incorrect_samples += 1
        elif new_result == AnnotationResult.SKIPPED:
            session.skipped_samples += 1

    def get_annotation_results_by_session(
        self, session_id: int, limit: int = 100, offset: int = 0
    ) -> List[AnnotationSampleResult]:
        """获取会话的标注结果列表"""
        return (
            self.db.query(AnnotationSampleResult)
            .filter(AnnotationSampleResult.session_id == session_id)
            .order_by(AnnotationSampleResult.annotated_at.desc())
            .limit(limit)
            .offset(offset)
            .all()
        )

    def get_annotation_result_by_sample(
        self, session_id: int, sample_id: int
    ) -> Optional[AnnotationSampleResult]:
        """获取指定样本的标注结果"""
        return (
            self.db.query(AnnotationSampleResult)
            .filter(
                and_(
                    AnnotationSampleResult.session_id == session_id,
                    AnnotationSampleResult.sample_id == sample_id,
                )
            )
            .first()
        )

    def get_session_progress(self, session_id: int) -> Dict[str, Any]:
        """
        获取会话进度信息

        Args:
            session_id: 会话ID

        Returns:
            Dict: 包含进度信息的字典
        """
        session = self.get_annotation_session_by_id(session_id)
        if not session:
            return {}

        progress_percentage = (
            (session.completed_samples / session.total_samples * 100)
            if session.total_samples > 0
            else 0
        )

        return {
            "session_id": session_id,
            "session_name": session.name,
            "status": session.status.value,
            "total_samples": session.total_samples,
            "completed_samples": session.completed_samples,
            "correct_samples": session.correct_samples,
            "incorrect_samples": session.incorrect_samples,
            "skipped_samples": session.skipped_samples,
            "progress_percentage": round(progress_percentage, 2),
            "created_at": session.created_at,
            "started_at": session.started_at,
            "completed_at": session.completed_at,
            "last_activity_at": session.last_activity_at,
        }
