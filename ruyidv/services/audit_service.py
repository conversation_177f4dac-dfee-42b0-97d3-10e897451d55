"""
Audit logging service for Ruyi Dataverse
"""

from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy.orm import Session

from ..models import AuditEventType, AuditLog


class AuditService:
    """Service for audit logging"""

    def __init__(self, db: Session):
        self.db = db

    def log_event(
        self,
        event_type: AuditEventType,
        session_id: Optional[int] = None,
        username: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ) -> AuditLog:
        """Log an audit event"""

        audit_log = AuditLog(
            event_type=event_type,
            session_id=session_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details,
            success=success,
            error_message=error_message,
        )

        self.db.add(audit_log)
        self.db.commit()
        self.db.refresh(audit_log)
        return audit_log

    def log_login_success(
        self,
        username: str,
        session_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> AuditLog:
        """Log successful login"""
        return self.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS,
            session_id=session_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            details={"login_type": "session" if session_id else "admin"},
        )

    def log_login_failure(
        self,
        username: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None,
    ) -> AuditLog:
        """Log failed login attempt"""
        return self.log_event(
            event_type=AuditEventType.LOGIN_FAILURE,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            success=False,
            error_message=error_message,
        )

    def log_logout(
        self,
        username: str,
        session_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> AuditLog:
        """Log logout"""
        return self.log_event(
            event_type=AuditEventType.LOGOUT,
            session_id=session_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
        )

    def log_session_timeout(
        self,
        username: str,
        session_id: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> AuditLog:
        """Log session timeout"""
        return self.log_event(
            event_type=AuditEventType.SESSION_TIMEOUT,
            session_id=session_id,
            username=username,
            details=details,
        )

    def log_annotation_submit(
        self,
        username: str,
        session_id: int,
        sample_id: int,
        result: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> AuditLog:
        """Log annotation submission"""
        return self.log_event(
            event_type=AuditEventType.ANNOTATION_SUBMIT,
            session_id=session_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            details={
                "sample_id": sample_id,
                "result": result,
            },
        )

    def log_session_access(
        self,
        username: str,
        session_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> AuditLog:
        """Log session access"""
        return self.log_event(
            event_type=AuditEventType.SESSION_ACCESS,
            session_id=session_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
        )

    def log_admin_action(
        self,
        username: str,
        action: str,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> AuditLog:
        """Log admin action"""
        return self.log_event(
            event_type=AuditEventType.ADMIN_ACTION,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            details={"action": action, **(details or {})},
        )

    def get_audit_logs(
        self,
        event_type: Optional[AuditEventType] = None,
        username: Optional[str] = None,
        session_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[AuditLog]:
        """Get audit logs with filtering"""
        query = self.db.query(AuditLog)

        if event_type:
            query = query.filter(AuditLog.event_type == event_type)
        if username:
            query = query.filter(AuditLog.username == username)
        if session_id:
            query = query.filter(AuditLog.session_id == session_id)
        if start_date:
            query = query.filter(AuditLog.timestamp >= start_date)
        if end_date:
            query = query.filter(AuditLog.timestamp <= end_date)

        return (
            query.order_by(AuditLog.timestamp.desc()).offset(offset).limit(limit).all()
        )
