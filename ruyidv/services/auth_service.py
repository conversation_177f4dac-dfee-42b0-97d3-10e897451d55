"""
Authentication service for Ruyi Dataverse
"""

import secrets
from typing import Optional

import bcrypt
from sqlalchemy.orm import Session

from ..models import AdminCredential, SessionCredential
from .audit_service import AuditService


class AuthService:
    """Authentication service"""

    def __init__(self, db: Session):
        self.db = db
        self.audit_service = AuditService(db)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        password_bytes = plain_password.encode("utf-8")
        # Convert string hash back to bytes for bcrypt
        hashed_password_bytes = hashed_password.encode("utf-8")
        return bcrypt.checkpw(password_bytes, hashed_password_bytes)

    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        password_bytes = password.encode("utf-8")
        salt = bcrypt.gensalt()
        hashed_password = bcrypt.hashpw(password_bytes, salt)
        return hashed_password.decode("utf-8")

    def generate_secure_password(self, length: int = 12) -> str:
        """Generate a secure random password"""
        return secrets.token_urlsafe(length)

    def validate_password_policy(
        self, password: str, username: Optional[str] = None
    ) -> tuple[bool, list[str]]:
        """Validate password against security policy"""
        from .password_policy import default_password_policy

        return default_password_policy.validate(password, username)

    def create_session_credential(
        self, session_id: int, username: str, password: str
    ) -> SessionCredential:
        """Create credentials for an annotation session"""
        # Check if session already has credentials
        existing_cred = (
            self.db.query(SessionCredential)
            .filter(SessionCredential.session_id == session_id)
            .first()
        )
        if existing_cred:
            raise ValueError(f"Session {session_id} already has credentials")

        # Create new credentials
        hashed_password = self.get_password_hash(password)
        credential = SessionCredential(
            session_id=session_id,
            username=username,
            hashed_password=hashed_password,
        )

        self.db.add(credential)
        self.db.commit()
        self.db.refresh(credential)
        return credential

    def authenticate_session(
        self,
        session_id: int,
        username: str,
        password: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> bool:
        """Authenticate access to a specific session"""
        credential = (
            self.db.query(SessionCredential)
            .filter(SessionCredential.session_id == session_id)
            .first()
        )

        if not credential:
            self.audit_service.log_login_failure(
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                error_message="Session not found",
            )
            return False

        if credential.username != username:
            self.audit_service.log_login_failure(
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                error_message="Username mismatch",
            )
            return False

        if not self.verify_password(password, credential.hashed_password):
            self.audit_service.log_login_failure(
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                error_message="Invalid password",
            )
            return False

        # Log successful session authentication
        self.audit_service.log_login_success(
            username=username,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
        )

        return True

    def update_session_credential_password(
        self, session_id: int, new_password: str
    ) -> bool:
        """Update password for session credentials"""
        credential = (
            self.db.query(SessionCredential)
            .filter(SessionCredential.session_id == session_id)
            .first()
        )

        if not credential:
            return False

        credential.hashed_password = self.get_password_hash(new_password)
        self.db.commit()
        return True

    # Admin Dashboard Authentication Methods

    def create_admin_credential(
        self, dashboard_path: str, password: str
    ) -> AdminCredential:
        """Create or update admin dashboard credentials (single admin system)"""
        from datetime import datetime

        # Check if any admin credential already exists (single admin system)
        existing_cred = self.db.query(AdminCredential).first()

        if existing_cred:
            # Update existing credential
            existing_cred.dashboard_path = dashboard_path
            existing_cred.hashed_password = self.get_password_hash(password)
            existing_cred.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(existing_cred)
            return existing_cred
        else:
            # Create new credential
            hashed_password = self.get_password_hash(password)
            credential = AdminCredential(
                dashboard_path=dashboard_path,
                hashed_password=hashed_password,
            )

            self.db.add(credential)
            self.db.commit()
            self.db.refresh(credential)
            return credential

    def authenticate_admin(
        self,
        dashboard_path: str,
        password: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> bool:
        """Authenticate admin dashboard access"""
        from datetime import datetime

        credential = (
            self.db.query(AdminCredential)
            .filter(AdminCredential.dashboard_path == dashboard_path)
            .first()
        )

        if not credential:
            self.audit_service.log_login_failure(
                username="admin",
                ip_address=ip_address,
                user_agent=user_agent,
                error_message="Admin dashboard not found",
            )
            return False

        if not self.verify_password(password, credential.hashed_password):
            self.audit_service.log_login_failure(
                username="admin",
                ip_address=ip_address,
                user_agent=user_agent,
                error_message="Invalid admin password",
            )
            return False

        # Update last login time
        credential.last_login_at = datetime.utcnow()
        self.db.commit()

        # Log successful admin authentication
        self.audit_service.log_login_success(
            username="admin",
            ip_address=ip_address,
            user_agent=user_agent,
        )

        return True

    def get_admin_credential(self) -> Optional[AdminCredential]:
        """Get the single admin credential"""
        return self.db.query(AdminCredential).first()

    def get_admin_credential_by_path(
        self, dashboard_path: str
    ) -> Optional[AdminCredential]:
        """Get admin credential by dashboard path"""
        return (
            self.db.query(AdminCredential)
            .filter(AdminCredential.dashboard_path == dashboard_path)
            .first()
        )

    def delete_admin_credential(self) -> bool:
        """Delete the admin credential (single admin system)"""
        credential = self.db.query(AdminCredential).first()

        if not credential:
            return False

        self.db.delete(credential)
        self.db.commit()
        return True

    def generate_secure_dashboard_path(self, length: int = 16) -> str:
        """Generate a secure random dashboard path string"""
        return (
            secrets.token_urlsafe(length)[:length]
            .replace("_", "")
            .replace("-", "")
            .lower()
        )
