from typing import Any, Dict, List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Session

from ..models import <PERSON><PERSON>, Sample
from ..schemas import BatchCreate


class BatchService:
    """批次管理服务"""

    def __init__(self, db: Session):
        self.db = db

    def create_batch(self, batch_data: BatchCreate) -> Batch:
        """
        创建新批次

        Args:
            batch_data: 批次创建数据

        Returns:
            Batch: 创建的批次记录
        """
        batch = Batch(name=batch_data.name, description=batch_data.description)

        self.db.add(batch)
        self.db.commit()
        self.db.refresh(batch)

        return batch

    def get_batch_by_id(self, batch_id: int) -> Optional[Batch]:
        """根据ID获取批次"""
        return self.db.query(Batch).filter(Batch.id == batch_id).first()

    def get_batch_by_name(self, name: str) -> Optional[Batch]:
        """根据名称获取批次"""
        return self.db.query(Batch).filter(Batch.name == name).first()

    def get_all_batches(self, limit: int = 100, offset: int = 0) -> List[Batch]:
        """获取所有批次列表"""
        return (
            self.db.query(Batch)
            .order_by(Batch.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    def update_batch(
        self,
        batch_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
    ) -> Optional[Batch]:
        """
        更新批次信息

        Args:
            batch_id: 批次ID
            name: 新名称（可选）
            description: 新描述（可选）

        Returns:
            Batch: 更新后的批次记录，如果不存在返回None
        """
        batch = self.get_batch_by_id(batch_id)
        if not batch:
            return None

        if name is not None:
            batch.name = name

        if description is not None:
            batch.description = description

        self.db.commit()
        self.db.refresh(batch)

        return batch

    def delete_batch(self, batch_id: int) -> bool:
        """
        删除批次

        Args:
            batch_id: 批次ID

        Returns:
            bool: 是否成功删除

        Note:
            由于设置了级联删除，删除批次会同时删除该批次下的所有样本
        """
        batch = self.get_batch_by_id(batch_id)
        if not batch:
            return False

        self.db.delete(batch)
        self.db.commit()

        return True

    def get_batch_statistics(self, batch_id: int) -> Optional[Dict[str, Any]]:
        """
        获取批次统计信息

        Args:
            batch_id: 批次ID

        Returns:
            Dict: 包含统计信息的字典，如果批次不存在返回None
        """
        batch = self.get_batch_by_id(batch_id)
        if not batch:
            return None

        # 统计样本总数
        total_samples = (
            self.db.query(Sample).filter(Sample.batch_id == batch_id).count()
        )

        # 按模式统计样本数量
        mode_stats = (
            self.db.query(Sample.mode, func.count(Sample.id))
            .filter(Sample.batch_id == batch_id)
            .group_by(Sample.mode)
            .all()
        )

        mode_counts = {mode.value: count for mode, count in mode_stats}

        # 统计关联的唯一图片数量
        unique_images = (
            self.db.query(func.count(func.distinct(Sample.image_id)))
            .filter(Sample.batch_id == batch_id)
            .scalar()
        )

        # 收集所有标签
        samples_with_labels = (
            self.db.query(Sample.labels)
            .filter(Sample.batch_id == batch_id)
            .filter(Sample.labels.isnot(None))
            .all()
        )

        all_labels = set()
        for (labels,) in samples_with_labels:
            if labels:
                all_labels.update(labels)

        return {
            "batch_id": batch_id,
            "batch_name": batch.name,
            "total_samples": total_samples,
            "unique_images": unique_images,
            "mode_distribution": mode_counts,
            "all_labels": sorted(list(all_labels)),
            "created_at": batch.created_at,
        }

    def get_batch_samples_summary(
        self, batch_id: int, limit: int = 10
    ) -> Optional[Dict[str, Any]]:
        """
        获取批次样本摘要

        Args:
            batch_id: 批次ID
            limit: 返回的样本数量限制

        Returns:
            Dict: 包含样本摘要的字典
        """
        batch = self.get_batch_by_id(batch_id)
        if not batch:
            return None

        # 获取最新的一些样本
        recent_samples = (
            self.db.query(Sample)
            .filter(Sample.batch_id == batch_id)
            .order_by(Sample.created_at.desc())
            .limit(limit)
            .all()
        )

        sample_summaries = []
        for sample in recent_samples:
            sample_summaries.append({
                "id": sample.id,
                "mode": sample.mode.value,
                "labels": sample.labels,
                "created_at": sample.created_at,
            })

        return {
            "batch_id": batch_id,
            "batch_name": batch.name,
            "recent_samples": sample_summaries,
        }

    def count_batches(self) -> int:
        """统计批次总数"""
        return self.db.query(Batch).count()

    def search_batches(self, keyword: str, limit: int = 50) -> List[Batch]:
        """
        搜索批次

        Args:
            keyword: 搜索关键词（在名称和描述中搜索）
            limit: 返回数量限制

        Returns:
            List[Batch]: 匹配的批次列表
        """
        search_pattern = f"%{keyword}%"

        return (
            self.db.query(Batch)
            .filter(
                (Batch.name.ilike(search_pattern))
                | (Batch.description.ilike(search_pattern))
            )
            .order_by(Batch.created_at.desc())
            .limit(limit)
            .all()
        )
