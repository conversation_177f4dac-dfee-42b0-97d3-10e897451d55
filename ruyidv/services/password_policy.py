"""
Password policy validation for Ruyi Dataverse
"""

import re
from typing import List, Optional


class PasswordPolicy:
    """Password policy validator"""

    def __init__(
        self,
        min_length: int = 8,
        max_length: int = 128,
        require_uppercase: bool = True,
        require_lowercase: bool = True,
        require_digits: bool = True,
        require_special: bool = True,
        forbidden_patterns: Optional[List[str]] = None,
    ):
        self.min_length = min_length
        self.max_length = max_length
        self.require_uppercase = require_uppercase
        self.require_lowercase = require_lowercase
        self.require_digits = require_digits
        self.require_special = require_special
        self.forbidden_patterns = forbidden_patterns or [
            "password",
            "123456",
            "qwerty",
            "admin",
            "root",
            "user",
        ]

        # Special characters that are allowed
        self.special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    def validate(
        self, password: str, username: Optional[str] = None
    ) -> tuple[bool, List[str]]:
        """
        Validate password against policy

        Returns:
            tuple: (is_valid, list_of_errors)
        """
        errors = []

        # Length checks
        if len(password) < self.min_length:
            errors.append(
                f"Password must be at least {self.min_length} characters long"
            )

        if len(password) > self.max_length:
            errors.append(
                f"Password must be no more than {self.max_length} characters long"
            )

        # Character type requirements
        if self.require_uppercase and not re.search(r"[A-Z]", password):
            errors.append("Password must contain at least one uppercase letter")

        if self.require_lowercase and not re.search(r"[a-z]", password):
            errors.append("Password must contain at least one lowercase letter")

        if self.require_digits and not re.search(r"\d", password):
            errors.append("Password must contain at least one digit")

        if self.require_special and not re.search(
            f"[{re.escape(self.special_chars)}]", password
        ):
            errors.append(
                f"Password must contain at least one special character ({self.special_chars})"
            )

        # Check for forbidden patterns
        password_lower = password.lower()
        for pattern in self.forbidden_patterns:
            if pattern.lower() in password_lower:
                errors.append(
                    f"Password cannot contain common patterns like '{pattern}'"
                )

        # Check if password contains username
        if username and username.lower() in password_lower:
            errors.append("Password cannot contain the username")

        # Check for repeated characters
        if self._has_repeated_chars(password):
            errors.append(
                "Password cannot have more than 3 consecutive identical characters"
            )

        # Check for sequential characters
        if self._has_sequential_chars(password):
            errors.append(
                "Password cannot contain sequential characters (e.g., 'abc', '123')"
            )

        return len(errors) == 0, errors

    def _has_repeated_chars(self, password: str, max_repeat: int = 3) -> bool:
        """Check if password has too many repeated characters"""
        count = 1
        for i in range(1, len(password)):
            if password[i] == password[i - 1]:
                count += 1
                if count > max_repeat:
                    return True
            else:
                count = 1
        return False

    def _has_sequential_chars(self, password: str, min_sequence: int = 3) -> bool:
        """Check if password has sequential characters"""
        password_lower = password.lower()

        # Check for ascending sequences
        for i in range(len(password_lower) - min_sequence + 1):
            sequence = password_lower[i : i + min_sequence]
            if self._is_ascending_sequence(sequence):
                return True

        # Check for descending sequences
        for i in range(len(password_lower) - min_sequence + 1):
            sequence = password_lower[i : i + min_sequence]
            if self._is_descending_sequence(sequence):
                return True

        return False

    def _is_ascending_sequence(self, sequence: str) -> bool:
        """Check if string is an ascending sequence"""
        if len(sequence) < 2:
            return False

        for i in range(1, len(sequence)):
            if ord(sequence[i]) != ord(sequence[i - 1]) + 1:
                return False
        return True

    def _is_descending_sequence(self, sequence: str) -> bool:
        """Check if string is a descending sequence"""
        if len(sequence) < 2:
            return False

        for i in range(1, len(sequence)):
            if ord(sequence[i]) != ord(sequence[i - 1]) - 1:
                return False
        return True

    def generate_policy_description(self) -> str:
        """Generate a human-readable description of the password policy"""
        requirements = []

        requirements.append(
            f"Be between {self.min_length} and {self.max_length} characters long"
        )

        if self.require_uppercase:
            requirements.append("Contain at least one uppercase letter")

        if self.require_lowercase:
            requirements.append("Contain at least one lowercase letter")

        if self.require_digits:
            requirements.append("Contain at least one digit")

        if self.require_special:
            requirements.append(
                f"Contain at least one special character ({self.special_chars})"
            )

        requirements.append("Not contain common patterns or the username")
        requirements.append("Not have more than 3 consecutive identical characters")
        requirements.append("Not contain sequential characters")

        return "Password must:\n• " + "\n• ".join(requirements)


# Default password policy instance
default_password_policy = PasswordPolicy(
    min_length=8,
    max_length=128,
    require_uppercase=True,
    require_lowercase=True,
    require_digits=True,
    require_special=True,
)
