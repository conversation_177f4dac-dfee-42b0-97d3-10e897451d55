"""
Rate limiting service for Ruyi Dataverse
"""

import time
from collections import defaultdict, deque
from typing import Dict, Optional

from ..config import settings


class RateLimiter:
    """Simple in-memory rate limiter"""

    def __init__(self):
        # Store attempts per IP address
        self._attempts: Dict[str, deque] = defaultdict(deque)

    def is_rate_limited(
        self,
        ip_address: Optional[str],
        max_attempts: Optional[int] = None,
        window_minutes: Optional[int] = None,
    ) -> bool:
        """Check if an IP address is rate limited"""
        if not ip_address:
            return False

        max_attempts = max_attempts or settings.MAX_LOGIN_ATTEMPTS
        window_minutes = window_minutes or settings.LOGIN_ATTEMPT_WINDOW_MINUTES
        window_seconds = window_minutes * 60

        current_time = time.time()
        attempts = self._attempts[ip_address]

        # Remove old attempts outside the window
        while attempts and current_time - attempts[0] > window_seconds:
            attempts.popleft()

        # Check if we've exceeded the limit
        return len(attempts) >= max_attempts

    def record_attempt(self, ip_address: Optional[str]) -> None:
        """Record a failed attempt for an IP address"""
        if not ip_address:
            return

        current_time = time.time()
        self._attempts[ip_address].append(current_time)

        # Clean up old attempts to prevent memory leaks
        window_seconds = settings.LOGIN_ATTEMPT_WINDOW_MINUTES * 60
        attempts = self._attempts[ip_address]
        while attempts and current_time - attempts[0] > window_seconds:
            attempts.popleft()

    def reset_attempts(self, ip_address: Optional[str]) -> None:
        """Reset attempts for an IP address (e.g., after successful login)"""
        if ip_address in self._attempts:
            del self._attempts[ip_address]

    def get_remaining_attempts(
        self,
        ip_address: Optional[str],
        max_attempts: Optional[int] = None,
        window_minutes: Optional[int] = None,
    ) -> int:
        """Get remaining attempts for an IP address"""
        if not ip_address:
            return max_attempts or settings.MAX_LOGIN_ATTEMPTS

        max_attempts = max_attempts or settings.MAX_LOGIN_ATTEMPTS
        window_minutes = window_minutes or settings.LOGIN_ATTEMPT_WINDOW_MINUTES
        window_seconds = window_minutes * 60

        current_time = time.time()
        attempts = self._attempts[ip_address]

        # Remove old attempts outside the window
        while attempts and current_time - attempts[0] > window_seconds:
            attempts.popleft()

        return max(0, max_attempts - len(attempts))

    def get_reset_time(
        self, ip_address: Optional[str], window_minutes: Optional[int] = None
    ) -> Optional[float]:
        """Get the time when rate limiting will reset for an IP address"""
        if not ip_address or ip_address not in self._attempts:
            return None

        window_minutes = window_minutes or settings.LOGIN_ATTEMPT_WINDOW_MINUTES
        window_seconds = window_minutes * 60

        attempts = self._attempts[ip_address]
        if not attempts:
            return None

        # The reset time is when the oldest attempt expires
        return attempts[0] + window_seconds


# Global rate limiter instance
rate_limiter = RateLimiter()


def get_client_ip(request) -> Optional[str]:
    """Extract client IP address from request"""
    # Check for forwarded headers first (for reverse proxies)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For can contain multiple IPs, take the first one
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()

    # Fall back to direct client IP
    if hasattr(request, "client") and hasattr(request.client, "host"):
        return request.client.host

    return None
