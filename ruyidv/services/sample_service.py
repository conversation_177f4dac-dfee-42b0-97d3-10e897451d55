import hashlib
import json
from typing import Any, Callable, Dict, List, Optional, Tuple

from sqlalchemy import distinct, func, or_
from sqlalchemy.orm import Session

from ..models import Batch, Image, Sample, SampleMode
from ..schemas import SampleCreate, SampleUpdate


class SampleService:
    """样本管理服务"""

    def __init__(self, db: Session):
        self.db = db

    def calculate_metadata_hash(self, metadata: Dict[str, Any]) -> str:
        """
        计算元数据的稳定哈希值

        Args:
            metadata: 样本元数据字典

        Returns:
            str: 32位MD5哈希值
        """
        # 确保字典的键排序，以获得稳定的哈希
        sorted_metadata = json.dumps(metadata, sort_keys=True, ensure_ascii=False)

        # 计算MD5哈希（32位，足够用于重复检测）
        hash_md5 = hashlib.md5()
        hash_md5.update(sorted_metadata.encode("utf-8"))
        return hash_md5.hexdigest()

    def check_duplicate_sample(
        self, image_id: int, mode: SampleMode, metadata: Dict[str, Any]
    ) -> Optional[Sample]:
        """
        检查样本是否已存在

        Args:
            image_id: 图片ID
            mode: 样本模式
            metadata: 样本元数据

        Returns:
            Sample: 如果找到重复样本则返回，否则返回None
        """
        metadata_hash = self.calculate_metadata_hash(metadata)

        # 查找具有相同 image_id, mode 和 metadata_hash 的样本
        existing_samples = (
            self.db.query(Sample)
            .filter(Sample.image_id == image_id, Sample.mode == mode)
            .all()
        )

        # 由于无法直接在数据库中比较JSON内容的哈希，
        # 我们需要在应用层进行比较
        for sample in existing_samples:
            if self.calculate_metadata_hash(sample.sample_metadata) == metadata_hash:
                return sample

        return None

    def validate_sample_metadata(
        self,
        mode: SampleMode,
        metadata: Dict[str, Any],
        image_width: Optional[int] = None,
        image_height: Optional[int] = None,
    ) -> bool:
        """验证不同模式的元数据格式"""

        def is_valid_coordinate(coord, allow_point: bool = False) -> bool:
            """验证坐标格式: [x1, y1, x2, y2] 或 [null, null, null, null]

            Args:
                coord: 坐标数组
                allow_point: 是否允许点坐标 (x1=x2, y1=y2)，用于describe模式
            """
            if not isinstance(coord, list) or len(coord) != 4:
                return False

            # 允许全为null的情况
            if all(x is None for x in coord):
                return True

            # 否则必须都是整数
            if not all(isinstance(x, int) for x in coord):
                return False

            x1, y1, x2, y2 = coord

            # 验证坐标顺序
            if allow_point:
                # describe模式允许点坐标 (x1=x2, y1=y2)
                if x1 > x2 or y1 > y2:
                    return False
            else:
                # 其他模式要求严格的矩形 (x1 < x2, y1 < y2)
                if x1 >= x2 or y1 >= y2:
                    return False

            # 如果提供了图像尺寸，验证坐标是否在有效范围内
            if image_width is not None and image_height is not None:
                if not (
                    0 <= x1 <= image_width
                    and 0 <= x2 <= image_width
                    and 0 <= y1 <= image_height
                    and 0 <= y2 <= image_height
                ):
                    return False

            return True

        try:
            if mode == SampleMode.GROUNDING:
                # Grounding模式: {description: str, coordinate: [x1,y1,x2,y2]}
                required_keys = {"description", "coordinate"}
                if not all(key in metadata for key in required_keys):
                    return False

                if not isinstance(metadata["description"], str):
                    return False

                return is_valid_coordinate(metadata["coordinate"])

            elif mode == SampleMode.DESCRIBE:
                # Describe模式: {coordinate: [x1,y1,x2,y2], description: str}
                # 注意：describe模式使用点坐标，允许 x1=x2, y1=y2
                required_keys = {"coordinate", "description"}
                if not all(key in metadata for key in required_keys):
                    return False

                if not isinstance(metadata["description"], str):
                    return False

                return is_valid_coordinate(metadata["coordinate"], allow_point=True)

            elif mode == SampleMode.ENUMERATE_TEXT:
                # Enumerate Text模式: {general_description: str, elements: [str, ...]}
                required_keys = {"general_description", "elements"}
                if not all(key in metadata for key in required_keys):
                    return False

                if not isinstance(metadata["general_description"], str):
                    return False

                if not isinstance(metadata["elements"], list):
                    return False

                return all(isinstance(elem, str) for elem in metadata["elements"])

            elif mode == SampleMode.ENUMERATE_COORD:
                # Enumerate Coord模式: {general_description: str, elements: [[x1,y1,x2,y2], ...]}
                required_keys = {"general_description", "elements"}
                if not all(key in metadata for key in required_keys):
                    return False

                if not isinstance(metadata["general_description"], str):
                    return False

                if not isinstance(metadata["elements"], list):
                    return False

                return all(is_valid_coordinate(elem) for elem in metadata["elements"])

            elif mode == SampleMode.CHECKLIST:
                # Checklist模式: {descriptions: [str, ...], results: [bool, ...]}
                required_keys = {"descriptions", "results"}
                if not all(key in metadata for key in required_keys):
                    return False

                descriptions = metadata["descriptions"]
                results = metadata["results"]

                if not isinstance(descriptions, list) or not isinstance(results, list):
                    return False

                if len(descriptions) != len(results):
                    return False

                if not all(isinstance(desc, str) for desc in descriptions):
                    return False

                return all(isinstance(result, bool) for result in results)

            elif mode == SampleMode.ENSURE:
                # Ensure模式: {expected_state: str, coordinate: [x1,y1,x2,y2]}
                required_keys = {"expected_state", "coordinate"}
                if not all(key in metadata for key in required_keys):
                    return False

                if not isinstance(metadata["expected_state"], str):
                    return False

                return is_valid_coordinate(metadata["coordinate"])

            else:
                return False

        except (KeyError, TypeError, ValueError):
            return False

    def create_sample(self, sample_data: SampleCreate) -> Sample:
        """
        创建新样本

        Args:
            sample_data: 样本创建数据

        Returns:
            Sample: 创建的样本记录

        Raises:
            ValueError: 如果数据无效或样本已存在
        """
        # 验证关联的图片是否存在
        image = self.db.query(Image).filter(Image.id == sample_data.image_id).first()
        if not image:
            raise ValueError(f"图片不存在: ID={sample_data.image_id}")

        # 验证关联的批次是否存在
        batch = self.db.query(Batch).filter(Batch.id == sample_data.batch_id).first()
        if not batch:
            raise ValueError(f"批次不存在: ID={sample_data.batch_id}")

        # 验证元数据格式
        if not self.validate_sample_metadata(
            sample_data.mode, sample_data.sample_metadata, image.width, image.height
        ):
            raise ValueError(f"元数据格式无效，模式: {sample_data.mode.value}")

        # 检查全局重复样本
        existing_sample = self.check_duplicate_sample(
            sample_data.image_id, sample_data.mode, sample_data.sample_metadata
        )
        if existing_sample:
            raise ValueError(
                f"样本已存在: 相同的图片(ID:{sample_data.image_id})、"
                f"模式({sample_data.mode.value})和元数据已在批次 "
                f"'{existing_sample.batch.name}'(ID:{existing_sample.batch_id}) 中存在 "
                f"(样本ID:{existing_sample.id})"
            )

        # 创建样本记录
        sample = Sample(
            image_id=sample_data.image_id,
            batch_id=sample_data.batch_id,
            mode=sample_data.mode,
            sample_metadata=sample_data.sample_metadata,
            labels=sample_data.labels or [],
        )

        self.db.add(sample)
        self.db.commit()
        self.db.refresh(sample)

        return sample

    def create_samples_batch(
        self,
        samples_data: List[SampleCreate],
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> Tuple[List[Sample], List[str]]:
        """
        批量创建样本，减少数据库提交次数

        Args:
            samples_data: 样本创建数据列表
            progress_callback: 进度回调函数，接收(current, total)参数

        Returns:
            Tuple: (成功创建的样本列表, 错误信息列表)
        """
        created_samples = []
        errors = []
        batch_size = 100  # 每100个样本提交一次

        # 预先验证所有关联对象
        image_ids = {sample.image_id for sample in samples_data}
        batch_ids = {sample.batch_id for sample in samples_data}

        # 批量查询图片和批次（分块处理以避免SQL变量限制）
        existing_images = {}
        existing_batches = {}

        # SQLite默认限制999个SQL变量，我们使用更保守的500
        chunk_size = 500

        # 分块查询图片
        image_id_list = list(image_ids)
        for i in range(0, len(image_id_list), chunk_size):
            chunk = image_id_list[i : i + chunk_size]
            chunk_images = self.db.query(Image).filter(Image.id.in_(chunk)).all()
            for img in chunk_images:
                existing_images[img.id] = img

        # 分块查询批次
        batch_id_list = list(batch_ids)
        for i in range(0, len(batch_id_list), chunk_size):
            chunk = batch_id_list[i : i + chunk_size]
            chunk_batches = self.db.query(Batch).filter(Batch.id.in_(chunk)).all()
            for batch in chunk_batches:
                existing_batches[batch.id] = batch

        for i, sample_data in enumerate(samples_data):
            try:
                # 验证关联的图片是否存在
                if sample_data.image_id not in existing_images:
                    errors.append(f"图片不存在: ID={sample_data.image_id}")
                    continue

                # 验证关联的批次是否存在
                if sample_data.batch_id not in existing_batches:
                    errors.append(f"批次不存在: ID={sample_data.batch_id}")
                    continue

                # 获取图片信息用于验证
                image = existing_images[sample_data.image_id]

                # 验证元数据格式
                if not self.validate_sample_metadata(
                    sample_data.mode,
                    sample_data.sample_metadata,
                    image.width,
                    image.height,
                ):
                    errors.append(f"元数据格式无效，模式: {sample_data.mode.value}")
                    continue

                # 检查全局重复样本
                existing_sample = self.check_duplicate_sample(
                    sample_data.image_id, sample_data.mode, sample_data.sample_metadata
                )
                if existing_sample:
                    errors.append(
                        f"样本已存在: 相同的图片(ID:{sample_data.image_id})、"
                        f"模式({sample_data.mode.value})和元数据已在批次 "
                        f"'{existing_sample.batch.name}'(ID:{existing_sample.batch_id}) 中存在 "
                        f"(样本ID:{existing_sample.id})"
                    )
                    continue

                # 创建样本记录（但不立即提交）
                sample = Sample(
                    image_id=sample_data.image_id,
                    batch_id=sample_data.batch_id,
                    mode=sample_data.mode,
                    sample_metadata=sample_data.sample_metadata,
                    labels=sample_data.labels or [],
                )

                self.db.add(sample)
                created_samples.append(sample)

                # 批量提交
                if len(created_samples) % batch_size == 0:
                    self.db.commit()
                    # 刷新已创建的样本
                    for s in created_samples[-batch_size:]:
                        self.db.refresh(s)

            except Exception as e:
                errors.append(f"创建样本失败: {str(e)}")

            # 更新进度
            if progress_callback:
                progress_callback(i + 1, len(samples_data))

        # 提交剩余的样本
        if created_samples:
            remaining = len(created_samples) % batch_size
            if remaining > 0:
                self.db.commit()
                # 刷新最后一批样本
                for s in created_samples[-remaining:]:
                    self.db.refresh(s)

        return created_samples, errors

    def create_samples_streaming(
        self,
        samples_data: list[SampleCreate],
        chunk_size: int = 1000,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> tuple[list[Sample], list[str]]:
        """
        流式批量创建样本，支持大规模数据集

        Args:
            samples_data: 样本创建数据列表
            chunk_size: 每个处理块的大小
            progress_callback: 进度回调函数，接收(current, total)参数

        Returns:
            Tuple[List[Sample], List[str]]: (成功创建的样本列表, 错误信息列表)
        """
        all_created_samples = []
        all_errors = []
        total_samples = len(samples_data)

        # 分块处理
        for i in range(0, total_samples, chunk_size):
            chunk = samples_data[i : i + chunk_size]

            # 处理当前块
            chunk_samples, chunk_errors = self._process_samples_chunk(chunk)

            all_created_samples.extend(chunk_samples)
            all_errors.extend(chunk_errors)

            # 更新进度
            if progress_callback:
                progress_callback(min(i + chunk_size, total_samples), total_samples)

        return all_created_samples, all_errors

    def _process_samples_chunk(
        self, chunk_data: list[SampleCreate]
    ) -> tuple[list[Sample], list[str]]:
        """
        处理单个样本块

        Args:
            chunk_data: 样本数据块

        Returns:
            Tuple[List[Sample], List[str]]: (创建的样本列表, 错误信息列表)
        """
        created_samples = []
        errors = []

        try:
            # 预先验证所有关联对象
            validation_result = self._validate_chunk_references(chunk_data)
            if not validation_result["valid"]:
                errors.extend(validation_result["errors"])
                return [], errors

            existing_images = validation_result["images"]
            existing_batches = validation_result["batches"]

            # 批量检查重复样本
            duplicate_check_result = self._check_chunk_duplicates(chunk_data)
            duplicate_samples = duplicate_check_result["duplicates"]

            # 创建样本对象
            sample_objects = []
            for i, sample_data in enumerate(chunk_data):
                # 跳过重复样本
                if i in duplicate_samples:
                    errors.append(
                        f"样本已存在: 图片ID {sample_data.image_id}, "
                        f"模式 {sample_data.mode.value}"
                    )
                    continue

                # 验证关联对象存在
                if sample_data.image_id not in existing_images:
                    errors.append(f"图片不存在: ID={sample_data.image_id}")
                    continue

                if sample_data.batch_id not in existing_batches:
                    errors.append(f"批次不存在: ID={sample_data.batch_id}")
                    continue

                # 验证元数据
                image = existing_images[sample_data.image_id]
                if not self.validate_sample_metadata(
                    sample_data.mode,
                    sample_data.sample_metadata,
                    image.width,
                    image.height,
                ):
                    errors.append(f"元数据格式无效，模式: {sample_data.mode.value}")
                    continue

                # 创建样本对象
                sample = Sample(
                    image_id=sample_data.image_id,
                    batch_id=sample_data.batch_id,
                    mode=sample_data.mode,
                    sample_metadata=sample_data.sample_metadata,
                    labels=sample_data.labels or [],
                )
                sample_objects.append(sample)

            # 批量添加到数据库
            if sample_objects:
                self.db.add_all(sample_objects)
                self.db.commit()

                # 刷新对象以获取ID
                for sample in sample_objects:
                    self.db.refresh(sample)

                created_samples = sample_objects

        except Exception as e:
            self.db.rollback()
            errors.append(f"处理样本块失败: {str(e)}")

        return created_samples, errors

    def _validate_chunk_references(self, chunk_data: list[SampleCreate]) -> dict:
        """
        验证块中所有样本的关联对象

        Args:
            chunk_data: 样本数据块

        Returns:
            Dict: 验证结果，包含 valid, images, batches, errors
        """
        # 收集所有需要验证的ID
        image_ids = {sample.image_id for sample in chunk_data}
        batch_ids = {sample.batch_id for sample in chunk_data}

        errors = []

        # 批量查询图片
        existing_images = {}
        if image_ids:
            # 分块查询以避免SQL变量限制
            chunk_size = 500
            image_id_list = list(image_ids)
            for i in range(0, len(image_id_list), chunk_size):
                chunk = image_id_list[i : i + chunk_size]
                chunk_images = self.db.query(Image).filter(Image.id.in_(chunk)).all()
                for img in chunk_images:
                    existing_images[img.id] = img

        # 批量查询批次
        existing_batches = {}
        if batch_ids:
            # 分块查询以避免SQL变量限制
            chunk_size = 500
            batch_id_list = list(batch_ids)
            for i in range(0, len(batch_id_list), chunk_size):
                chunk = batch_id_list[i : i + chunk_size]
                chunk_batches = self.db.query(Batch).filter(Batch.id.in_(chunk)).all()
                for batch in chunk_batches:
                    existing_batches[batch.id] = batch

        # 检查缺失的关联对象
        missing_images = image_ids - set(existing_images.keys())
        missing_batches = batch_ids - set(existing_batches.keys())

        if missing_images:
            errors.extend([f"图片不存在: ID={img_id}" for img_id in missing_images])

        if missing_batches:
            errors.extend([
                f"批次不存在: ID={batch_id}" for batch_id in missing_batches
            ])

        return {
            "valid": len(errors) == 0,
            "images": existing_images,
            "batches": existing_batches,
            "errors": errors,
        }

    def _check_chunk_duplicates(self, chunk_data: list[SampleCreate]) -> dict:
        """
        检查块中的重复样本

        Args:
            chunk_data: 样本数据块

        Returns:
            Dict: 包含 duplicates (重复样本的索引集合)
        """
        duplicate_indices = set()

        # 为了性能，我们只检查明显的重复（相同image_id + mode + metadata）
        seen_combinations = set()

        for i, sample_data in enumerate(chunk_data):
            # 创建唯一标识符
            metadata_str = str(sorted(sample_data.sample_metadata.items()))
            combination = (sample_data.image_id, sample_data.mode.value, metadata_str)

            if combination in seen_combinations:
                duplicate_indices.add(i)
            else:
                seen_combinations.add(combination)

        return {"duplicates": duplicate_indices}

    def update_sample(
        self, sample_id: int, update_data: SampleUpdate
    ) -> Optional[Sample]:
        """
        更新样本

        Args:
            sample_id: 样本ID
            update_data: 更新数据

        Returns:
            Sample: 更新后的样本记录，如果不存在返回None
        """
        sample = self.db.query(Sample).filter(Sample.id == sample_id).first()
        if not sample:
            return None

        # 更新元数据
        if update_data.sample_metadata is not None:
            # 验证新的元数据格式
            if not self.validate_sample_metadata(
                sample.mode,
                update_data.sample_metadata,
                sample.image.width,
                sample.image.height,
            ):
                raise ValueError(f"元数据格式无效，模式: {sample.mode.value}")
            sample.sample_metadata = update_data.sample_metadata

        # 更新标签
        if update_data.labels is not None:
            sample.labels = update_data.labels

        self.db.commit()
        self.db.refresh(sample)

        return sample

    def update_sample_labels(
        self, sample_id: int, labels: List[str]
    ) -> Optional[Sample]:
        """更新样本标签"""
        sample = self.db.query(Sample).filter(Sample.id == sample_id).first()
        if not sample:
            return None

        sample.labels = labels
        self.db.commit()
        self.db.refresh(sample)

        return sample

    def get_sample_by_id(self, sample_id: int) -> Optional[Sample]:
        """根据ID获取样本"""
        return self.db.query(Sample).filter(Sample.id == sample_id).first()

    def get_samples_with_filters(
        self,
        batch_id: Optional[int] = None,
        batch_ids: Optional[List[int]] = None,
        mode: Optional[SampleMode] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        limit: Optional[int] = 100,
        offset: int = 0,
    ) -> List[Sample]:
        """
        根据过滤条件获取样本列表

        Args:
            batch_id: 单个批次ID过滤（向后兼容）
            batch_ids: 多个批次ID过滤
            mode: 单个模式过滤（向后兼容）
            modes: 多个模式过滤
            labels: 标签过滤（包含任一标签的样本）
            limit: 返回数量限制，None表示无限制
            offset: 偏移量

        Returns:
            List[Sample]: 符合条件的样本列表
        """
        query = self.db.query(Sample)

        # 批次过滤 - 支持单个和多个，处理大列表
        if batch_ids is not None:
            if len(batch_ids) > 500:  # 如果列表很大，使用分块查询
                batch_conditions = []
                chunk_size = 500
                for i in range(0, len(batch_ids), chunk_size):
                    chunk = batch_ids[i : i + chunk_size]
                    batch_conditions.append(Sample.batch_id.in_(chunk))
                query = query.filter(or_(*batch_conditions))
            else:
                query = query.filter(Sample.batch_id.in_(batch_ids))
        elif batch_id is not None:
            query = query.filter(Sample.batch_id == batch_id)

        # 模式过滤 - 支持单个和多个，处理大列表
        if modes is not None:
            if len(modes) > 500:  # 如果列表很大，使用分块查询
                mode_conditions = []
                chunk_size = 500
                for i in range(0, len(modes), chunk_size):
                    chunk = modes[i : i + chunk_size]
                    mode_conditions.append(Sample.mode.in_(chunk))
                query = query.filter(or_(*mode_conditions))
            else:
                query = query.filter(Sample.mode.in_(modes))
        elif mode is not None:
            query = query.filter(Sample.mode == mode)

        # 标签过滤 - 使用JSON查询（SQLite支持有限，这里用简单的包含检查）
        if labels:
            # 对于SQLite，我们需要用字符串包含检查
            # 在生产环境中，建议使用PostgreSQL的JSON操作符
            label_conditions = []
            for label in labels:
                # 检查JSON数组中是否包含特定标签
                label_conditions.append(Sample.labels.contains([label]))
            query = query.filter(or_(*label_conditions))

        # 排序、分页
        query = query.order_by(Sample.created_at.desc())
        query = query.offset(offset)

        # 只有当limit不为None时才应用限制
        if limit is not None:
            query = query.limit(limit)

        return query.all()

    def get_samples_by_image(self, image_id: int) -> List[Sample]:
        """获取指定图片的所有样本"""
        return self.db.query(Sample).filter(Sample.image_id == image_id).all()

    def get_samples_by_batch(self, batch_id: int) -> List[Sample]:
        """获取指定批次的所有样本"""
        return self.db.query(Sample).filter(Sample.batch_id == batch_id).all()

    def delete_sample(self, sample_id: int) -> bool:
        """删除样本"""
        sample = self.db.query(Sample).filter(Sample.id == sample_id).first()
        if not sample:
            return False

        self.db.delete(sample)
        self.db.commit()
        return True

    def count_samples_with_filters(
        self,
        batch_id: Optional[int] = None,
        batch_ids: Optional[List[int]] = None,
        mode: Optional[SampleMode] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
    ) -> int:
        """统计符合条件的样本数量"""
        query = self.db.query(Sample)

        # 批次过滤 - 支持单个和多个，处理大列表
        if batch_ids is not None:
            if len(batch_ids) > 500:  # 如果列表很大，使用分块查询
                batch_conditions = []
                chunk_size = 500
                for i in range(0, len(batch_ids), chunk_size):
                    chunk = batch_ids[i : i + chunk_size]
                    batch_conditions.append(Sample.batch_id.in_(chunk))
                query = query.filter(or_(*batch_conditions))
            else:
                query = query.filter(Sample.batch_id.in_(batch_ids))
        elif batch_id is not None:
            query = query.filter(Sample.batch_id == batch_id)

        # 模式过滤 - 支持单个和多个，处理大列表
        if modes is not None:
            if len(modes) > 500:  # 如果列表很大，使用分块查询
                mode_conditions = []
                chunk_size = 500
                for i in range(0, len(modes), chunk_size):
                    chunk = modes[i : i + chunk_size]
                    mode_conditions.append(Sample.mode.in_(chunk))
                query = query.filter(or_(*mode_conditions))
            else:
                query = query.filter(Sample.mode.in_(modes))
        elif mode is not None:
            query = query.filter(Sample.mode == mode)

        if labels:
            label_conditions = []
            for label in labels:
                label_conditions.append(Sample.labels.contains([label]))
            query = query.filter(or_(*label_conditions))

        return query.count()

    def get_system_statistics(self) -> Dict[str, Any]:
        """
        获取系统级统计信息，专门为stats overview命令优化

        Returns:
            Dict: 包含各种统计信息的字典
        """
        # 总样本数
        total_samples = self.db.query(func.count(Sample.id)).scalar()

        # 唯一图片数
        unique_images = self.db.query(func.count(distinct(Sample.image_id))).scalar()

        # 模式分布 - 使用数据库聚合
        mode_distribution = {}
        mode_results = (
            self.db.query(Sample.mode, func.count(Sample.id))
            .group_by(Sample.mode)
            .all()
        )
        for mode, count in mode_results:
            mode_distribution[mode.value] = count

        return {
            "total_samples": total_samples,
            "unique_images": unique_images,
            "mode_distribution": mode_distribution,
        }

    def get_all_labels_efficiently(self, batch_size: int = 5000) -> List[str]:
        """
        高效获取所有标签，分批处理避免内存问题

        Args:
            batch_size: 每批处理的样本数量

        Returns:
            List[str]: 所有标签列表
        """
        all_labels = []
        offset = 0

        while True:
            # 分批获取样本
            samples = (
                self.db.query(Sample.labels).offset(offset).limit(batch_size).all()
            )

            if not samples:
                break

            # 提取标签
            for (labels,) in samples:
                if labels:
                    all_labels.extend(labels)

            offset += batch_size

        return all_labels
