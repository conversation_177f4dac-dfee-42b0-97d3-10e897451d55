#!/usr/bin/env python3
"""
Version Synchronization Script for Ruyi Dataverse

This script synchronizes version numbers across all components:
- Reads version from pyproject.toml (single source of truth)
- Updates frontend/package.json to match
- Validates that all components use the same version

Usage:
    python scripts/sync_version.py [--check] [--update-frontend]

Options:
    --check: Only check if versions are synchronized (exit code 1 if not)
    --update-frontend: Update frontend package.json to match backend version
"""

import argparse
import json
import sys
from pathlib import Path

try:
    import tomllib
except ImportError:
    # Python < 3.11 fallback
    try:
        import tomli as tomllib
    except ImportError:
        print(
            "Error: tomllib/tomli not available. Please install tomli for Python < 3.11"
        )
        sys.exit(1)


def get_project_root() -> Path:
    """Get the project root directory"""
    script_dir = Path(__file__).parent
    return script_dir.parent


def read_backend_version() -> str:
    """Read version from pyproject.toml"""
    project_root = get_project_root()
    pyproject_path = project_root / "pyproject.toml"

    if not pyproject_path.exists():
        raise FileNotFoundError(f"pyproject.toml not found at {pyproject_path}")

    with open(pyproject_path, "rb") as f:
        data = tomllib.load(f)

    try:
        return data["project"]["version"]
    except KeyError:
        raise ValueError("Version not found in pyproject.toml under [project.version]")


def read_frontend_version() -> str:
    """Read version from frontend/package.json"""
    project_root = get_project_root()
    package_json_path = project_root / "frontend" / "package.json"

    if not package_json_path.exists():
        raise FileNotFoundError(f"package.json not found at {package_json_path}")

    with open(package_json_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    try:
        return data["version"]
    except KeyError:
        raise ValueError("Version not found in frontend/package.json")


def update_frontend_version(new_version: str) -> None:
    """Update version in frontend/package.json"""
    project_root = get_project_root()
    package_json_path = project_root / "frontend" / "package.json"

    if not package_json_path.exists():
        raise FileNotFoundError(f"package.json not found at {package_json_path}")

    with open(package_json_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    old_version = data.get("version", "unknown")
    data["version"] = new_version

    with open(package_json_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
        f.write("\n")  # Add trailing newline

    print(f"✅ Updated frontend version: {old_version} → {new_version}")


def check_version_sync() -> tuple[str, str, bool]:
    """Check if versions are synchronized"""
    try:
        backend_version = read_backend_version()
        frontend_version = read_frontend_version()
        is_synced = backend_version == frontend_version
        return backend_version, frontend_version, is_synced
    except Exception as e:
        print(f"❌ Error checking versions: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Synchronize version numbers across Ruyi Dataverse components"
    )
    parser.add_argument(
        "--check",
        action="store_true",
        help="Only check if versions are synchronized (exit with code 1 if not)",
    )
    parser.add_argument(
        "--update-frontend",
        action="store_true",
        help="Update frontend package.json to match backend version",
    )

    args = parser.parse_args()

    # If no specific action is requested, default to checking
    if not args.check and not args.update_frontend:
        args.check = True

    backend_version, frontend_version, is_synced = check_version_sync()

    print(f"📦 Backend version (pyproject.toml): {backend_version}")
    print(f"🌐 Frontend version (package.json): {frontend_version}")

    if args.check:
        if is_synced:
            print("✅ All versions are synchronized!")
            sys.exit(0)
        else:
            print("❌ Versions are not synchronized!")
            print(f"   Backend: {backend_version}")
            print(f"   Frontend: {frontend_version}")
            print("\nRun with --update-frontend to synchronize versions.")
            sys.exit(1)

    if args.update_frontend:
        if is_synced:
            print("✅ Versions are already synchronized, no update needed.")
        else:
            update_frontend_version(backend_version)
            print("✅ Frontend version synchronized with backend!")


if __name__ == "__main__":
    main()
