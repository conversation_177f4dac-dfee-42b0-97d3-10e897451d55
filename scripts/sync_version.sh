#!/bin/bash
# Version Synchronization Script for Ruyi Dataverse
# This script provides convenient commands for version management

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PYTHON_SCRIPT="$SCRIPT_DIR/sync_version.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  check           Check if all versions are synchronized"
    echo "  sync            Synchronize frontend version with backend"
    echo "  update VERSION  Update version in pyproject.toml and sync frontend"
    echo "  show            Show current versions"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 check                    # Check version synchronization"
    echo "  $0 sync                     # Sync frontend with backend"
    echo "  $0 update 0.3.0             # Update to version 0.3.0"
    echo "  $0 show                     # Show current versions"
}

check_versions() {
    echo -e "${BLUE}🔍 Checking version synchronization...${NC}"
    python3 "$PYTHON_SCRIPT" --check
}

sync_versions() {
    echo -e "${BLUE}🔄 Synchronizing versions...${NC}"
    python3 "$PYTHON_SCRIPT" --update-frontend
}

show_versions() {
    echo -e "${BLUE}📋 Current versions:${NC}"
    python3 "$PYTHON_SCRIPT" --check || true
}

update_version() {
    local new_version="$1"
    
    if [[ -z "$new_version" ]]; then
        echo -e "${RED}❌ Error: Version number required${NC}"
        echo "Usage: $0 update VERSION"
        exit 1
    fi
    
    # Validate version format (basic semver check)
    if ! [[ "$new_version" =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$ ]]; then
        echo -e "${YELLOW}⚠️  Warning: Version '$new_version' doesn't follow semantic versioning format${NC}"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Aborted."
            exit 1
        fi
    fi
    
    echo -e "${BLUE}📝 Updating version to $new_version...${NC}"
    
    # Update pyproject.toml
    if command -v sed >/dev/null 2>&1; then
        sed -i.bak "s/^version = \".*\"/version = \"$new_version\"/" "$PROJECT_ROOT/pyproject.toml"
        rm -f "$PROJECT_ROOT/pyproject.toml.bak"
        echo -e "${GREEN}✅ Updated pyproject.toml${NC}"
    else
        echo -e "${RED}❌ Error: sed command not found${NC}"
        exit 1
    fi
    
    # Sync frontend
    sync_versions
    
    echo -e "${GREEN}🎉 Version updated successfully to $new_version!${NC}"
    echo ""
    echo -e "${YELLOW}📝 Next steps:${NC}"
    echo "  1. Review the changes: git diff"
    echo "  2. Test the application: uv run ruyidv --version"
    echo "  3. Commit the changes: git add . && git commit -m 'Bump version to $new_version'"
    echo "  4. Create a tag: git tag v$new_version"
}

# Main command handling
case "${1:-help}" in
    check)
        check_versions
        ;;
    sync)
        sync_versions
        ;;
    update)
        update_version "$2"
        ;;
    show)
        show_versions
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        usage
        exit 1
        ;;
esac
